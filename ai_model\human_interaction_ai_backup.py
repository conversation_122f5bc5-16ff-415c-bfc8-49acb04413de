"""
Advanced Human Interaction AI Model

This module implements a sophisticated AI model capable of learning and replicating
human-like browser interactions from recorded interaction data. The model uses
deep learning techniques to understand and generate realistic mouse movements,
typing patterns, and scrolling behaviors.

Key Features:
- Multi-modal learning (mouse, keyboard, scroll)
- Temporal pattern recognition
- Error simulation and correction patterns
- Anti-detection capabilities
- Adaptive behavior based on context
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import json
import os
import math
import random
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from collections import defaultdict
import logging
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class InteractionConfig:
    """Configuration for interaction generation"""
    # Mouse settings
    mouse_smoothness: float = 0.8  # 0-1, higher = smoother
    mouse_speed_variance: float = 0.3  # Speed variation
    mouse_error_rate: float = 0.05  # Probability of slight overshoot
    
    # Typing settings
    typing_speed_wpm: float = 65.0  # Words per minute
    typing_error_rate: float = 0.02  # Probability of typos
    typing_correction_delay: float = 0.5  # Seconds before correction
    
    # Scrolling settings
    scroll_smoothness: float = 0.7  # Scroll smoothness
    scroll_overshoot_rate: float = 0.1  # Probability of overshooting
    
    # Timing settings
    reaction_time_base: float = 0.2  # Base reaction time in seconds
    reaction_time_variance: float = 0.1  # Reaction time variation
    
    # Anti-detection settings
    add_micro_movements: bool = True  # Add subtle micro-movements
    randomize_timing: bool = True  # Add timing variations
    simulate_fatigue: bool = True  # Simulate user fatigue over time

class FeatureExtractor:
    """Extracts features from raw interaction data for training"""
    
    def __init__(self):
        self.mouse_scaler = StandardScaler()
        self.typing_scaler = StandardScaler()
        self.scroll_scaler = StandardScaler()
        
    def extract_mouse_features(self, events: List[Dict]) -> np.ndarray:
        """Extract features from mouse movement events"""
        features = []
        
        for i in range(1, len(events)):
            prev_event = events[i-1]
            curr_event = events[i]
            
            # Basic movement features
            dx = curr_event['x'] - prev_event['x']
            dy = curr_event['y'] - prev_event['y']
            distance = math.sqrt(dx**2 + dy**2)
            
            # Temporal features
            time_delta = curr_event['timestamp'] - prev_event['timestamp']
            velocity = distance / max(time_delta, 1e-6)
            
            # Direction features
            angle = math.atan2(dy, dx) if distance > 0 else 0
            
            # Acceleration (if we have previous velocity)
            if i > 1:
                prev_dx = prev_event['x'] - events[i-2]['x']
                prev_dy = prev_event['y'] - events[i-2]['y']
                prev_distance = math.sqrt(prev_dx**2 + prev_dy**2)
                prev_time_delta = prev_event['timestamp'] - events[i-2]['timestamp']
                prev_velocity = prev_distance / max(prev_time_delta, 1e-6)
                acceleration = (velocity - prev_velocity) / max(time_delta, 1e-6)
            else:
                acceleration = 0
            
            # Curvature (change in direction)
            if i > 1:
                prev_angle = math.atan2(prev_dy, prev_dx) if prev_distance > 0 else 0
                curvature = abs(angle - prev_angle)
                if curvature > math.pi:
                    curvature = 2 * math.pi - curvature
            else:
                curvature = 0
            
            features.append([
                curr_event['x'], curr_event['y'],  # Position
                dx, dy,  # Delta movement
                distance, velocity, acceleration,  # Movement metrics
                angle, curvature,  # Direction metrics
                time_delta,  # Timing
                curr_event['timestamp']  # Absolute time
            ])
        
        return np.array(features)
    
    def extract_typing_features(self, events: List[Dict]) -> np.ndarray:
        """Extract features from typing events"""
        features = []
        
        for i, event in enumerate(events):
            # Basic event features
            event_type = 1 if event['type'] == 'keydown' else 0
            key_code = hash(event.get('code', '')) % 1000  # Simple key encoding
            
            # Timing features
            if i > 0:
                time_since_last = event['timestamp'] - events[i-1]['timestamp']
            else:
                time_since_last = 0
            
            # Error detection features
            is_correct = 1 if event.get('correct', True) else 0
            error_type = 0  # Default no error
            if event.get('error_type'):
                error_map = {'typo': 1, 'extra_char': 2, 'missing_char': 3}
                error_type = error_map.get(event['error_type'], 0)
            
            # Text length and position
            text_length = len(event.get('current_text', ''))
            
            features.append([
                event_type, key_code,  # Event basics
                time_since_last,  # Timing
                is_correct, error_type,  # Error features
                text_length,  # Context
                event['timestamp']  # Absolute time
            ])
        
        return np.array(features)
    
    def extract_scroll_features(self, events: List[Dict]) -> np.ndarray:
        """Extract features from scrolling events"""
        features = []
        
        for i, event in enumerate(events):
            # Basic scroll features
            scroll_y = event.get('scroll_y', 0)
            scroll_delta = event.get('scroll_delta_y', 0)
            
            # Event type
            is_scroll_stop = 1 if event['type'] == 'scroll_stop' else 0
            is_point_reached = 1 if event['type'] == 'point_reached' else 0
            
            # Timing features
            if i > 0:
                time_since_last = event['timestamp'] - events[i-1]['timestamp']
            else:
                time_since_last = 0
            
            # Velocity and acceleration
            if i > 0 and time_since_last > 0:
                velocity = abs(scroll_delta) / time_since_last
            else:
                velocity = 0
            
            if i > 1:
                prev_delta = events[i-1].get('scroll_delta_y', 0)
                prev_time = events[i-1]['timestamp'] - events[i-2]['timestamp']
                prev_velocity = abs(prev_delta) / max(prev_time, 1e-6)
                acceleration = (velocity - prev_velocity) / max(time_since_last, 1e-6)
            else:
                acceleration = 0
            
            features.append([
                scroll_y, scroll_delta,  # Position and movement
                velocity, acceleration,  # Movement metrics
                is_scroll_stop, is_point_reached,  # Event types
                time_since_last,  # Timing
                event['timestamp']  # Absolute time
            ])
        
        return np.array(features)

class MouseMovementLSTM(nn.Module):
    """LSTM network for learning mouse movement patterns"""
    
    def __init__(self, input_size=11, hidden_size=128, num_layers=2, output_size=2):
        super(MouseMovementLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True, dropout=0.2)
        self.attention = nn.MultiheadAttention(hidden_size, num_heads=8, dropout=0.1)
        self.fc1 = nn.Linear(hidden_size, 64)
        self.fc2 = nn.Linear(64, 32)
        self.fc3 = nn.Linear(32, output_size)  # x, y coordinates
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, x):
        # LSTM processing
        lstm_out, _ = self.lstm(x)
        
        # Attention mechanism
        lstm_out = lstm_out.transpose(0, 1)  # (seq_len, batch, hidden_size)
        attn_out, _ = self.attention(lstm_out, lstm_out, lstm_out)
        attn_out = attn_out.transpose(0, 1)  # Back to (batch, seq_len, hidden_size)
        
        # Take the last output
        out = attn_out[:, -1, :]
        
        # Fully connected layers
        out = F.relu(self.fc1(out))
        out = self.dropout(out)
        out = F.relu(self.fc2(out))
        out = self.dropout(out)
        out = self.fc3(out)
        
        return out

class TypingPatternLSTM(nn.Module):
    """LSTM network for learning typing patterns"""
    
    def __init__(self, input_size=7, hidden_size=96, num_layers=2, output_size=3):
        super(TypingPatternLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True, dropout=0.2)
        self.fc1 = nn.Linear(hidden_size, 48)
        self.fc2 = nn.Linear(48, 24)
        self.fc3 = nn.Linear(24, output_size)  # next_key_time, error_probability, correction_time
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        out = lstm_out[:, -1, :]  # Take last output
        
        out = F.relu(self.fc1(out))
        out = self.dropout(out)
        out = F.relu(self.fc2(out))
        out = self.dropout(out)
        out = self.fc3(out)
        
        return out

class ScrollingPatternLSTM(nn.Module):
    """LSTM network for learning scrolling patterns"""
    
    def __init__(self, input_size=8, hidden_size=96, num_layers=2, output_size=2):
        super(ScrollingPatternLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True, dropout=0.2)
        self.fc1 = nn.Linear(hidden_size, 48)
        self.fc2 = nn.Linear(48, 24)
        self.fc3 = nn.Linear(24, output_size)  # scroll_delta, timing
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, x):
        lstm_out, _ = self.lstm(x)
        out = lstm_out[:, -1, :]
        
        out = F.relu(self.fc1(out))
        out = self.dropout(out)
        out = F.relu(self.fc2(out))
        out = self.dropout(out)
        out = self.fc3(out)
        
        return out

class HumanInteractionAI:
    """Main AI model for human-like interaction generation"""
    
    def __init__(self, config: InteractionConfig = None):
        self.config = config or InteractionConfig()
        self.feature_extractor = FeatureExtractor()
        
        # Initialize models
        self.mouse_model = MouseMovementLSTM()
        self.typing_model = TypingPatternLSTM()
        self.scrolling_model = ScrollingPatternLSTM()
        
        # Training data storage
        self.mouse_data = []
        self.typing_data = []
        self.scrolling_data = []
        
        # Device configuration
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.mouse_model.to(self.device)
        self.typing_model.to(self.device)
        self.scrolling_model.to(self.device)
        
        logger.info(f"Initialized HumanInteractionAI on device: {self.device}")
    
    def load_training_data(self, data_directory: str = 'data/'):
        """Load and preprocess training data from JSON files"""
        logger.info(f"Loading training data from {data_directory}")
        
        if not os.path.exists(data_directory):
            logger.error(f"Data directory {data_directory} does not exist")
            return
        
        for filename in os.listdir(data_directory):
            if filename.endswith('.json'):
                filepath = os.path.join(data_directory, filename)
                try:
                    with open(filepath, 'r') as f:
                        data = json.load(f)
                        self._process_interaction_data(data)
                except Exception as e:
                    logger.error(f"Error loading {filepath}: {e}")
        
        logger.info(f"Loaded {len(self.mouse_data)} mouse sequences, "
                   f"{len(self.typing_data)} typing sequences, "
                   f"{len(self.scrolling_data)} scrolling sequences")
    
    def _process_interaction_data(self, data: Dict):
        """Process a single interaction data file"""
        test_type = data.get('test_type', '')
        events = data.get('events', [])
        
        if not events:
            return
        
        if test_type == 'mouse_movement':
            mouse_events = [e for e in events if e['type'] == 'mousemove']
            if len(mouse_events) > 10:  # Minimum sequence length
                features = self.feature_extractor.extract_mouse_features(mouse_events)
                self.mouse_data.append(features)
        
        elif test_type == 'typing':
            typing_events = [e for e in events if e['type'] in ['keydown', 'keyup', 'input_change']]
            if len(typing_events) > 5:
                features = self.feature_extractor.extract_typing_features(typing_events)
                self.typing_data.append(features)
        
        elif test_type == 'scrolling':
            scroll_events = [e for e in events if e['type'] in ['scroll', 'scroll_stop', 'point_reached']]
            if len(scroll_events) > 3:
                features = self.feature_extractor.extract_scroll_features(scroll_events)
                self.scrolling_data.append(features)
    
    def train_models(self, epochs: int = 100, batch_size: int = 32, learning_rate: float = 0.001):
        """Train all models on the loaded data"""
        logger.info("Starting model training...")
        
        # Train mouse model
        if self.mouse_data:
            logger.info("Training mouse movement model...")
            self._train_mouse_model(epochs, batch_size, learning_rate)
        
        # Train typing model
        if self.typing_data:
            logger.info("Training typing pattern model...")
            self._train_typing_model(epochs, batch_size, learning_rate)
        
        # Train scrolling model
        if self.scrolling_data:
            logger.info("Training scrolling pattern model...")
            self._train_scrolling_model(epochs, batch_size, learning_rate)
        
        logger.info("Training completed!")
    
    def _train_mouse_model(self, epochs: int, batch_size: int, learning_rate: float):
        """Train the mouse movement model with optimized GPU utilization"""
        # Prepare sequences
        sequences = []
        targets = []
        
        for data in self.mouse_data:
            if len(data) < 10:
                continue
            
            # Create sliding windows
            window_size = 10
            for i in range(len(data) - window_size):
                seq = data[i:i+window_size]
                target = data[i+window_size][:2]  # x, y coordinates
                sequences.append(seq)
                targets.append(target)
        
        if not sequences:
            logger.warning("No mouse training sequences available")
            return
        
        # Convert to numpy arrays first for efficient processing
        sequences_np = np.array(sequences, dtype=np.float32)
        targets_np = np.array(targets, dtype=np.float32)
        
        # Split data on CPU to avoid GPU memory issues
        X_train_np, X_val_np, y_train_np, y_val_np = train_test_split(
            sequences_np, targets_np, test_size=0.2, random_state=42
        )
        
        # Convert to tensors and move to GPU once
        X_train = torch.from_numpy(X_train_np).to(self.device, non_blocking=True)
        X_val = torch.from_numpy(X_val_np).to(self.device, non_blocking=True)
        y_train = torch.from_numpy(y_train_np).to(self.device, non_blocking=True)
        y_val = torch.from_numpy(y_val_np).to(self.device, non_blocking=True)
        
        # Optimize batch size for GPU utilization
        optimal_batch_size = min(max(batch_size, 128), len(X_train) // 4)
        logger.info(f"Using optimized batch size: {optimal_batch_size}")
        
        # Training setup with optimized parameters
        optimizer = torch.optim.AdamW(self.mouse_model.parameters(), lr=learning_rate, weight_decay=1e-4)
        criterion = nn.MSELoss()
        scheduler = torch.optim.lr_scheduler.OneCycleLR(
            optimizer, max_lr=learning_rate*10, epochs=epochs, 
            steps_per_epoch=len(X_train)//optimal_batch_size + 1
        )
        
        # Enable mixed precision training for better GPU utilization
        scaler = torch.cuda.amp.GradScaler() if self.device.type == 'cuda' else None
        
        # Training loop with optimizations
        best_val_loss = float('inf')
        for epoch in range(epochs):
            self.mouse_model.train()
            total_loss = 0
            num_batches = 0
            
            # Shuffle indices for better training
            indices = torch.randperm(len(X_train), device=self.device)
            
            # Mini-batch training with optimizations
            for i in range(0, len(X_train), optimal_batch_size):
                batch_indices = indices[i:i+optimal_batch_size]
                batch_X = X_train[batch_indices]
                batch_y = y_train[batch_indices]
                
                optimizer.zero_grad()
                
                # Use mixed precision if available
                if scaler is not None:
                    with torch.cuda.amp.autocast():
                        outputs = self.mouse_model(batch_X)
                        loss = criterion(outputs, batch_y)
                    
                    scaler.scale(loss).backward()
                    scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(self.mouse_model.parameters(), max_norm=1.0)
                    scaler.step(optimizer)
                    scaler.update()
                else:
                    outputs = self.mouse_model(batch_X)
                    loss = criterion(outputs, batch_y)
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.mouse_model.parameters(), max_norm=1.0)
                    optimizer.step()
                
                scheduler.step()
                total_loss += loss.item()
                num_batches += 1
            
            # Validation with optimized batch processing
            self.mouse_model.eval()
            val_loss = 0
            val_batches = 0
            with torch.no_grad():
                for i in range(0, len(X_val), optimal_batch_size):
                    batch_X_val = X_val[i:i+optimal_batch_size]
                    batch_y_val = y_val[i:i+optimal_batch_size]
                    
                    if scaler is not None:
                        with torch.cuda.amp.autocast():
                            val_outputs = self.mouse_model(batch_X_val)
                            batch_val_loss = criterion(val_outputs, batch_y_val)
                    else:
                        val_outputs = self.mouse_model(batch_X_val)
                        batch_val_loss = criterion(val_outputs, batch_y_val)
                    
                    val_loss += batch_val_loss.item()
                    val_batches += 1
            
            avg_val_loss = val_loss / val_batches if val_batches > 0 else float('inf')
            
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                torch.save(self.mouse_model.state_dict(), 'mouse_model_best.pth')
            
            if epoch % 5 == 0:  # More frequent logging
                avg_train_loss = total_loss / num_batches if num_batches > 0 else 0
                logger.info(f"Mouse Model - Epoch {epoch}: Train Loss: {avg_train_loss:.6f}, "
                           f"Val Loss: {avg_val_loss:.6f}, LR: {scheduler.get_last_lr()[0]:.6f}")
    
    def _train_typing_model(self, epochs: int, batch_size: int, learning_rate: float):
        """Train the typing pattern model with optimized GPU utilization"""
        sequences = []
        targets = []
        
        for data in self.typing_data:
            if len(data) < 5:
                continue
            
            window_size = 5
            for i in range(len(data) - window_size):
                seq = data[i:i+window_size]
                # Target: next keystroke timing, error probability, correction time
                next_event = data[i+window_size]
                target = [next_event[2], next_event[3], next_event[4]]  # time, is_correct, error_type
                sequences.append(seq)
                targets.append(target)
        
        if not sequences:
            logger.warning("No typing training sequences available")
            return
        
        # Convert to numpy arrays first for efficient processing
        sequences_np = np.array(sequences, dtype=np.float32)
        targets_np = np.array(targets, dtype=np.float32)
        
        # Split data on CPU to avoid GPU memory issues
        X_train_np, X_val_np, y_train_np, y_val_np = train_test_split(
            sequences_np, targets_np, test_size=0.2, random_state=42
        )
        
        # Convert to tensors and move to GPU once
        X_train = torch.from_numpy(X_train_np).to(self.device, non_blocking=True)
        X_val = torch.from_numpy(X_val_np).to(self.device, non_blocking=True)
        y_train = torch.from_numpy(y_train_np).to(self.device, non_blocking=True)
        y_val = torch.from_numpy(y_val_np).to(self.device, non_blocking=True)
        
        # Optimize batch size for GPU utilization
        optimal_batch_size = min(max(batch_size, 128), len(X_train) // 4)
        logger.info(f"Using optimized batch size for typing: {optimal_batch_size}")
        
        # Training setup with optimized parameters
        optimizer = torch.optim.AdamW(self.typing_model.parameters(), lr=learning_rate, weight_decay=1e-4)
        criterion = nn.MSELoss()
        scheduler = torch.optim.lr_scheduler.OneCycleLR(
            optimizer, max_lr=learning_rate*10, epochs=epochs, 
            steps_per_epoch=len(X_train)//optimal_batch_size + 1
        )
        
        # Enable mixed precision training for better GPU utilization
        scaler = torch.cuda.amp.GradScaler() if self.device.type == 'cuda' else None
        
        best_val_loss = float('inf')
        for epoch in range(epochs):
            self.typing_model.train()
            total_loss = 0
            num_batches = 0
            
            # Shuffle indices for better training
            indices = torch.randperm(len(X_train), device=self.device)
            
            for i in range(0, len(X_train), optimal_batch_size):
                batch_indices = indices[i:i+optimal_batch_size]
                batch_X = X_train[batch_indices]
                batch_y = y_train[batch_indices]
                
                optimizer.zero_grad()
                
                # Use mixed precision if available
                if scaler is not None:
                    with torch.cuda.amp.autocast():
                        outputs = self.typing_model(batch_X)
                        loss = criterion(outputs, batch_y)
                    
                    scaler.scale(loss).backward()
                    scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(self.typing_model.parameters(), max_norm=1.0)
                    scaler.step(optimizer)
                    scaler.update()
                else:
                    outputs = self.typing_model(batch_X)
                    loss = criterion(outputs, batch_y)
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.typing_model.parameters(), max_norm=1.0)
                    optimizer.step()
                
                scheduler.step()
                total_loss += loss.item()
                num_batches += 1
            
            # Validation with optimized batch processing
            self.typing_model.eval()
            val_loss = 0
            val_batches = 0
            with torch.no_grad():
                for i in range(0, len(X_val), optimal_batch_size):
                    batch_X_val = X_val[i:i+optimal_batch_size]
                    batch_y_val = y_val[i:i+optimal_batch_size]
                    
                    if scaler is not None:
                        with torch.cuda.amp.autocast():
                            val_outputs = self.typing_model(batch_X_val)
                            batch_val_loss = criterion(val_outputs, batch_y_val)
                    else:
                        val_outputs = self.typing_model(batch_X_val)
                        batch_val_loss = criterion(val_outputs, batch_y_val)
                    
                    val_loss += batch_val_loss.item()
                    val_batches += 1
            
            avg_val_loss = val_loss / val_batches if val_batches > 0 else float('inf')
            
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                torch.save(self.typing_model.state_dict(), 'typing_model_best.pth')
            
            if epoch % 5 == 0:  # More frequent logging
                avg_train_loss = total_loss / num_batches if num_batches > 0 else 0
                logger.info(f"Typing Model - Epoch {epoch}: Train Loss: {avg_train_loss:.6f}, "
                           f"Val Loss: {avg_val_loss:.6f}, LR: {scheduler.get_last_lr()[0]:.6f}")
    
    def _train_scrolling_model(self, epochs: int, batch_size: int, learning_rate: float):
        """Train the scrolling pattern model with optimized GPU utilization"""
        sequences = []
        targets = []
        
        for data in self.scrolling_data:
            if len(data) < 3:
                continue
            
            window_size = 3
            for i in range(len(data) - window_size):
                seq = data[i:i+window_size]
                next_event = data[i+window_size]
                target = [next_event[1], next_event[6]]  # scroll_delta, timing
                sequences.append(seq)
                targets.append(target)
        
        if not sequences:
            logger.warning("No scrolling training sequences available")
            return
        
        # Convert to numpy arrays first for efficient processing
        sequences_np = np.array(sequences, dtype=np.float32)
        targets_np = np.array(targets, dtype=np.float32)
        
        # Split data on CPU to avoid GPU memory issues
        X_train_np, X_val_np, y_train_np, y_val_np = train_test_split(
            sequences_np, targets_np, test_size=0.2, random_state=42
        )
        
        # Convert to tensors and move to GPU once
        X_train = torch.from_numpy(X_train_np).to(self.device, non_blocking=True)
        X_val = torch.from_numpy(X_val_np).to(self.device, non_blocking=True)
        y_train = torch.from_numpy(y_train_np).to(self.device, non_blocking=True)
        y_val = torch.from_numpy(y_val_np).to(self.device, non_blocking=True)
        
        # Optimize batch size for GPU utilization
        optimal_batch_size = min(max(batch_size, 128), len(X_train) // 4)
        logger.info(f"Using optimized batch size for scrolling: {optimal_batch_size}")
        
        # Training setup with optimized parameters
        optimizer = torch.optim.AdamW(self.scrolling_model.parameters(), lr=learning_rate, weight_decay=1e-4)
        criterion = nn.MSELoss()
        scheduler = torch.optim.lr_scheduler.OneCycleLR(
            optimizer, max_lr=learning_rate*10, epochs=epochs, 
            steps_per_epoch=len(X_train)//optimal_batch_size + 1
        )
        
        # Enable mixed precision training for better GPU utilization
        scaler = torch.cuda.amp.GradScaler() if self.device.type == 'cuda' else None
        
        best_val_loss = float('inf')
        for epoch in range(epochs):
            self.scrolling_model.train()
            total_loss = 0
            num_batches = 0
            
            # Shuffle indices for better training
            indices = torch.randperm(len(X_train), device=self.device)
            
            for i in range(0, len(X_train), optimal_batch_size):
                batch_indices = indices[i:i+optimal_batch_size]
                batch_X = X_train[batch_indices]
                batch_y = y_train[batch_indices]
                
                optimizer.zero_grad()
                
                # Use mixed precision if available
                if scaler is not None:
                    with torch.cuda.amp.autocast():
                        outputs = self.scrolling_model(batch_X)
                        loss = criterion(outputs, batch_y)
                    
                    scaler.scale(loss).backward()
                    scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(self.scrolling_model.parameters(), max_norm=1.0)
                    scaler.step(optimizer)
                    scaler.update()
                else:
                    outputs = self.scrolling_model(batch_X)
                    loss = criterion(outputs, batch_y)
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.scrolling_model.parameters(), max_norm=1.0)
                    optimizer.step()
                
                scheduler.step()
                total_loss += loss.item()
                num_batches += 1
            
            # Validation with optimized batch processing
            self.scrolling_model.eval()
            val_loss = 0
            val_batches = 0
            with torch.no_grad():
                for i in range(0, len(X_val), optimal_batch_size):
                    batch_X_val = X_val[i:i+optimal_batch_size]
                    batch_y_val = y_val[i:i+optimal_batch_size]
                    
                    if scaler is not None:
                        with torch.cuda.amp.autocast():
                            val_outputs = self.scrolling_model(batch_X_val)
                            batch_val_loss = criterion(val_outputs, batch_y_val)
                    else:
                        val_outputs = self.scrolling_model(batch_X_val)
                        batch_val_loss = criterion(val_outputs, batch_y_val)
                    
                    val_loss += batch_val_loss.item()
                    val_batches += 1
            
            avg_val_loss = val_loss / val_batches if val_batches > 0 else float('inf')
            
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                torch.save(self.scrolling_model.state_dict(), 'scrolling_model_best.pth')
            
            if epoch % 5 == 0:  # More frequent logging
                avg_train_loss = total_loss / num_batches if num_batches > 0 else 0
                logger.info(f"Scrolling Model - Epoch {epoch}: Train Loss: {avg_train_loss:.6f}, "
                           f"Val Loss: {avg_val_loss:.6f}, LR: {scheduler.get_last_lr()[0]:.6f}")
    
    def save_models(self, directory: str = 'saved_models/'):
        """Save all trained models"""
        os.makedirs(directory, exist_ok=True)
        
        torch.save(self.mouse_model.state_dict(), os.path.join(directory, 'mouse_model.pth'))
        torch.save(self.typing_model.state_dict(), os.path.join(directory, 'typing_model.pth'))
        torch.save(self.scrolling_model.state_dict(), os.path.join(directory, 'scrolling_model.pth'))
        
        # Save configuration
        config_dict = {
            'mouse_smoothness': self.config.mouse_smoothness,
            'mouse_speed_variance': self.config.mouse_speed_variance,
            'mouse_error_rate': self.config.mouse_error_rate,
            'typing_speed_wpm': self.config.typing_speed_wpm,
            'typing_error_rate': self.config.typing_error_rate,
            'typing_correction_delay': self.config.typing_correction_delay,
            'scroll_smoothness': self.config.scroll_smoothness,
            'scroll_overshoot_rate': self.config.scroll_overshoot_rate,
            'reaction_time_base': self.config.reaction_time_base,
            'reaction_time_variance': self.config.reaction_time_variance,
            'add_micro_movements': self.config.add_micro_movements,
            'randomize_timing': self.config.randomize_timing,
            'simulate_fatigue': self.config.simulate_fatigue
        }
        
        with open(os.path.join(directory, 'config.json'), 'w') as f:
            json.dump(config_dict, f, indent=2)
        
        logger.info(f"Models saved to {directory}")
    
    def load_models(self, directory: str = 'saved_models/'):
        """Load pre-trained models"""
        try:
            self.mouse_model.load_state_dict(torch.load(
                os.path.join(directory, 'mouse_model.pth'), map_location=self.device
            ))
            self.typing_model.load_state_dict(torch.load(
                os.path.join(directory, 'typing_model.pth'), map_location=self.device
            ))
            self.scrolling_model.load_state_dict(torch.load(
                os.path.join(directory, 'scrolling_model.pth'), map_location=self.device
            ))
            
            # Load configuration
            config_path = os.path.join(directory, 'config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config_dict = json.load(f)
                    for key, value in config_dict.items():
                        setattr(self.config, key, value)
            
            logger.info(f"Models loaded from {directory}")
        except Exception as e:
            logger.error(f"Error loading models: {e}")

if __name__ == "__main__":
    # Example usage
    config = InteractionConfig()
    ai = HumanInteractionAI(config)
    
    # Load and train on data
    ai.load_training_data('data/')
    ai.train_models(epochs=50, batch_size=16, learning_rate=0.001)
    
    # Save the trained models
    ai.save_models()