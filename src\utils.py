import os
import datetime
import asyncio
from .logger import logger # Import the new logger

async def log_error(email, error_message, page_html=None):
    """
    Logs an error message to a file using the logger and optionally saves the page HTML.
    """
    logs_dir = "error_logs"
    html_errors_dir = "errors_html"
    
    os.makedirs(logs_dir, exist_ok=True)
    os.makedirs(html_errors_dir, exist_ok=True)

    timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    
    # Log the error using the logger
    logger.error(f"Email: {email} - Error: {error_message}", extra={'email': email})
    
    # Save HTML to a separate file if provided
    if page_html:
        html_file_name = f"error_page_{email}_{timestamp}.html"
        html_file_path = os.path.join(html_errors_dir, html_file_name)
        with open(html_file_path, "w", encoding="utf-8") as f:
            f.write(page_html)
        logger.info(f"Error HTML saved to: {html_file_path}", extra={'email': email})