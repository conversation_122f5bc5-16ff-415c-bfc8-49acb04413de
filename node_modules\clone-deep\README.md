# clone-deep [![NPM version](https://img.shields.io/npm/v/clone-deep.svg)](https://www.npmjs.com/package/clone-deep) [![Build Status](https://img.shields.io/travis/jonschlinkert/clone-deep.svg)](https://travis-ci.org/jonschlinkert/clone-deep)

> Recursively (deep) clone JavaScript native types, like Object, Array, RegExp, Date as well as primitives.

The `instanceClone` function is invoked to clone objects that are not "plain" objects (as defined by [](#isPlainObject)`isPlainObject`) if it is provided. If `instanceClone` is not specified, it will not attempt to clone non-plain objects, and will copy the object reference.

## Install

Install with [npm](https://www.npmjs.com/)

```sh
$ npm i clone-deep --save
```

## Usage

```js
var cloneDeep = require('clone-deep');

var obj = {a: 'b'};
var arr = [obj];

var copy = cloneDeep(arr);
obj.c = 'd';

console.log(copy);
//=> [{a: 'b'}]

console.log(arr);
//=> [{a: 'b', c: 'd'}]
```

## Other object utils

* [assign-deep](https://www.npmjs.com/package/assign-deep): Deeply assign the enumerable properties and/or es6 Symbol properies of source objects to the target… [more](https://www.npmjs.com/package/assign-deep) | [homepage](https://github.com/jonschlinkert/assign-deep)
* [extend-shallow](https://www.npmjs.com/package/extend-shallow): Extend an object with the properties of additional objects. node.js/javascript util. | [homepage](https://github.com/jonschlinkert/extend-shallow)
* [merge-deep](https://www.npmjs.com/package/merge-deep): Recursively merge values in a javascript object. | [homepage](https://github.com/jonschlinkert/merge-deep)
* [mixin-deep](https://www.npmjs.com/package/mixin-deep): Deeply mix the properties of objects into the first object. Like merge-deep, but doesn't clone. | [homepage](https://github.com/jonschlinkert/mixin-deep)
* [mixin-object](https://www.npmjs.com/package/mixin-object): Mixin the own and inherited properties of other objects onto the first object. Pass an… [more](https://www.npmjs.com/package/mixin-object) | [homepage](https://github.com/jonschlinkert/mixin-object)
* [shallow-clone](https://www.npmjs.com/package/shallow-clone): Make a shallow clone of an object, array or primitive. | [homepage](https://github.com/jonschlinkert/shallow-clone)

## Running tests

Install dev dependencies:

```sh
$ npm i -d && npm test
```

## Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](https://github.com/jonschlinkert/clone-deep/issues/new).

## Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](http://twitter.com/jonschlinkert)

Based on [mout's](https://github.com/mout/mout) implementation of deepClone.

## License

Copyright © 2014-2015 [Jon Schlinkert](https://github.com/jonschlinkert)
Released under the MIT license.

***

_This file was generated by [verb](https://github.com/verbose/verb) on December 23, 2015._

<!-- deps:helper-lookup-deps -->