import json
import os

def visualize_interaction_data(data):
    """
    Visualizes a single interaction dataset for debugging and analysis.
    This function provides a simple text-based visualization.

    Args:
        data (dict): A dictionary representing a single parsed JSON interaction dataset.
                     Expected to follow the structure defined in DATA_STRUCTURE.md.
    """
    print("\n--- Interaction Visualization ---")
    print(f"Session ID: {data.get('session_id', 'N/A')}")
    print(f"Timestamp: {data.get('timestamp', 'N/A')}")
    print(f"Interaction Type: {data.get('interaction_type', 'N/A')}")

    if 'mouse_movement' in data and data['mouse_movement']:
        print("\nMouse Movement Events:")
        for i, event in enumerate(data['mouse_movement']):
            # Assuming event is [timestamp, x, y]
            print(f"  {i+1}. Time: {event[0]}, X: {event[1]}, Y: {event[2]}")
            if i >= 9: # Limit to first 10 events for brevity
                print("  ... (truncated)")
                break

    if 'typing_events' in data and data['typing_events']:
        print("\nTyping Events:")
        for i, event in enumerate(data['typing_events']):
            # Assuming event is [timestamp, key, type]
            print(f"  {i+1}. Time: {event[0]}, Key: '{event[1]}', Type: {event[2]}")
            if i >= 9: # Limit to first 10 events for brevity
                print("  ... (truncated)")
                break

    if 'scroll_events' in data and data['scroll_events']:
        print("\nScroll Events:")
        for i, event in enumerate(data['scroll_events']):
            # Assuming event is [timestamp, scrollX, scrollY]
            print(f"  {i+1}. Time: {event[0]}, ScrollX: {event[1]}, ScrollY: {event[2]}")
            if i >= 9: # Limit to first 10 events for brevity
                print("  ... (truncated)")
                break

    if 'click_events' in data and data['click_events']:
        print("\nClick Events:")
        for i, event in enumerate(data['click_events']):
            # Assuming event is [timestamp, x, y, button]
            print(f"  {i+1}. Time: {event[0]}, X: {event[1]}, Y: {event[2]}, Button: {event[3]}")
            if i >= 9: # Limit to first 10 events for brevity
                print("  ... (truncated)")
                break

    print("---------------------------\n")

if __name__ == '__main__':
    # Example of how to use the function with dummy data
    dummy_data_mouse = {
        "session_id": "dummy_mouse_123",
        "timestamp": 1678886400,
        "interaction_type": "mouse_movement",
        "mouse_movement": [
            [100, 50, 100], [150, 55, 105], [200, 60, 110], [250, 65, 115],
            [300, 70, 120], [350, 75, 125], [400, 80, 130], [450, 85, 135],
            [500, 90, 140], [550, 95, 145], [600, 100, 150], [650, 105, 155]
        ]
    }

    dummy_data_typing = {
        "session_id": "dummy_typing_456",
        "timestamp": 1678886500,
        "interaction_type": "typing",
        "typing_events": [
            [100, "H", "keydown"], [120, "e", "keydown"], [140, "l", "keydown"],
            [160, "l", "keydown"], [180, "o", "keydown"], [200, " ", "keydown"],
            [220, "W", "keydown"], [240, "o", "keydown"], [260, "r", "keydown"],
            [280, "l", "keydown"], [300, "d", "keydown"]
        ]
    }

    dummy_data_mixed = {
        "session_id": "dummy_mixed_789",
        "timestamp": 1678886600,
        "interaction_type": "mixed",
        "mouse_movement": [
            [100, 10, 20], [150, 12, 22]
        ],
        "typing_events": [
            [110, "A", "keydown"], [130, "B", "keydown"]
        ],
        "scroll_events": [
            [200, 0, 10], [220, 0, 25]
        ],
        "click_events": [
            [300, 100, 200, 0]
        ]
    }

    print("Visualizing dummy mouse movement data:")
    visualize_interaction_data(dummy_data_mouse)

    print("Visualizing dummy typing data:")
    visualize_interaction_data(dummy_data_typing)

    print("Visualizing dummy mixed data:")
    visualize_interaction_data(dummy_data_mixed)

    # To visualize actual data, you would combine with data_loader:
    # from data_loader import load_interaction_data
    # actual_data = load_interaction_data('data/')
    # if actual_data:
    #     print("\nVisualizing first actual loaded dataset:")
    #     visualize_interaction_data(actual_data[0])