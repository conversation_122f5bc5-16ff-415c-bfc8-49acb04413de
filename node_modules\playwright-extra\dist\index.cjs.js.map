{"version": 3, "file": "index.cjs.js", "sources": ["../src/helper/loader.ts", "../src/puppeteer-compatiblity-shim/index.ts", "../src/plugins.ts", "../src/extra.ts", "../src/index.ts"], "sourcesContent": ["import type * as pw from 'playwright-core'\n\n/** Node.js module loader helper */\nexport class Loader<TargetModule> {\n  constructor(public moduleName: string, public packageNames: string[]) {}\n\n  /**\n   * Lazy load a top level export from another module by wrapping it in a JS proxy.\n   *\n   * This allows us to re-export e.g. `devices` from `playwright` while redirecting direct calls\n   * to it to the module version the user has installed, rather than shipping with a hardcoded version.\n   *\n   * If we don't do this and the user doesn't have the target module installed we'd throw immediately when our code is imported.\n   *\n   * We use a \"super\" Proxy defining all traps, so calls like `Object.keys(playwright.devices).length` will return the correct value.\n   */\n  public lazyloadExportOrDie<T extends keyof TargetModule>(exportName: T) {\n    const that = this\n    const trapHandler = Object.fromEntries(\n      Object.getOwnPropertyNames(Reflect).map((name: any) => [\n        name,\n        function (target: any, ...args: any[]) {\n          const moduleExport = that.loadModuleOrDie()[exportName]\n          const customTarget = moduleExport as any\n          const result = ((Reflect as any)[name] as any)(\n            customTarget || target,\n            ...args\n          )\n          return result\n        }\n      ])\n    )\n    return new Proxy({}, trapHandler) as TargetModule[T]\n  }\n\n  /** Load the module if possible */\n  public loadModule() {\n    return requirePackages<TargetModule>(this.packageNames)\n  }\n\n  /** Load the module if possible or throw */\n  public loadModuleOrDie(): TargetModule {\n    const module = requirePackages<TargetModule>(this.packageNames)\n    if (module) {\n      return module\n    }\n    throw this.requireError\n  }\n\n  public get requireError() {\n    const moduleNamePretty =\n      this.moduleName.charAt(0).toUpperCase() + this.moduleName.slice(1)\n    return new Error(`\n  ${moduleNamePretty} is missing. :-)\n\n  I've tried loading ${this.packageNames\n    .map(p => `\"${p}\"`)\n    .join(', ')} - no luck.\n\n  Make sure you install one of those packages or use the named 'addExtra' export,\n  to patch a specific (and maybe non-standard) implementation of ${moduleNamePretty}.\n\n  To get the latest stable version of ${moduleNamePretty} run:\n  'yarn add ${this.moduleName}' or 'npm i ${this.moduleName}'\n  `)\n  }\n}\n\nexport function requirePackages<TargetModule = any>(packageNames: string[]) {\n  for (const name of packageNames) {\n    try {\n      return require(name) as TargetModule\n    } catch (_) {\n      continue // noop\n    }\n  }\n  return\n}\n\n/** Playwright specific module loader */\nexport const playwrightLoader = new Loader<typeof pw>('playwright', [\n  'playwright-core',\n  'playwright'\n])\n", "import Debug from 'debug'\nconst debug = Debug('playwright-extra:puppeteer-compat')\n\nimport type * as pw from 'playwright-core'\n\nexport type PlaywrightObject = pw.Page | pw.Frame | pw.Browser\n\nexport interface PuppeteerBrowserShim {\n  isCompatShim?: boolean\n  isPlaywright?: boolean\n  pages?: pw.BrowserContext['pages']\n  userAgent: () => Promise<'string'>\n}\n\nexport interface PuppeteerPageShim {\n  isCompatShim?: boolean\n  isPlaywright?: boolean\n  browser?: () => pw.Browser\n  evaluateOnNewDocument?: pw.Page['addInitScript']\n  _client: () => pw.CDPSession\n}\n\nexport const isPlaywrightPage = (obj: unknown): obj is pw.Page => {\n  return 'unroute' in (obj as pw.Page)\n}\nexport const isPlaywrightFrame = (obj: unknown): obj is pw.Frame => {\n  return ['parentFrame', 'frameLocator'].every(x => x in (obj as pw.Frame))\n}\nexport const isPlaywrightBrowser = (obj: unknown): obj is pw.Browser => {\n  return 'newContext' in (obj as pw.Browser)\n}\nexport const isPuppeteerCompat = (obj?: unknown): obj is PlaywrightObject => {\n  return !!obj && typeof obj === 'object' && !!(obj as any).isCompatShim\n}\n\nconst cache = {\n  objectToShim: new Map<PlaywrightObject, PlaywrightObject>(),\n  cdpSession: {\n    page: new Map<pw.Page | pw.Frame, pw.CDPSession>(),\n    browser: new Map<pw.Browser, pw.CDPSession>()\n  }\n}\n\n/** Augment a Playwright object with compatibility with certain Puppeteer methods */\nexport function addPuppeteerCompat<\n  Input extends pw.Page | pw.Frame | pw.Browser | null\n>(object: Input): Input {\n  if (!object || typeof object !== 'object') {\n    return object\n  }\n  if (cache.objectToShim.has(object)) {\n    return cache.objectToShim.get(object) as Input\n  }\n  if (isPuppeteerCompat(object)) {\n    return object\n  }\n  debug('addPuppeteerCompat', cache.objectToShim.size)\n  if (isPlaywrightPage(object) || isPlaywrightFrame(object)) {\n    const shim = createPageShim(object)\n    cache.objectToShim.set(object, shim)\n    return shim as Input\n  }\n  if (isPlaywrightBrowser(object)) {\n    const shim = createBrowserShim(object)\n    cache.objectToShim.set(object, shim)\n    return shim as Input\n  }\n  debug('Received unknown object:', Reflect.ownKeys(object))\n  return object\n}\n\n// Only chromium browsers support CDP\nconst dummyCDPClient = {\n  send: async (...args: any[]) => {\n    debug('dummy CDP client called', 'send', args)\n  },\n  on: (...args: any[]) => {\n    debug('dummy CDP client called', 'on', args)\n  }\n} as pw.CDPSession\n\nexport async function getPageCDPSession(page: pw.Page | pw.Frame) {\n  let session = cache.cdpSession.page.get(page)\n  if (session) {\n    debug('getPageCDPSession: use existing')\n    return session\n  }\n  debug('getPageCDPSession: use new')\n  const context = isPlaywrightFrame(page)\n    ? page.page().context()\n    : page.context()\n  try {\n    session = await context.newCDPSession(page)\n    cache.cdpSession.page.set(page, session)\n    return session\n  } catch (err: any) {\n    debug('getPageCDPSession: error while creating session:', err.message)\n    debug(\n      'getPageCDPSession: Unable create CDP session (most likely a different browser than chromium) - returning a dummy'\n    )\n  }\n  return dummyCDPClient\n}\n\nexport async function getBrowserCDPSession(browser: pw.Browser) {\n  let session = cache.cdpSession.browser.get(browser)\n  if (session) {\n    debug('getBrowserCDPSession: use existing')\n    return session\n  }\n  debug('getBrowserCDPSession: use new')\n  try {\n    session = await browser.newBrowserCDPSession()\n    cache.cdpSession.browser.set(browser, session)\n    return session\n  } catch (err: any) {\n    debug('getBrowserCDPSession: error while creating session:', err.message)\n    debug(\n      'getBrowserCDPSession: Unable create CDP session (most likely a different browser than chromium) - returning a dummy'\n    )\n  }\n  return dummyCDPClient\n}\n\nexport function createPageShim(page: pw.Page | pw.Frame) {\n  const objId = Math.random().toString(36).substring(2, 7)\n  const shim = new Proxy(page, {\n    get(target, prop) {\n      if (prop === 'isCompatShim' || prop === 'isPlaywright') {\n        return true\n      }\n      debug('page - get', objId, prop)\n      if (prop === '_client') {\n        return () => ({\n          send: async (method: string, params: any) => {\n            const session = await getPageCDPSession(page)\n            return await session.send(method as any, params)\n          },\n          on: (event: string, listener: any) => {\n            getPageCDPSession(page).then(session => {\n              session.on(event as any, listener)\n            })\n          }\n        })\n      }\n      if (prop === 'setBypassCSP') {\n        return async (enabled: boolean) => {\n          const session = await getPageCDPSession(page)\n          return await session.send('Page.setBypassCSP', {\n            enabled\n          })\n        }\n      }\n      if (prop === 'setUserAgent') {\n        return async (userAgent: string, userAgentMetadata?: any) => {\n          const session = await getPageCDPSession(page)\n          return await session.send('Emulation.setUserAgentOverride', {\n            userAgent,\n            userAgentMetadata\n          })\n        }\n      }\n      if (prop === 'browser') {\n        if (isPlaywrightPage(page)) {\n          return () => {\n            let browser = page.context().browser()\n            if (!browser) {\n              debug(\n                'page.browser() - not available, most likely due to launchPersistentContext'\n              )\n              // Use a page shim as quick drop-in (so browser.userAgent() still works)\n              browser = page as any\n            }\n            return addPuppeteerCompat(browser)\n          }\n        }\n      }\n      if (prop === 'evaluateOnNewDocument') {\n        if (isPlaywrightPage(page)) {\n          return async function (pageFunction: any | string, ...args: any[]) {\n            return await page.addInitScript(pageFunction, args[0])\n          }\n        }\n      }\n      // Only relevant when page is being used a pseudo stand-in for the browser object (launchPersistentContext)\n      if (prop === 'userAgent') {\n        return async (enabled: boolean) => {\n          const session = await getPageCDPSession(page)\n          const data = await session.send('Browser.getVersion')\n          return data.userAgent\n        }\n      }\n      return Reflect.get(target, prop)\n    }\n  })\n  return shim\n}\n\nexport function createBrowserShim(browser: pw.Browser) {\n  const objId = Math.random().toString(36).substring(2, 7)\n  const shim = new Proxy(browser, {\n    get(target, prop) {\n      if (prop === 'isCompatShim' || prop === 'isPlaywright') {\n        return true\n      }\n      debug('browser - get', objId, prop)\n      if (prop === 'pages') {\n        return () =>\n          browser\n            .contexts()\n            .flatMap(c => c.pages().map(page => addPuppeteerCompat(page)))\n      }\n      if (prop === 'userAgent') {\n        return async () => {\n          const session = await getBrowserCDPSession(browser)\n          const data = await session.send('Browser.getVersion')\n          return data.userAgent\n        }\n      }\n      return Reflect.get(target, prop)\n    }\n  })\n  return shim\n}\n", "import Debug from 'debug'\nconst debug = Debug('playwright-extra:plugins')\n\nimport {\n  Plugin,\n  PluginMethodName,\n  PluginMethodFn,\n  PluginModule,\n  CompatiblePluginModule\n} from './types'\n\nimport { requirePackages } from './helper/loader'\nimport { addPuppeteerCompat } from './puppeteer-compatiblity-shim'\n\nexport class PluginList {\n  private readonly _plugins: Plugin[] = []\n  private readonly _dependencyDefaults: Map<string, any> = new Map()\n  private readonly _dependencyResolution: Map<string, CompatiblePluginModule> =\n    new Map()\n\n  constructor() {}\n\n  /**\n   * Get a list of all registered plugins.\n   */\n  public get list() {\n    return this._plugins\n  }\n\n  /**\n   * Get the names of all registered plugins.\n   */\n  public get names() {\n    return this._plugins.map(p => p.name)\n  }\n\n  /**\n   * Add a new plugin to the list (after checking if it's well-formed).\n   *\n   * @param plugin\n   * @internal\n   */\n  public add(plugin: Plugin) {\n    if (!this.isValidPluginInstance(plugin)) {\n      return false\n    }\n    if (!!plugin.onPluginRegistered) {\n      plugin.onPluginRegistered({ framework: 'playwright' })\n    }\n    // PuppeteerExtraPlugin: Populate `_childClassMembers` list containing methods defined by the plugin\n    if (!!plugin._registerChildClassMembers) {\n      plugin._registerChildClassMembers(Object.getPrototypeOf(plugin))\n    }\n    if (plugin.requirements?.has('dataFromPlugins')) {\n      plugin.getDataFromPlugins = this.getData.bind(this)\n    }\n    this._plugins.push(plugin)\n    return true\n  }\n\n  /** Check if the shape of a plugin is correct or warn */\n  protected isValidPluginInstance(plugin: Plugin) {\n    if (\n      !plugin ||\n      typeof plugin !== 'object' ||\n      !plugin._isPuppeteerExtraPlugin\n    ) {\n      console.error(\n        `Warning: Plugin is not derived from PuppeteerExtraPlugin, ignoring.`,\n        plugin\n      )\n      return false\n    }\n    if (!plugin.name) {\n      console.error(\n        `Warning: Plugin with no name registering, ignoring.`,\n        plugin\n      )\n      return false\n    }\n    return true\n  }\n\n  /** Error callback in case calling a plugin method throws an error. Can be overwritten. */\n  public onPluginError(plugin: Plugin, method: PluginMethodName, err: Error) {\n    console.warn(\n      `An error occured while executing \"${method}\" in plugin \"${plugin.name}\":`,\n      err\n    )\n  }\n\n  /**\n   * Define default values for plugins implicitly required through the `dependencies` plugin stanza.\n   *\n   * @param dependencyPath - The string by which the dependency is listed (not the plugin name)\n   *\n   * @example\n   * chromium.use(stealth)\n   * chromium.plugins.setDependencyDefaults('stealth/evasions/webgl.vendor', { vendor: 'Bob', renderer: 'Alice' })\n   */\n  public setDependencyDefaults(dependencyPath: string, opts: any) {\n    this._dependencyDefaults.set(dependencyPath, opts)\n    return this\n  }\n\n  /**\n   * Define custom plugin modules for plugins implicitly required through the `dependencies` plugin stanza.\n   *\n   * Using this will prevent dynamic imports from being used, which JS bundlers often have issues with.\n   *\n   * @example\n   * chromium.use(stealth)\n   * chromium.plugins.setDependencyResolution('stealth/evasions/webgl.vendor', VendorPlugin)\n   */\n  public setDependencyResolution(\n    dependencyPath: string,\n    pluginModule: CompatiblePluginModule\n  ) {\n    this._dependencyResolution.set(dependencyPath, pluginModule)\n    return this\n  }\n\n  /**\n   * Prepare plugins to be used (resolve dependencies, ordering)\n   * @internal\n   */\n  public prepare() {\n    this.resolveDependencies()\n    this.order()\n  }\n\n  /** Return all plugins using the supplied method */\n  protected filterByMethod(methodName: PluginMethodName) {\n    return this._plugins.filter(plugin => {\n      // PuppeteerExtraPlugin: The base class will already define all methods, hence we need to do a different check\n      if (\n        !!plugin._childClassMembers &&\n        Array.isArray(plugin._childClassMembers)\n      ) {\n        return plugin._childClassMembers.includes(methodName)\n      }\n      return methodName in plugin\n    })\n  }\n\n  /** Conditionally add puppeteer compatibility to values provided to the plugins */\n  protected _addPuppeteerCompatIfNeeded<TMethod extends PluginMethodName>(\n    plugin: Plugin,\n    method: TMethod,\n    args: Parameters<PluginMethodFn<TMethod>>\n  ) {\n    const canUseShim = plugin._isPuppeteerExtraPlugin && !plugin.noPuppeteerShim\n    const methodWhitelist: PluginMethodName[] = [\n      'onBrowser',\n      'onPageCreated',\n      'onPageClose',\n      'afterConnect',\n      'afterLaunch'\n    ]\n    const shouldUseShim = methodWhitelist.includes(method)\n    if (!canUseShim || !shouldUseShim) {\n      return args\n    }\n    debug('add puppeteer compatibility', plugin.name, method)\n    return [...args.map(arg => addPuppeteerCompat(arg as any))] as Parameters<\n      PluginMethodFn<TMethod>\n    >\n  }\n\n  /**\n   * Dispatch plugin lifecycle events in a typesafe way.\n   * Only Plugins that expose the supplied property will be called.\n   *\n   * Will not await results to dispatch events as fast as possible to all plugins.\n   *\n   * @param method - The lifecycle method name\n   * @param args - Optional: Any arguments to be supplied to the plugin methods\n   * @internal\n   */\n  public dispatch<TMethod extends PluginMethodName>(\n    method: TMethod,\n    ...args: Parameters<PluginMethodFn<TMethod>>\n  ): void {\n    const plugins = this.filterByMethod(method)\n    debug('dispatch', method, {\n      all: this._plugins.length,\n      filteredByMethod: plugins.length\n    })\n    for (const plugin of plugins) {\n      try {\n        args = this._addPuppeteerCompatIfNeeded.bind(this)(plugin, method, args)\n        const fnType = plugin[method]?.constructor?.name\n        debug('dispatch to plugin', {\n          plugin: plugin.name,\n          method,\n          fnType\n        })\n        if (fnType === 'AsyncFunction') {\n          ;(plugin[method] as any)(...args).catch((err: any) =>\n            this.onPluginError(plugin, method, err)\n          )\n        } else {\n          ;(plugin[method] as any)(...args)\n        }\n      } catch (err) {\n        this.onPluginError(plugin, method, err as any)\n      }\n    }\n  }\n\n  /**\n   * Dispatch plugin lifecycle events in a typesafe way.\n   * Only Plugins that expose the supplied property will be called.\n   *\n   * Can also be used to get a definite return value after passing it to plugins:\n   * Calls plugins sequentially and passes on a value (waterfall style).\n   *\n   * The plugins can either modify the value or return an updated one.\n   * Will return the latest, updated value which ran through all plugins.\n   *\n   * By convention only the first argument will be used as the updated value.\n   *\n   * @param method - The lifecycle method name\n   * @param args - Optional: Any arguments to be supplied to the plugin methods\n   * @internal\n   */\n  public async dispatchBlocking<TMethod extends PluginMethodName>(\n    method: TMethod,\n    ...args: Parameters<PluginMethodFn<TMethod>>\n  ): Promise<ReturnType<PluginMethodFn<TMethod>>> {\n    const plugins = this.filterByMethod(method)\n    debug('dispatchBlocking', method, {\n      all: this._plugins.length,\n      filteredByMethod: plugins.length\n    })\n\n    let retValue: any = null\n    for (const plugin of plugins) {\n      try {\n        args = this._addPuppeteerCompatIfNeeded.bind(this)(plugin, method, args)\n        retValue = await (plugin[method] as any)(...args)\n        // In case we got a return value use that as new first argument for followup function calls\n        if (retValue !== undefined) {\n          args[0] = retValue\n        }\n      } catch (err) {\n        this.onPluginError(plugin, method, err as any)\n        return retValue\n      }\n    }\n    return retValue\n  }\n\n  /**\n   * Order plugins that have expressed a special placement requirement.\n   *\n   * This is useful/necessary for e.g. plugins that depend on the data from other plugins.\n   *\n   * @private\n   */\n  protected order() {\n    debug('order:before', this.names)\n    const runLast = this._plugins\n      .filter(p => p.requirements?.has('runLast'))\n      .map(p => p.name)\n    for (const name of runLast) {\n      const index = this._plugins.findIndex(p => p.name === name)\n      this._plugins.push(this._plugins.splice(index, 1)[0])\n    }\n    debug('order:after', this.names)\n  }\n\n  /**\n   * Collects the exposed `data` property of all registered plugins.\n   * Will be reduced/flattened to a single array.\n   *\n   * Can be accessed by plugins that listed the `dataFromPlugins` requirement.\n   *\n   * Implemented mainly for plugins that need data from other plugins (e.g. `user-preferences`).\n   *\n   * @see [PuppeteerExtraPlugin]/data\n   * @param name - Filter data by optional name\n   *\n   * @private\n   */\n  protected getData(name?: string) {\n    const data = this._plugins\n      .filter((p: any) => !!p.data)\n      .map((p: any) => (Array.isArray(p.data) ? p.data : [p.data]))\n      .reduce((acc, arr) => [...acc, ...arr], [])\n    return name ? data.filter((d: any) => d.name === name) : data\n  }\n\n  /**\n   * Handle `plugins` stanza (already instantiated plugins that don't require dynamic imports)\n   */\n  protected resolvePluginsStanza() {\n    debug('resolvePluginsStanza')\n    const pluginNames = new Set(this.names)\n    this._plugins\n      .filter(p => !!p.plugins && p.plugins.length)\n      .filter(p => !pluginNames.has(p.name)) // TBD: Do we want to filter out existing?\n      .forEach(parent => {\n        ;(parent.plugins || []).forEach(p => {\n          debug(parent.name, 'adding missing plugin', p.name)\n          this.add(p as Plugin)\n        })\n      })\n  }\n\n  /**\n   * Handle `dependencies` stanza (which requires dynamic imports)\n   *\n   * Plugins can define `dependencies` as a Set or Array of dependency paths, or a Map with additional opts\n   *\n   * @note\n   * - The default opts for implicit dependencies can be defined using `setDependencyDefaults()`\n   * - Dynamic imports can be avoided by providing plugin modules with `setDependencyResolution()`\n   */\n  protected resolveDependenciesStanza() {\n    debug('resolveDependenciesStanza')\n\n    /** Attempt to dynamically require a plugin module */\n    const requireDependencyOrDie = (\n      parentName: string,\n      dependencyPath: string\n    ) => {\n      // If the user provided the plugin module already we use that\n      if (this._dependencyResolution.has(dependencyPath)) {\n        return this._dependencyResolution.get(dependencyPath) as PluginModule\n      }\n\n      const possiblePrefixes = ['puppeteer-extra-plugin-'] // could be extended later\n      const isAlreadyPrefixed = possiblePrefixes.some(prefix =>\n        dependencyPath.startsWith(prefix)\n      )\n      const packagePaths: string[] = []\n      // If the dependency is not already prefixed we attempt to require all possible combinations to find one that works\n      if (!isAlreadyPrefixed) {\n        packagePaths.push(\n          ...possiblePrefixes.map(prefix => prefix + dependencyPath)\n        )\n      }\n      // We always attempt to require the path verbatim (as a last resort)\n      packagePaths.push(dependencyPath)\n      const pluginModule = requirePackages<PluginModule>(packagePaths)\n      if (pluginModule) {\n        return pluginModule\n      }\n\n      const explanation = `\nThe plugin '${parentName}' listed '${dependencyPath}' as dependency,\nwhich could not be found. Please install it:\n\n${packagePaths\n  .map(packagePath => `yarn add ${packagePath.split('/')[0]}`)\n  .join(`\\n or:\\n`)}\n\nNote: You don't need to require the plugin yourself,\nunless you want to modify it's default settings.\n\nIf your bundler has issues with dynamic imports take a look at '.plugins.setDependencyResolution()'.\n      `\n      console.warn(explanation)\n      throw new Error('Plugin dependency not found')\n    }\n\n    const existingPluginNames = new Set(this.names)\n    const recursivelyLoadMissingDependencies = ({\n      name: parentName,\n      dependencies\n    }: Plugin): any => {\n      if (!dependencies) {\n        return\n      }\n      const processDependency = (dependencyPath: string, opts?: any) => {\n        const pluginModule = requireDependencyOrDie(parentName, dependencyPath)\n        opts = opts || this._dependencyDefaults.get(dependencyPath) || {}\n        const plugin = pluginModule(opts)\n        if (existingPluginNames.has(plugin.name)) {\n          debug(parentName, '=> dependency already exists:', plugin.name)\n          return\n        }\n        existingPluginNames.add(plugin.name)\n        debug(parentName, '=> adding new dependency:', plugin.name, opts)\n        this.add(plugin)\n        return recursivelyLoadMissingDependencies(plugin)\n      }\n\n      if (dependencies instanceof Set || Array.isArray(dependencies)) {\n        return [...dependencies].forEach(dependencyPath =>\n          processDependency(dependencyPath)\n        )\n      }\n      if (dependencies instanceof Map) {\n        // Note: `k,v => v,k` (Map + forEach will reverse the order)\n        return dependencies.forEach((v, k) => processDependency(k, v))\n      }\n    }\n    this.list.forEach(recursivelyLoadMissingDependencies)\n  }\n\n  /**\n   * Lightweight plugin dependency management to require plugins and code mods on demand.\n   * @private\n   */\n  protected resolveDependencies() {\n    debug('resolveDependencies')\n    this.resolvePluginsStanza()\n    this.resolveDependenciesStanza()\n  }\n}\n", "import Debug from 'debug'\nconst debug = Debug('playwright-extra')\n\nimport type * as pw from 'playwright-core'\nimport type { CompatiblePlugin, Plugin } from './types'\n\nimport { PluginList } from './plugins'\nimport { playwrightLoader } from './helper/loader'\n\ntype PlaywrightBrowserLauncher = pw.BrowserType\n\n/**\n * The Playwright browser launcher APIs we're augmenting\n * @private\n */\ninterface AugmentedLauncherAPIs\n  extends Pick<\n    PlaywrightBrowserLauncher,\n    'launch' | 'launchPersistentContext' | 'connect' | 'connectOverCDP'\n  > {}\n\n/**\n * Modular plugin framework to teach `playwright` new tricks.\n */\nexport class PlaywrightExtraClass implements AugmentedLauncherAPIs {\n  /** Plugin manager */\n  public readonly plugins: PluginList\n\n  constructor(private _launcher?: Partial<PlaywrightBrowserLauncher>) {\n    this.plugins = new PluginList()\n  }\n\n  /**\n   * The **main interface** to register plugins.\n   *\n   * Can be called multiple times to enable multiple plugins.\n   *\n   * Plugins derived from `PuppeteerExtraPlugin` will be used with a compatiblity layer.\n   *\n   * @example\n   * chromium.use(plugin1).use(plugin2)\n   * firefox.use(plugin1).use(plugin2)\n   *\n   * @see [PuppeteerExtraPlugin]\n   *\n   * @return The same `PlaywrightExtra` instance (for optional chaining)\n   */\n  public use(plugin: CompatiblePlugin): this {\n    const isValid = plugin && 'name' in plugin\n    if (!isValid) {\n      throw new Error('A plugin must be provided to .use()')\n    }\n    if (this.plugins.add(plugin as Plugin)) {\n      debug('Plugin registered', plugin.name)\n    }\n    return this\n  }\n\n  /**\n   * In order to support a default export which will require vanilla playwright automatically,\n   * as well as `addExtra` to patch a provided launcher, we need to so some gymnastics here.\n   *\n   * Otherwise this would throw immediately, even when only using the `addExtra` export with an arbitrary compatible launcher.\n   *\n   * The solution is to make the vanilla launcher optional and only throw once we try to effectively use and can't find it.\n   *\n   * @internal\n   */\n  public get launcher(): Partial<PlaywrightBrowserLauncher> {\n    if (!this._launcher) {\n      throw playwrightLoader.requireError\n    }\n    return this._launcher\n  }\n\n  public async launch(\n    ...args: Parameters<PlaywrightBrowserLauncher['launch']>\n  ): ReturnType<PlaywrightBrowserLauncher['launch']> {\n    if (!this.launcher.launch) {\n      throw new Error('Launcher does not support \"launch\"')\n    }\n\n    let [options] = args\n    options = { args: [], ...(options || {}) } // Initialize args array\n    debug('launch', options)\n    this.plugins.prepare()\n\n    // Give plugins the chance to modify the options before continuing\n    options =\n      (await this.plugins.dispatchBlocking('beforeLaunch', options)) || options\n\n    debug('launch with options', options)\n    if ('userDataDir' in options) {\n      debug(\n        \"A plugin defined userDataDir during .launch, which isn't supported by playwright - ignoring\"\n      )\n      delete (options as any).userDataDir\n    }\n    const browser = await this.launcher['launch'](options)\n    await this.plugins.dispatchBlocking('onBrowser', browser)\n    await this._bindBrowserEvents(browser)\n    await this.plugins.dispatchBlocking('afterLaunch', browser)\n    return browser\n  }\n\n  public async launchPersistentContext(\n    ...args: Parameters<PlaywrightBrowserLauncher['launchPersistentContext']>\n  ): ReturnType<PlaywrightBrowserLauncher['launchPersistentContext']> {\n    if (!this.launcher.launchPersistentContext) {\n      throw new Error('Launcher does not support \"launchPersistentContext\"')\n    }\n\n    let [userDataDir, options] = args\n    options = { args: [], ...(options || {}) } // Initialize args array\n    debug('launchPersistentContext', options)\n    this.plugins.prepare()\n\n    // Give plugins the chance to modify the options before continuing\n    options =\n      (await this.plugins.dispatchBlocking('beforeLaunch', options)) || options\n\n    const context = await this.launcher['launchPersistentContext'](\n      userDataDir,\n      options\n    )\n    await this.plugins.dispatchBlocking('afterLaunch', context)\n    this._bindBrowserContextEvents(context)\n    return context\n  }\n\n  async connect(\n    wsEndpointOrOptions: string | (pw.ConnectOptions & { wsEndpoint?: string }),\n    wsOptions: pw.ConnectOptions = {}\n  ): ReturnType<PlaywrightBrowserLauncher['connect']> {\n    if (!this.launcher.connect) {\n      throw new Error('Launcher does not support \"connect\"')\n    }\n    this.plugins.prepare()\n\n    // Playwright currently supports two function signatures for .connect\n    let options: pw.ConnectOptions & { wsEndpoint?: string } = {}\n    let wsEndpointAsString = false\n    if (typeof wsEndpointOrOptions === 'object') {\n      options = { ...wsEndpointOrOptions, ...wsOptions }\n    } else {\n      wsEndpointAsString = true\n      options = { wsEndpoint: wsEndpointOrOptions, ...wsOptions }\n    }\n    debug('connect', options)\n\n    // Give plugins the chance to modify the options before launch/connect\n    options =\n      (await this.plugins.dispatchBlocking('beforeConnect', options)) || options\n\n    // Follow call signature of end user\n    const args: any[] = []\n    const wsEndpoint = options.wsEndpoint\n    if (wsEndpointAsString) {\n      delete options.wsEndpoint\n      args.push(wsEndpoint, options)\n    } else {\n      args.push(options)\n    }\n\n    const browser = (await (this.launcher['connect'] as any)(\n      ...args\n    )) as pw.Browser\n\n    await this.plugins.dispatchBlocking('onBrowser', browser)\n    await this._bindBrowserEvents(browser)\n    await this.plugins.dispatchBlocking('afterConnect', browser)\n    return browser\n  }\n\n  async connectOverCDP(\n    wsEndpointOrOptions:\n      | string\n      | (pw.ConnectOverCDPOptions & { endpointURL?: string }),\n    wsOptions: pw.ConnectOverCDPOptions = {}\n  ): ReturnType<PlaywrightBrowserLauncher['connectOverCDP']> {\n    if (!this.launcher.connectOverCDP) {\n      throw new Error(`Launcher does not implement 'connectOverCDP'`)\n    }\n    this.plugins.prepare()\n\n    // Playwright currently supports two function signatures for .connectOverCDP\n    let options: pw.ConnectOverCDPOptions & { endpointURL?: string } = {}\n    let wsEndpointAsString = false\n    if (typeof wsEndpointOrOptions === 'object') {\n      options = { ...wsEndpointOrOptions, ...wsOptions }\n    } else {\n      wsEndpointAsString = true\n      options = { endpointURL: wsEndpointOrOptions, ...wsOptions }\n    }\n    debug('connectOverCDP'), options\n\n    // Give plugins the chance to modify the options before launch/connect\n    options =\n      (await this.plugins.dispatchBlocking('beforeConnect', options)) || options\n\n    // Follow call signature of end user\n    const args: any[] = []\n    const endpointURL = options.endpointURL\n    if (wsEndpointAsString) {\n      delete options.endpointURL\n      args.push(endpointURL, options)\n    } else {\n      args.push(options)\n    }\n\n    const browser = (await (this.launcher['connectOverCDP'] as any)(\n      ...args\n    )) as pw.Browser\n\n    await this.plugins.dispatchBlocking('onBrowser', browser)\n    await this._bindBrowserEvents(browser)\n    await this.plugins.dispatchBlocking('afterConnect', browser)\n    return browser\n  }\n\n  protected async _bindBrowserContextEvents(\n    context: pw.BrowserContext,\n    contextOptions?: pw.BrowserContextOptions\n  ) {\n    debug('_bindBrowserContextEvents')\n    this.plugins.dispatch('onContextCreated', context, contextOptions)\n\n    // Make sure things like `addInitScript` show an effect on the very first page as well\n    context.newPage = ((originalMethod, ctx) => {\n      return async () => {\n        const page = await originalMethod.call(ctx)\n        await page.goto('about:blank')\n        return page\n      }\n    })(context.newPage, context)\n\n    context.on('close', () => {\n      // When using `launchPersistentContext` context closing is the same as browser closing\n      if (!context.browser()) {\n        this.plugins.dispatch('onDisconnected')\n      }\n    })\n    context.on('page', page => {\n      this.plugins.dispatch('onPageCreated', page)\n      page.on('close', () => {\n        this.plugins.dispatch('onPageClose', page)\n      })\n    })\n  }\n\n  protected async _bindBrowserEvents(browser: pw.Browser) {\n    debug('_bindPlaywrightBrowserEvents')\n\n    browser.on('disconnected', () => {\n      this.plugins.dispatch('onDisconnected', browser)\n    })\n\n    // Note: `browser.newPage` will implicitly call `browser.newContext` as well\n    browser.newContext = ((originalMethod, ctx) => {\n      return async (options: pw.BrowserContextOptions = {}) => {\n        const contextOptions: pw.BrowserContextOptions =\n          (await this.plugins.dispatchBlocking(\n            'beforeContext',\n            options,\n            browser\n          )) || options\n        const context = await originalMethod.call(ctx, contextOptions)\n        this._bindBrowserContextEvents(context, contextOptions)\n        return context\n      }\n    })(browser.newContext, browser)\n  }\n}\n\n/**\n * PlaywrightExtra class with additional launcher methods.\n *\n * Augments the class with an instance proxy to pass on methods that are not augmented to the original target.\n *\n */\nexport const PlaywrightExtra = new Proxy(PlaywrightExtraClass, {\n  construct(classTarget, args) {\n    debug(`create instance of ${classTarget.name}`)\n    const result = Reflect.construct(classTarget, args) as PlaywrightExtraClass\n    return new Proxy(result, {\n      get(target, prop) {\n        if (prop in target) {\n          return Reflect.get(target, prop)\n        }\n        debug('proxying property to original launcher: ', prop)\n        return Reflect.get(target.launcher, prop)\n      }\n    })\n  }\n})\n", "import type * as pw from 'playwright-core'\n\nimport { PlaywrightExtra, PlaywrightExtraClass } from './extra'\nimport { PluginList } from './plugins'\nimport { playwright<PERSON>oader as loader } from './helper/loader'\n\nexport { PlaywrightExtra, PlaywrightExtraClass } from './extra'\nexport { PluginList } from './plugins'\n\n/** A playwright browser launcher */\nexport type PlaywrightBrowserLauncher = pw.BrowserType<{}>\n/** A playwright browser launcher with plugin functionality */\nexport type AugmentedBrowserLauncher = PlaywrightExtraClass &\n  PlaywrightBrowserLauncher\n\n/**\n * The minimum shape we expect from a playwright compatible launcher object.\n * We intentionally keep this not strict so other custom or compatible launchers can be used.\n */\nexport interface PlaywrightCompatibleLauncher {\n  connect(...args: any[]): Promise<any>\n  launch(...args: any[]): Promise<any>\n}\n\n/** Our custom module exports */\ninterface ExtraModuleExports {\n  PlaywrightExtra: typeof PlaywrightExtra\n  PlaywrightExtraClass: typeof PlaywrightExtraClass\n  PluginList: typeof PluginList\n  addExtra: typeof addExtra\n  chromium: AugmentedBrowserLauncher\n  firefox: AugmentedBrowserLauncher\n  webkit: AugmentedBrowserLauncher\n}\n\n/** Vanilla playwright module exports */\ntype PlaywrightModuleExports = typeof pw\n\n/**\n * Augment the provided Playwright browser launcher with plugin functionality.\n *\n * Using `addExtra` will always create a fresh PlaywrightExtra instance.\n *\n * @example\n * import playwright from 'playwright'\n * import { addExtra } from 'playwright-extra'\n *\n * const chromium = addExtra(playwright.chromium)\n * chromium.use(plugin)\n *\n * @param launcher - Playwright (or compatible) browser launcher\n */\nexport const addExtra = <Launcher extends PlaywrightCompatibleLauncher>(\n  launcher?: Launcher\n) => new PlaywrightExtra(launcher) as PlaywrightExtraClass & Launcher\n\n/**\n * This object can be used to launch or connect to Chromium with plugin functionality.\n *\n * This default export will behave exactly the same as the regular playwright\n * (just with extra plugin functionality) and can be used as a drop-in replacement.\n *\n * Behind the scenes it will try to require either the `playwright-core`\n * or `playwright` module from the installed dependencies.\n *\n * @note\n * Due to Node.js import caching this will result in a single\n * PlaywrightExtra instance, even when used in different files. If you need multiple\n * instances with different plugins please use `addExtra`.\n *\n * @example\n * // javascript import\n * const { chromium } = require('playwright-extra')\n *\n * // typescript/es6 module import\n * import { chromium } from 'playwright-extra'\n *\n * // Add plugins\n * chromium.use(...)\n */\nexport const chromium = addExtra((loader.loadModule() || {}).chromium)\n/**\n * This object can be used to launch or connect to Firefox with plugin functionality\n * @note This export will always return the same instance, if you wish to use multiple instances with different plugins use `addExtra`\n */\nexport const firefox = addExtra((loader.loadModule() || {}).firefox)\n/**\n * This object can be used to launch or connect to Webkit with plugin functionality\n * @note This export will always return the same instance, if you wish to use multiple instances with different plugins use `addExtra`\n */\nexport const webkit = addExtra((loader.loadModule() || {}).webkit)\n\n// Other playwright module exports we simply re-export with lazy loading\nexport const _android = loader.lazyloadExportOrDie('_android')\nexport const _electron = loader.lazyloadExportOrDie('_electron')\nexport const request = loader.lazyloadExportOrDie('request')\nexport const selectors = loader.lazyloadExportOrDie('selectors')\nexport const devices = loader.lazyloadExportOrDie('devices')\nexport const errors = loader.lazyloadExportOrDie('errors')\n\n/** Playwright with plugin functionality */\nconst moduleExports: ExtraModuleExports & PlaywrightModuleExports = {\n  // custom exports\n  PlaywrightExtra,\n  PlaywrightExtraClass,\n  PluginList,\n  addExtra,\n  chromium,\n  firefox,\n  webkit,\n\n  // vanilla exports\n  _android,\n  _electron,\n  request,\n  selectors,\n  devices,\n  errors\n}\n\nexport default moduleExports\n"], "names": ["debug", "loader"], "mappings": ";;;;;;;;;;;;;AAEA;MACa,MAAM;IACjB,YAAmB,UAAkB,EAAS,YAAsB;QAAjD,eAAU,GAAV,UAAU,CAAQ;QAAS,iBAAY,GAAZ,YAAY,CAAU;KAAI;;;;;;;;;;;IAYjE,mBAAmB,CAA+B,UAAa;QACpE,MAAM,IAAI,GAAG,IAAI,CAAA;QACjB,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CACpC,MAAM,CAAC,mBAAmB,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,IAAS,KAAK;YACrD,IAAI;YACJ,UAAU,MAAW,EAAE,GAAG,IAAW;gBACnC,MAAM,YAAY,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC,UAAU,CAAC,CAAA;gBACvD,MAAM,YAAY,GAAG,YAAmB,CAAA;gBACxC,MAAM,MAAM,GAAK,OAAe,CAAC,IAAI,CAAS,CAC5C,YAAY,IAAI,MAAM,EACtB,GAAG,IAAI,CACR,CAAA;gBACD,OAAO,MAAM,CAAA;aACd;SACF,CAAC,CACH,CAAA;QACD,OAAO,IAAI,KAAK,CAAC,EAAE,EAAE,WAAW,CAAoB,CAAA;KACrD;;IAGM,UAAU;QACf,OAAO,eAAe,CAAe,IAAI,CAAC,YAAY,CAAC,CAAA;KACxD;;IAGM,eAAe;QACpB,MAAM,MAAM,GAAG,eAAe,CAAe,IAAI,CAAC,YAAY,CAAC,CAAA;QAC/D,IAAI,MAAM,EAAE;YACV,OAAO,MAAM,CAAA;SACd;QACD,MAAM,IAAI,CAAC,YAAY,CAAA;KACxB;IAED,IAAW,YAAY;QACrB,MAAM,gBAAgB,GACpB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QACpE,OAAO,IAAI,KAAK,CAAC;IACjB,gBAAgB;;uBAEG,IAAI,CAAC,YAAY;aACnC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC;aAClB,IAAI,CAAC,IAAI,CAAC;;;mEAGoD,gBAAgB;;wCAE3C,gBAAgB;cAC1C,IAAI,CAAC,UAAU,eAAe,IAAI,CAAC,UAAU;GACxD,CAAC,CAAA;KACD;CACF;SAEe,eAAe,CAAqB,YAAsB;IACxE,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE;QAC/B,IAAI;YACF,OAAO,OAAO,CAAC,IAAI,CAAiB,CAAA;SACrC;QAAC,OAAO,CAAC,EAAE;YACV,SAAQ;SACT;KACF;IACD,OAAM;AACR,CAAC;AAED;AACO,MAAM,gBAAgB,GAAG,IAAI,MAAM,CAAY,YAAY,EAAE;IAClE,iBAAiB;IACjB,YAAY;CACb,CAAC;;AClFF,MAAM,KAAK,GAAG,KAAK,CAAC,mCAAmC,CAAC,CAAA;AAqBxD,AAAO,MAAM,gBAAgB,GAAG,CAAC,GAAY;IAC3C,OAAO,SAAS,IAAK,GAAe,CAAA;AACtC,CAAC,CAAA;AACD,AAAO,MAAM,iBAAiB,GAAG,CAAC,GAAY;IAC5C,OAAO,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAK,GAAgB,CAAC,CAAA;AAC3E,CAAC,CAAA;AACD,AAAO,MAAM,mBAAmB,GAAG,CAAC,GAAY;IAC9C,OAAO,YAAY,IAAK,GAAkB,CAAA;AAC5C,CAAC,CAAA;AACD,AAAO,MAAM,iBAAiB,GAAG,CAAC,GAAa;IAC7C,OAAO,CAAC,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,CAAE,GAAW,CAAC,YAAY,CAAA;AACxE,CAAC,CAAA;AAED,MAAM,KAAK,GAAG;IACZ,YAAY,EAAE,IAAI,GAAG,EAAsC;IAC3D,UAAU,EAAE;QACV,IAAI,EAAE,IAAI,GAAG,EAAqC;QAClD,OAAO,EAAE,IAAI,GAAG,EAA6B;KAC9C;CACF,CAAA;AAED;AACA,SAAgB,kBAAkB,CAEhC,MAAa;IACb,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QACzC,OAAO,MAAM,CAAA;KACd;IACD,IAAI,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;QAClC,OAAO,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAU,CAAA;KAC/C;IACD,IAAI,iBAAiB,CAAC,MAAM,CAAC,EAAE;QAC7B,OAAO,MAAM,CAAA;KACd;IACD,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;IACpD,IAAI,gBAAgB,CAAC,MAAM,CAAC,IAAI,iBAAiB,CAAC,MAAM,CAAC,EAAE;QACzD,MAAM,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAA;QACnC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QACpC,OAAO,IAAa,CAAA;KACrB;IACD,IAAI,mBAAmB,CAAC,MAAM,CAAC,EAAE;QAC/B,MAAM,IAAI,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAA;QACtC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QACpC,OAAO,IAAa,CAAA;KACrB;IACD,KAAK,CAAC,0BAA0B,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAA;IAC1D,OAAO,MAAM,CAAA;AACf,CAAC;AAED;AACA,MAAM,cAAc,GAAG;IACrB,IAAI,EAAE,OAAO,GAAG,IAAW;QACzB,KAAK,CAAC,yBAAyB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;KAC/C;IACD,EAAE,EAAE,CAAC,GAAG,IAAW;QACjB,KAAK,CAAC,yBAAyB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;KAC7C;CACe,CAAA;AAElB,AAAO,eAAe,iBAAiB,CAAC,IAAwB;IAC9D,IAAI,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IAC7C,IAAI,OAAO,EAAE;QACX,KAAK,CAAC,iCAAiC,CAAC,CAAA;QACxC,OAAO,OAAO,CAAA;KACf;IACD,KAAK,CAAC,4BAA4B,CAAC,CAAA;IACnC,MAAM,OAAO,GAAG,iBAAiB,CAAC,IAAI,CAAC;UACnC,IAAI,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE;UACrB,IAAI,CAAC,OAAO,EAAE,CAAA;IAClB,IAAI;QACF,OAAO,GAAG,MAAM,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;QAC3C,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;QACxC,OAAO,OAAO,CAAA;KACf;IAAC,OAAO,GAAQ,EAAE;QACjB,KAAK,CAAC,kDAAkD,EAAE,GAAG,CAAC,OAAO,CAAC,CAAA;QACtE,KAAK,CACH,kHAAkH,CACnH,CAAA;KACF;IACD,OAAO,cAAc,CAAA;AACvB,CAAC;AAED,AAAO,eAAe,oBAAoB,CAAC,OAAmB;IAC5D,IAAI,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;IACnD,IAAI,OAAO,EAAE;QACX,KAAK,CAAC,oCAAoC,CAAC,CAAA;QAC3C,OAAO,OAAO,CAAA;KACf;IACD,KAAK,CAAC,+BAA+B,CAAC,CAAA;IACtC,IAAI;QACF,OAAO,GAAG,MAAM,OAAO,CAAC,oBAAoB,EAAE,CAAA;QAC9C,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAC9C,OAAO,OAAO,CAAA;KACf;IAAC,OAAO,GAAQ,EAAE;QACjB,KAAK,CAAC,qDAAqD,EAAE,GAAG,CAAC,OAAO,CAAC,CAAA;QACzE,KAAK,CACH,qHAAqH,CACtH,CAAA;KACF;IACD,OAAO,cAAc,CAAA;AACvB,CAAC;AAED,SAAgB,cAAc,CAAC,IAAwB;IACrD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IACxD,MAAM,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE;QAC3B,GAAG,CAAC,MAAM,EAAE,IAAI;YACd,IAAI,IAAI,KAAK,cAAc,IAAI,IAAI,KAAK,cAAc,EAAE;gBACtD,OAAO,IAAI,CAAA;aACZ;YACD,KAAK,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;YAChC,IAAI,IAAI,KAAK,SAAS,EAAE;gBACtB,OAAO,OAAO;oBACZ,IAAI,EAAE,OAAO,MAAc,EAAE,MAAW;wBACtC,MAAM,OAAO,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,CAAA;wBAC7C,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,MAAa,EAAE,MAAM,CAAC,CAAA;qBACjD;oBACD,EAAE,EAAE,CAAC,KAAa,EAAE,QAAa;wBAC/B,iBAAiB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO;4BAClC,OAAO,CAAC,EAAE,CAAC,KAAY,EAAE,QAAQ,CAAC,CAAA;yBACnC,CAAC,CAAA;qBACH;iBACF,CAAC,CAAA;aACH;YACD,IAAI,IAAI,KAAK,cAAc,EAAE;gBAC3B,OAAO,OAAO,OAAgB;oBAC5B,MAAM,OAAO,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,CAAA;oBAC7C,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE;wBAC7C,OAAO;qBACR,CAAC,CAAA;iBACH,CAAA;aACF;YACD,IAAI,IAAI,KAAK,cAAc,EAAE;gBAC3B,OAAO,OAAO,SAAiB,EAAE,iBAAuB;oBACtD,MAAM,OAAO,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,CAAA;oBAC7C,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,gCAAgC,EAAE;wBAC1D,SAAS;wBACT,iBAAiB;qBAClB,CAAC,CAAA;iBACH,CAAA;aACF;YACD,IAAI,IAAI,KAAK,SAAS,EAAE;gBACtB,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;oBAC1B,OAAO;wBACL,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,CAAA;wBACtC,IAAI,CAAC,OAAO,EAAE;4BACZ,KAAK,CACH,4EAA4E,CAC7E,CAAA;;4BAED,OAAO,GAAG,IAAW,CAAA;yBACtB;wBACD,OAAO,kBAAkB,CAAC,OAAO,CAAC,CAAA;qBACnC,CAAA;iBACF;aACF;YACD,IAAI,IAAI,KAAK,uBAAuB,EAAE;gBACpC,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE;oBAC1B,OAAO,gBAAgB,YAA0B,EAAE,GAAG,IAAW;wBAC/D,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;qBACvD,CAAA;iBACF;aACF;;YAED,IAAI,IAAI,KAAK,WAAW,EAAE;gBACxB,OAAO,OAAO,OAAgB;oBAC5B,MAAM,OAAO,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,CAAA;oBAC7C,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAA;oBACrD,OAAO,IAAI,CAAC,SAAS,CAAA;iBACtB,CAAA;aACF;YACD,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;SACjC;KACF,CAAC,CAAA;IACF,OAAO,IAAI,CAAA;AACb,CAAC;AAED,SAAgB,iBAAiB,CAAC,OAAmB;IACnD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IACxD,MAAM,IAAI,GAAG,IAAI,KAAK,CAAC,OAAO,EAAE;QAC9B,GAAG,CAAC,MAAM,EAAE,IAAI;YACd,IAAI,IAAI,KAAK,cAAc,IAAI,IAAI,KAAK,cAAc,EAAE;gBACtD,OAAO,IAAI,CAAA;aACZ;YACD,KAAK,CAAC,eAAe,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;YACnC,IAAI,IAAI,KAAK,OAAO,EAAE;gBACpB,OAAO,MACL,OAAO;qBACJ,QAAQ,EAAE;qBACV,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;aACnE;YACD,IAAI,IAAI,KAAK,WAAW,EAAE;gBACxB,OAAO;oBACL,MAAM,OAAO,GAAG,MAAM,oBAAoB,CAAC,OAAO,CAAC,CAAA;oBACnD,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAA;oBACrD,OAAO,IAAI,CAAC,SAAS,CAAA;iBACtB,CAAA;aACF;YACD,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;SACjC;KACF,CAAC,CAAA;IACF,OAAO,IAAI,CAAA;AACb,CAAC;;AC9ND,MAAMA,OAAK,GAAG,KAAK,CAAC,0BAA0B,CAAC,CAAA;AAU/C,MAGa,UAAU;IAMrB;QALiB,aAAQ,GAAa,EAAE,CAAA;QACvB,wBAAmB,GAAqB,IAAI,GAAG,EAAE,CAAA;QACjD,0BAAqB,GACpC,IAAI,GAAG,EAAE,CAAA;KAEK;;;;IAKhB,IAAW,IAAI;QACb,OAAO,IAAI,CAAC,QAAQ,CAAA;KACrB;;;;IAKD,IAAW,KAAK;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAA;KACtC;;;;;;;IAQM,GAAG,CAAC,MAAc;;QACvB,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE;YACvC,OAAO,KAAK,CAAA;SACb;QACD,IAAI,CAAC,CAAC,MAAM,CAAC,kBAAkB,EAAE;YAC/B,MAAM,CAAC,kBAAkB,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC,CAAA;SACvD;;QAED,IAAI,CAAC,CAAC,MAAM,CAAC,0BAA0B,EAAE;YACvC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAA;SACjE;QACD,IAAI,MAAA,MAAM,CAAC,YAAY,0CAAE,GAAG,CAAC,iBAAiB,CAAC,EAAE;YAC/C,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SACpD;QACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC1B,OAAO,IAAI,CAAA;KACZ;;IAGS,qBAAqB,CAAC,MAAc;QAC5C,IACE,CAAC,MAAM;YACP,OAAO,MAAM,KAAK,QAAQ;YAC1B,CAAC,MAAM,CAAC,uBAAuB,EAC/B;YACA,OAAO,CAAC,KAAK,CACX,qEAAqE,EACrE,MAAM,CACP,CAAA;YACD,OAAO,KAAK,CAAA;SACb;QACD,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;YAChB,OAAO,CAAC,KAAK,CACX,qDAAqD,EACrD,MAAM,CACP,CAAA;YACD,OAAO,KAAK,CAAA;SACb;QACD,OAAO,IAAI,CAAA;KACZ;;IAGM,aAAa,CAAC,MAAc,EAAE,MAAwB,EAAE,GAAU;QACvE,OAAO,CAAC,IAAI,CACV,qCAAqC,MAAM,gBAAgB,MAAM,CAAC,IAAI,IAAI,EAC1E,GAAG,CACJ,CAAA;KACF;;;;;;;;;;IAWM,qBAAqB,CAAC,cAAsB,EAAE,IAAS;QAC5D,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;QAClD,OAAO,IAAI,CAAA;KACZ;;;;;;;;;;IAWM,uBAAuB,CAC5B,cAAsB,EACtB,YAAoC;QAEpC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,cAAc,EAAE,YAAY,CAAC,CAAA;QAC5D,OAAO,IAAI,CAAA;KACZ;;;;;IAMM,OAAO;QACZ,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAC1B,IAAI,CAAC,KAAK,EAAE,CAAA;KACb;;IAGS,cAAc,CAAC,UAA4B;QACnD,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM;;YAEhC,IACE,CAAC,CAAC,MAAM,CAAC,kBAAkB;gBAC3B,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,EACxC;gBACA,OAAO,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;aACtD;YACD,OAAO,UAAU,IAAI,MAAM,CAAA;SAC5B,CAAC,CAAA;KACH;;IAGS,2BAA2B,CACnC,MAAc,EACd,MAAe,EACf,IAAyC;QAEzC,MAAM,UAAU,GAAG,MAAM,CAAC,uBAAuB,IAAI,CAAC,MAAM,CAAC,eAAe,CAAA;QAC5E,MAAM,eAAe,GAAuB;YAC1C,WAAW;YACX,eAAe;YACf,aAAa;YACb,cAAc;YACd,aAAa;SACd,CAAA;QACD,MAAM,aAAa,GAAG,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QACtD,IAAI,CAAC,UAAU,IAAI,CAAC,aAAa,EAAE;YACjC,OAAO,IAAI,CAAA;SACZ;QACDA,OAAK,CAAC,6BAA6B,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;QACzD,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,kBAAkB,CAAC,GAAU,CAAC,CAAC,CAEzD,CAAA;KACF;;;;;;;;;;;IAYM,QAAQ,CACb,MAAe,EACf,GAAG,IAAyC;;QAE5C,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAC3CA,OAAK,CAAC,UAAU,EAAE,MAAM,EAAE;YACxB,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;YACzB,gBAAgB,EAAE,OAAO,CAAC,MAAM;SACjC,CAAC,CAAA;QACF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC5B,IAAI;gBACF,IAAI,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;gBACxE,MAAM,MAAM,GAAG,MAAA,MAAA,MAAM,CAAC,MAAM,CAAC,0CAAE,WAAW,0CAAE,IAAI,CAAA;gBAChDA,OAAK,CAAC,oBAAoB,EAAE;oBAC1B,MAAM,EAAE,MAAM,CAAC,IAAI;oBACnB,MAAM;oBACN,MAAM;iBACP,CAAC,CAAA;gBACF,IAAI,MAAM,KAAK,eAAe,EAAE;oBAC9B,CAAC;oBAAC,MAAM,CAAC,MAAM,CAAS,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,GAAQ,KAC/C,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CACxC,CAAA;iBACF;qBAAM;oBACL,CAAC;oBAAC,MAAM,CAAC,MAAM,CAAS,CAAC,GAAG,IAAI,CAAC,CAAA;iBAClC;aACF;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,GAAU,CAAC,CAAA;aAC/C;SACF;KACF;;;;;;;;;;;;;;;;;IAkBM,MAAM,gBAAgB,CAC3B,MAAe,EACf,GAAG,IAAyC;QAE5C,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAC3CA,OAAK,CAAC,kBAAkB,EAAE,MAAM,EAAE;YAChC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;YACzB,gBAAgB,EAAE,OAAO,CAAC,MAAM;SACjC,CAAC,CAAA;QAEF,IAAI,QAAQ,GAAQ,IAAI,CAAA;QACxB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC5B,IAAI;gBACF,IAAI,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;gBACxE,QAAQ,GAAG,MAAO,MAAM,CAAC,MAAM,CAAS,CAAC,GAAG,IAAI,CAAC,CAAA;;gBAEjD,IAAI,QAAQ,KAAK,SAAS,EAAE;oBAC1B,IAAI,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAA;iBACnB;aACF;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,GAAU,CAAC,CAAA;gBAC9C,OAAO,QAAQ,CAAA;aAChB;SACF;QACD,OAAO,QAAQ,CAAA;KAChB;;;;;;;;IASS,KAAK;QACbA,OAAK,CAAC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ;aAC1B,MAAM,CAAC,CAAC,cAAI,OAAA,MAAA,CAAC,CAAC,YAAY,0CAAE,GAAG,CAAC,SAAS,CAAC,CAAA,EAAA,CAAC;aAC3C,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAA;QACnB,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;YAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAA;YAC3D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;SACtD;QACDA,OAAK,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;KACjC;;;;;;;;;;;;;;IAeS,OAAO,CAAC,IAAa;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ;aACvB,MAAM,CAAC,CAAC,CAAM,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;aAC5B,GAAG,CAAC,CAAC,CAAM,MAAM,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;aAC5D,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAA;QAC7C,OAAO,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAM,KAAK,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,CAAA;KAC9D;;;;IAKS,oBAAoB;QAC5BA,OAAK,CAAC,sBAAsB,CAAC,CAAA;QAC7B,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACvC,IAAI,CAAC,QAAQ;aACV,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;aAC5C,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;aACrC,OAAO,CAAC,MAAM;YACZ,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC;gBAC/BA,OAAK,CAAC,MAAM,CAAC,IAAI,EAAE,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAA;gBACnD,IAAI,CAAC,GAAG,CAAC,CAAW,CAAC,CAAA;aACtB,CAAC,CAAA;SACH,CAAC,CAAA;KACL;;;;;;;;;;IAWS,yBAAyB;QACjCA,OAAK,CAAC,2BAA2B,CAAC,CAAA;;QAGlC,MAAM,sBAAsB,GAAG,CAC7B,UAAkB,EAClB,cAAsB;;YAGtB,IAAI,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;gBAClD,OAAO,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,cAAc,CAAiB,CAAA;aACtE;YAED,MAAM,gBAAgB,GAAG,CAAC,yBAAyB,CAAC,CAAA;YACpD,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,IACpD,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,CAClC,CAAA;YACD,MAAM,YAAY,GAAa,EAAE,CAAA;;YAEjC,IAAI,CAAC,iBAAiB,EAAE;gBACtB,YAAY,CAAC,IAAI,CACf,GAAG,gBAAgB,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,GAAG,cAAc,CAAC,CAC3D,CAAA;aACF;;YAED,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;YACjC,MAAM,YAAY,GAAG,eAAe,CAAe,YAAY,CAAC,CAAA;YAChE,IAAI,YAAY,EAAE;gBAChB,OAAO,YAAY,CAAA;aACpB;YAED,MAAM,WAAW,GAAG;cACZ,UAAU,aAAa,cAAc;;;EAGjD,YAAY;iBACX,GAAG,CAAC,WAAW,IAAI,YAAY,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;iBAC3D,IAAI,CAAC,UAAU,CAAC;;;;;;OAMZ,CAAA;YACD,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YACzB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAA;SAC/C,CAAA;QAED,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC/C,MAAM,kCAAkC,GAAG,CAAC,EAC1C,IAAI,EAAE,UAAU,EAChB,YAAY,EACL;YACP,IAAI,CAAC,YAAY,EAAE;gBACjB,OAAM;aACP;YACD,MAAM,iBAAiB,GAAG,CAAC,cAAsB,EAAE,IAAU;gBAC3D,MAAM,YAAY,GAAG,sBAAsB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAA;gBACvE,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAA;gBACjE,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,CAAA;gBACjC,IAAI,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;oBACxCA,OAAK,CAAC,UAAU,EAAE,+BAA+B,EAAE,MAAM,CAAC,IAAI,CAAC,CAAA;oBAC/D,OAAM;iBACP;gBACD,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;gBACpCA,OAAK,CAAC,UAAU,EAAE,2BAA2B,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;gBACjE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;gBAChB,OAAO,kCAAkC,CAAC,MAAM,CAAC,CAAA;aAClD,CAAA;YAED,IAAI,YAAY,YAAY,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;gBAC9D,OAAO,CAAC,GAAG,YAAY,CAAC,CAAC,OAAO,CAAC,cAAc,IAC7C,iBAAiB,CAAC,cAAc,CAAC,CAClC,CAAA;aACF;YACD,IAAI,YAAY,YAAY,GAAG,EAAE;;gBAE/B,OAAO,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;aAC/D;SACF,CAAA;QACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAA;KACtD;;;;;IAMS,mBAAmB;QAC3BA,OAAK,CAAC,qBAAqB,CAAC,CAAA;QAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAC3B,IAAI,CAAC,yBAAyB,EAAE,CAAA;KACjC;CACF;;AC1ZD,MAAMA,OAAK,GAAG,KAAK,CAAC,kBAAkB,CAAC,CAAA;AAKvC,AAeA;;;AAGA,MAAa,oBAAoB;IAI/B,YAAoB,SAA8C;QAA9C,cAAS,GAAT,SAAS,CAAqC;QAChE,IAAI,CAAC,OAAO,GAAG,IAAI,UAAU,EAAE,CAAA;KAChC;;;;;;;;;;;;;;;;IAiBM,GAAG,CAAC,MAAwB;QACjC,MAAM,OAAO,GAAG,MAAM,IAAI,MAAM,IAAI,MAAM,CAAA;QAC1C,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAA;SACvD;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAgB,CAAC,EAAE;YACtCA,OAAK,CAAC,mBAAmB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAA;SACxC;QACD,OAAO,IAAI,CAAA;KACZ;;;;;;;;;;;IAYD,IAAW,QAAQ;QACjB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,gBAAgB,CAAC,YAAY,CAAA;SACpC;QACD,OAAO,IAAI,CAAC,SAAS,CAAA;KACtB;IAEM,MAAM,MAAM,CACjB,GAAG,IAAqD;QAExD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA;SACtD;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAA;QACpB,OAAO,mBAAK,IAAI,EAAE,EAAE,KAAM,OAAO,IAAI,EAAE,EAAG,CAAA;QAC1CA,OAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;QACxB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAA;;QAGtB,OAAO;YACL,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,cAAc,EAAE,OAAO,CAAC,KAAK,OAAO,CAAA;QAE3EA,OAAK,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAA;QACrC,IAAI,aAAa,IAAI,OAAO,EAAE;YAC5BA,OAAK,CACH,6FAA6F,CAC9F,CAAA;YACD,OAAQ,OAAe,CAAC,WAAW,CAAA;SACpC;QACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAA;QACtD,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;QACzD,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAA;QACtC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAA;QAC3D,OAAO,OAAO,CAAA;KACf;IAEM,MAAM,uBAAuB,CAClC,GAAG,IAAsE;QAEzE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EAAE;YAC1C,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAA;SACvE;QAED,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,GAAG,IAAI,CAAA;QACjC,OAAO,mBAAK,IAAI,EAAE,EAAE,KAAM,OAAO,IAAI,EAAE,EAAG,CAAA;QAC1CA,OAAK,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAA;QACzC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAA;;QAGtB,OAAO;YACL,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,cAAc,EAAE,OAAO,CAAC,KAAK,OAAO,CAAA;QAE3E,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAC5D,WAAW,EACX,OAAO,CACR,CAAA;QACD,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAA;QAC3D,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAA;QACvC,OAAO,OAAO,CAAA;KACf;IAED,MAAM,OAAO,CACX,mBAA2E,EAC3E,YAA+B,EAAE;QAEjC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAA;SACvD;QACD,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAA;;QAGtB,IAAI,OAAO,GAAgD,EAAE,CAAA;QAC7D,IAAI,kBAAkB,GAAG,KAAK,CAAA;QAC9B,IAAI,OAAO,mBAAmB,KAAK,QAAQ,EAAE;YAC3C,OAAO,mCAAQ,mBAAmB,GAAK,SAAS,CAAE,CAAA;SACnD;aAAM;YACL,kBAAkB,GAAG,IAAI,CAAA;YACzB,OAAO,mBAAK,UAAU,EAAE,mBAAmB,IAAK,SAAS,CAAE,CAAA;SAC5D;QACDA,OAAK,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;;QAGzB,OAAO;YACL,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,OAAO,CAAC,KAAK,OAAO,CAAA;;QAG5E,MAAM,IAAI,GAAU,EAAE,CAAA;QACtB,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAA;QACrC,IAAI,kBAAkB,EAAE;YACtB,OAAO,OAAO,CAAC,UAAU,CAAA;YACzB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;SAC/B;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;SACnB;QAED,MAAM,OAAO,IAAI,MAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAS,CACtD,GAAG,IAAI,CACR,CAAe,CAAA;QAEhB,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;QACzD,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAA;QACtC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAA;QAC5D,OAAO,OAAO,CAAA;KACf;IAED,MAAM,cAAc,CAClB,mBAEyD,EACzD,YAAsC,EAAE;QAExC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;YACjC,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAA;SAChE;QACD,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAA;;QAGtB,IAAI,OAAO,GAAwD,EAAE,CAAA;QACrE,IAAI,kBAAkB,GAAG,KAAK,CAAA;QAC9B,IAAI,OAAO,mBAAmB,KAAK,QAAQ,EAAE;YAC3C,OAAO,mCAAQ,mBAAmB,GAAK,SAAS,CAAE,CAAA;SACnD;aAAM;YACL,kBAAkB,GAAG,IAAI,CAAA;YACzB,OAAO,mBAAK,WAAW,EAAE,mBAAmB,IAAK,SAAS,CAAE,CAAA;SAC7D;QACDA,OAAK,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAA;;QAGhC,OAAO;YACL,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,OAAO,CAAC,KAAK,OAAO,CAAA;;QAG5E,MAAM,IAAI,GAAU,EAAE,CAAA;QACtB,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;QACvC,IAAI,kBAAkB,EAAE;YACtB,OAAO,OAAO,CAAC,WAAW,CAAA;YAC1B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;SAChC;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;SACnB;QAED,MAAM,OAAO,IAAI,MAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAS,CAC7D,GAAG,IAAI,CACR,CAAe,CAAA;QAEhB,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;QACzD,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAA;QACtC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAA;QAC5D,OAAO,OAAO,CAAA;KACf;IAES,MAAM,yBAAyB,CACvC,OAA0B,EAC1B,cAAyC;QAEzCA,OAAK,CAAC,2BAA2B,CAAC,CAAA;QAClC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,kBAAkB,EAAE,OAAO,EAAE,cAAc,CAAC,CAAA;;QAGlE,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG;YACrC,OAAO;gBACL,MAAM,IAAI,GAAG,MAAM,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBAC3C,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;gBAC9B,OAAO,IAAI,CAAA;aACZ,CAAA;SACF,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAE5B,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE;;YAElB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE;gBACtB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAA;aACxC;SACF,CAAC,CAAA;QACF,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI;YACrB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;YAC5C,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE;gBACf,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,CAAA;aAC3C,CAAC,CAAA;SACH,CAAC,CAAA;KACH;IAES,MAAM,kBAAkB,CAAC,OAAmB;QACpDA,OAAK,CAAC,8BAA8B,CAAC,CAAA;QAErC,OAAO,CAAC,EAAE,CAAC,cAAc,EAAE;YACzB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAA;SACjD,CAAC,CAAA;;QAGF,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG;YACxC,OAAO,OAAO,UAAoC,EAAE;gBAClD,MAAM,cAAc,GAClB,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAClC,eAAe,EACf,OAAO,EACP,OAAO,CACR,KAAK,OAAO,CAAA;gBACf,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,cAAc,CAAC,CAAA;gBAC9D,IAAI,CAAC,yBAAyB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAA;gBACvD,OAAO,OAAO,CAAA;aACf,CAAA;SACF,EAAE,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;KAChC;CACF;AAED;;;;;;AAMA,MAAa,eAAe,GAAG,IAAI,KAAK,CAAC,oBAAoB,EAAE;IAC7D,SAAS,CAAC,WAAW,EAAE,IAAI;QACzBA,OAAK,CAAC,sBAAsB,WAAW,CAAC,IAAI,EAAE,CAAC,CAAA;QAC/C,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAyB,CAAA;QAC3E,OAAO,IAAI,KAAK,CAAC,MAAM,EAAE;YACvB,GAAG,CAAC,MAAM,EAAE,IAAI;gBACd,IAAI,IAAI,IAAI,MAAM,EAAE;oBAClB,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;iBACjC;gBACDA,OAAK,CAAC,0CAA0C,EAAE,IAAI,CAAC,CAAA;gBACvD,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;aAC1C;SACF,CAAC,CAAA;KACH;CACF,CAAC;;AChQF;;;;;;;;;;;;;;AAcA,MAAa,QAAQ,GAAG,CACtB,QAAmB,KAChB,IAAI,eAAe,CAAC,QAAQ,CAAoC,CAAA;AAErE;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,MAAa,QAAQ,GAAG,QAAQ,CAAC,CAACC,gBAAM,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,QAAQ,CAAC,CAAA;AACtE;;;;AAIA,MAAa,OAAO,GAAG,QAAQ,CAAC,CAACA,gBAAM,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,OAAO,CAAC,CAAA;AACpE;;;;AAIA,MAAa,MAAM,GAAG,QAAQ,CAAC,CAACA,gBAAM,CAAC,UAAU,EAAE,IAAI,EAAE,EAAE,MAAM,CAAC,CAAA;AAElE;AACA,MAAa,QAAQ,GAAGA,gBAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAA;AAC9D,MAAa,SAAS,GAAGA,gBAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAA;AAChE,MAAa,OAAO,GAAGA,gBAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAA;AAC5D,MAAa,SAAS,GAAGA,gBAAM,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAA;AAChE,MAAa,OAAO,GAAGA,gBAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAA;AAC5D,MAAa,MAAM,GAAGA,gBAAM,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAA;AAE1D;AACA,MAAM,aAAa,GAAiD;;IAElE,eAAe;IACf,oBAAoB;IACpB,UAAU;IACV,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,MAAM;;IAGN,QAAQ;IACR,SAAS;IACT,OAAO;IACP,SAAS;IACT,OAAO;IACP,MAAM;CACP,CAAA;;;;;;;;;;;;;;;;;;;;;;;;"}