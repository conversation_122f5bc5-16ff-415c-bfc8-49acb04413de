# Human Interaction AI Model

An advanced AI model that learns from recorded human interaction data to generate realistic browser automation patterns. This model can produce human-like mouse movements, typing patterns, and scrolling behaviors that are virtually indistinguishable from real human interactions.

## Features

### 🎯 **Multi-Modal Learning**
- **Mouse Movement**: Learns natural cursor trajectories with realistic acceleration/deceleration
- **Typing Patterns**: Captures human typing rhythms, including errors and corrections
- **Scrolling Behavior**: Models human scrolling patterns with hesitations and overshooting

### 🧠 **Advanced Neural Architecture**
- **LSTM Networks**: Captures temporal dependencies in human behavior
- **Attention Mechanisms**: Focuses on important interaction patterns
- **Feature Engineering**: Extracts meaningful patterns from raw interaction data

### 🎭 **Human-like Characteristics**
- **Natural Movement**: Bezier curves with micro-movements and realistic timing
- **Error Simulation**: Realistic typos and correction patterns
- **Fatigue Modeling**: Simulates user fatigue over extended sessions
- **Anti-Detection**: Sophisticated patterns to avoid bot detection

### ⚡ **High Performance**
- **GPU Acceleration**: CUDA support for fast training and inference
- **Efficient Generation**: Real-time interaction generation
- **Scalable Architecture**: Handles large datasets efficiently

## Installation

### Prerequisites
```bash
# Python 3.8 or higher
python --version

# Install required packages
pip install torch torchvision torchaudio
pip install numpy matplotlib seaborn scikit-learn
pip install asyncio logging dataclasses
```

### Setup
```bash
# Clone or download the project
cd ai_model/

# Install dependencies
pip install -r requirements.txt

# Create necessary directories
mkdir -p saved_models training_plots test_results
```

## Quick Start

### 1. Training the Model

```bash
# Basic training with default settings
python train_model.py

# Advanced training with custom parameters
python train_model.py \
    --data-dir ../data/ \
    --epochs 150 \
    --batch-size 64 \
    --learning-rate 0.0005 \
    --visualize \
    --save-dir ./saved_models/
```

### 2. Testing the Model

```bash
# Test the trained model
python test_model.py --visualize --benchmark

# Test with custom configuration
python test_model.py \
    --model-dir ./saved_models/ \
    --output-dir ./test_results/ \
    --num-tests 20 \
    --visualize
```

### 3. Using the Model in Code

```python
from ai_model.human_interaction_ai import HumanInteractionAI, InteractionConfig
from ai_model.interaction_generator import HumanInteractionGenerator

# Initialize the AI model
config = InteractionConfig()
ai_model = HumanInteractionAI(config)
ai_model.load_models('./saved_models/')

# Create interaction generator
generator = HumanInteractionGenerator(ai_model)

# Generate human-like mouse movement
mouse_path = generator.generate_mouse_path((0, 0), (500, 300))
print(f"Generated path with {len(mouse_path.points)} points")

# Generate human-like typing
typing_sequence = generator.generate_typing_sequence("Hello World!")
print(f"Generated typing with {len(typing_sequence.keys)} keystrokes")

# Generate human-like scrolling
scroll_sequence = generator.generate_scroll_sequence(1000, 0)
print(f"Generated scroll with {len(scroll_sequence.scroll_deltas)} events")
```

## Model Architecture

### Neural Network Components

#### 1. Mouse Movement LSTM
```
Input Features (11): [x, y, dx, dy, distance, velocity, acceleration, angle, curvature, time_delta, timestamp]
↓
LSTM Layers (2 × 128 hidden units) + Attention
↓
Fully Connected Layers (128 → 64 → 32 → 2)
↓
Output: [next_x, next_y]
```

#### 2. Typing Pattern LSTM
```
Input Features (7): [event_type, key_code, time_since_last, is_correct, error_type, text_length, timestamp]
↓
LSTM Layers (2 × 96 hidden units)
↓
Fully Connected Layers (96 → 48 → 24 → 3)
↓
Output: [next_key_time, error_probability, correction_time]
```

#### 3. Scrolling Pattern LSTM
```
Input Features (8): [scroll_y, scroll_delta, velocity, acceleration, is_scroll_stop, is_point_reached, time_since_last, timestamp]
↓
LSTM Layers (2 × 96 hidden units)
↓
Fully Connected Layers (96 → 48 → 24 → 2)
↓
Output: [scroll_delta, timing]
```

### Feature Engineering

The model extracts sophisticated features from raw interaction data:

- **Movement Dynamics**: Velocity, acceleration, curvature, direction changes
- **Temporal Patterns**: Inter-event intervals, reaction times, pause durations
- **Error Patterns**: Typo types, correction delays, error frequencies
- **Context Awareness**: Text position, scroll position, session duration

## Configuration Options

### InteractionConfig Parameters

```python
@dataclass
class InteractionConfig:
    # Mouse settings
    mouse_smoothness: float = 0.8      # 0-1, higher = smoother curves
    mouse_speed_variance: float = 0.3   # Speed variation factor
    mouse_error_rate: float = 0.05      # Probability of overshoot
    
    # Typing settings
    typing_speed_wpm: float = 65.0      # Base typing speed
    typing_error_rate: float = 0.02     # Typo probability
    typing_correction_delay: float = 0.5 # Correction reaction time
    
    # Scrolling settings
    scroll_smoothness: float = 0.7      # Scroll smoothness factor
    scroll_overshoot_rate: float = 0.1  # Overshoot probability
    
    # Timing settings
    reaction_time_base: float = 0.2     # Base reaction time
    reaction_time_variance: float = 0.1 # Reaction time variation
    
    # Anti-detection settings
    add_micro_movements: bool = True    # Add subtle movements
    randomize_timing: bool = True       # Add timing variations
    simulate_fatigue: bool = True       # Model user fatigue
```

## Integration with Browser Automation

### NoDriver Integration

```python
from ai_model.interaction_generator import NoDriverIntegration

# Initialize integration
integration = NoDriverIntegration(generator)

# Use with nodriver
async def automate_with_human_behavior(driver):
    # Human-like mouse movement
    await integration.human_move_to(driver, 500, 300)
    
    # Human-like typing
    await integration.human_type(driver, "Hello World!")
    
    # Human-like scrolling
    await integration.human_scroll_to(driver, 1000)
```

### Custom Integration

```python
# Generate interactions and apply to your automation framework
mouse_path = generator.generate_mouse_path((0, 0), (500, 300))

# Apply to your automation tool
for i, (point, timing) in enumerate(zip(mouse_path.points, mouse_path.timings)):
    if i > 0:
        await asyncio.sleep(timing - mouse_path.timings[i-1])
    await your_driver.move_mouse(point[0], point[1])
```

## Training Data Requirements

### Data Format
The model expects JSON files with the following structure:
```json
{
    "test_type": "mouse_movement|typing|scrolling",
    "test_id": "unique_identifier",
    "timestamp_start": 0,
    "timestamp_end": 5000,
    "settings": {...},
    "events": [
        {
            "type": "mousemove|keydown|scroll|...",
            "timestamp": 100,
            "x": 150,
            "y": 200,
            ...
        }
    ]
}
```

### Data Quality Guidelines
- **Minimum Sequences**: 50+ sequences per interaction type
- **Sequence Length**: 10+ events per mouse sequence, 5+ per typing/scroll
- **Diversity**: Various movement distances, text types, scroll targets
- **Quality**: Clean, realistic human interactions without automation artifacts

## Performance Metrics

### Training Metrics
- **Loss Convergence**: MSE loss for coordinate/timing prediction
- **Validation Accuracy**: Performance on held-out data
- **Feature Importance**: Analysis of learned patterns

### Human-likeness Metrics
- **Movement Smoothness**: Velocity variance and trajectory analysis
- **Timing Realism**: Inter-event interval distributions
- **Error Patterns**: Realistic mistake and correction frequencies
- **Detection Resistance**: Ability to bypass bot detection systems

## Advanced Features

### Fatigue Simulation
The model simulates user fatigue over time:
- Slower movements after extended use
- Increased error rates with session duration
- Longer reaction times as fatigue increases

### Context Adaptation
- Adjusts behavior based on task complexity
- Different patterns for different interaction types
- Learns from user-specific patterns when available

### Anti-Detection Capabilities
- Micro-movements that mimic natural hand tremor
- Realistic timing variations
- Human-like error and correction patterns
- Adaptive behavior to avoid detection patterns

## Troubleshooting

### Common Issues

#### 1. CUDA Out of Memory
```bash
# Reduce batch size
python train_model.py --batch-size 16

# Or use CPU
export CUDA_VISIBLE_DEVICES=""
```

#### 2. No Training Data Found
```bash
# Check data directory
ls -la data/
# Ensure JSON files exist with correct format
```

#### 3. Poor Generation Quality
```bash
# Train for more epochs
python train_model.py --epochs 200

# Increase model complexity (edit model architecture)
# Collect more diverse training data
```

### Performance Optimization

#### Training Optimization
- Use GPU acceleration when available
- Increase batch size for faster training
- Use mixed precision training for memory efficiency
- Implement gradient accumulation for large batches

#### Generation Optimization
- Cache model predictions for repeated patterns
- Use vectorized operations for batch generation
- Implement async generation for real-time use

## Contributing

### Adding New Interaction Types
1. Extend `FeatureExtractor` with new feature extraction methods
2. Create new LSTM model architecture
3. Update training pipeline in `HumanInteractionAI`
4. Add generation methods to `HumanInteractionGenerator`

### Improving Existing Models
1. Experiment with different architectures
2. Add new features to existing extractors
3. Implement advanced training techniques
4. Optimize generation algorithms

## Research and Development

### Current Research Directions
- **Transformer Architectures**: Exploring attention-based models
- **Generative Adversarial Networks**: GAN-based interaction generation
- **Reinforcement Learning**: Learning from interaction success/failure
- **Multi-User Modeling**: Capturing individual user patterns

### Future Enhancements
- Real-time adaptation to detection systems
- Cross-platform interaction modeling
- Advanced context understanding
- Biometric pattern integration

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Citation

If you use this model in your research, please cite:

```bibtex
@software{human_interaction_ai,
  title={Human Interaction AI Model},
  author={Your Name},
  year={2025},
  url={https://github.com/your-repo/human-interaction-ai}
}
```

## Support

For questions, issues, or contributions:
- Open an issue on GitHub
- Check the troubleshooting section
- Review the example code
- Consult the API documentation

---

**Note**: This model is designed for legitimate automation and testing purposes. Please ensure compliance with applicable laws and terms of service when using this technology.