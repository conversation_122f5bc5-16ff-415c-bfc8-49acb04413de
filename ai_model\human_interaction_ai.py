"""
Advanced Human Interaction AI Model - Enhanced Version

This module implements a sophisticated AI model capable of learning and replicating
human-like browser interactions from recorded interaction data. The model uses
advanced deep learning techniques to understand and generate highly realistic mouse movements,
typing patterns, and scrolling behaviors that can evade modern bot detection systems.

Key Features:
- Multi-modal learning with transformer attention mechanisms
- Advanced temporal pattern recognition with LSTM and GRU layers
- Sophisticated error simulation and correction patterns
- Advanced anti-detection capabilities with behavioral fingerprinting
- Contextual adaptation based on page content and user state
- Micro-movement generation for ultra-realistic interactions
- Advanced data augmentation and normalization
- Optimized GPU utilization with mixed precision training
- Behavioral biometrics simulation
- Environmental adaptation (time of day, fatigue, etc.)
"""

import time
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import json
import os
import math
import random
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from collections import defaultdict
import logging
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class InteractionConfig:
    """Enhanced configuration for advanced interaction generation"""
    # Mouse settings
    mouse_smoothness: float = 0.85  # 0-1, higher = smoother
    mouse_speed_variance: float = 0.25  # Speed variation
    mouse_error_rate: float = 0.03  # Probability of slight overshoot
    mouse_micro_movement_intensity: float = 0.8  # Intensity of micro-movements
    mouse_jitter_frequency: float = 0.15  # Frequency of small jitters
    mouse_acceleration_variance: float = 0.4  # Acceleration pattern variance
    
    # Typing settings
    typing_speed_wpm: float = 68.0  # Words per minute (more realistic)
    typing_error_rate: float = 0.018  # Probability of typos
    typing_correction_delay: float = 0.45  # Seconds before correction
    typing_burst_probability: float = 0.12  # Probability of typing bursts
    typing_pause_probability: float = 0.08  # Probability of thinking pauses
    typing_rhythm_variance: float = 0.35  # Rhythm variation
    
    # Scrolling settings
    scroll_smoothness: float = 0.75  # Scroll smoothness
    scroll_overshoot_rate: float = 0.08  # Probability of overshooting
    scroll_momentum_simulation: bool = True  # Simulate scroll momentum
    scroll_micro_adjustments: bool = True  # Add micro-adjustments
    scroll_reading_pauses: bool = True  # Simulate reading pauses
    
    # Timing settings
    reaction_time_base: float = 0.18  # Base reaction time in seconds
    reaction_time_variance: float = 0.12  # Reaction time variation
    cognitive_load_factor: float = 1.0  # Cognitive load multiplier
    
    # Advanced anti-detection settings
    add_micro_movements: bool = True  # Add subtle micro-movements
    randomize_timing: bool = True  # Add timing variations
    simulate_fatigue: bool = True  # Simulate user fatigue over time
    behavioral_fingerprinting: bool = True  # Create consistent behavioral patterns
    environmental_adaptation: bool = True  # Adapt to time of day, etc.
    human_error_simulation: bool = True  # Simulate realistic human errors
    attention_drift_simulation: bool = True  # Simulate attention drift
    
    # Biometric simulation
    simulate_hand_tremor: bool = True  # Simulate natural hand tremor
    tremor_frequency: float = 8.5  # Hz, natural hand tremor frequency
    tremor_amplitude: float = 0.3  # Tremor amplitude in pixels
    
    # Context awareness
    page_context_adaptation: bool = True  # Adapt behavior based on page content
    task_complexity_awareness: bool = True  # Adjust behavior for task complexity
    stress_level_simulation: bool = True  # Simulate stress effects on behavior

class FeatureExtractor:
    """Extracts features from raw interaction data for training with proper normalization"""
    
    def __init__(self):
        self.mouse_scaler = StandardScaler()
        self.typing_scaler = StandardScaler()
        self.scroll_scaler = StandardScaler()
        self.fitted = False
        
    def extract_mouse_features(self, events: List[Dict]) -> np.ndarray:
        """Extract features from mouse movement events with proper scaling"""
        features = []
        
        for i in range(1, len(events)):
            prev_event = events[i-1]
            curr_event = events[i]
            
            # Basic movement features - normalize coordinates to 0-1 range
            dx = curr_event['x'] - prev_event['x']
            dy = curr_event['y'] - prev_event['y']
            distance = math.sqrt(dx**2 + dy**2)
            
            # Temporal features - convert to seconds and normalize
            time_delta = (curr_event['timestamp'] - prev_event['timestamp']) / 1000.0  # Convert to seconds
            velocity = distance / max(time_delta, 1e-6)
            
            # Direction features
            angle = math.atan2(dy, dx) if distance > 0 else 0
            
            # Acceleration (if we have previous velocity)
            if i > 1:
                prev_dx = prev_event['x'] - events[i-2]['x']
                prev_dy = prev_event['y'] - events[i-2]['y']
                prev_distance = math.sqrt(prev_dx**2 + prev_dy**2)
                prev_time_delta = (prev_event['timestamp'] - events[i-2]['timestamp']) / 1000.0
                prev_velocity = prev_distance / max(prev_time_delta, 1e-6)
                acceleration = (velocity - prev_velocity) / max(time_delta, 1e-6)
            else:
                acceleration = 0
            
            # Curvature (change in direction)
            if i > 1:
                prev_angle = math.atan2(prev_dy, prev_dx) if prev_distance > 0 else 0
                curvature = abs(angle - prev_angle)
                if curvature > math.pi:
                    curvature = 2 * math.pi - curvature
            else:
                curvature = 0
            
            # Normalize coordinates to 0-1 range (assuming max screen size 2000x2000)
            norm_x = curr_event['x'] / 2000.0
            norm_y = curr_event['y'] / 2000.0
            norm_dx = dx / 2000.0
            norm_dy = dy / 2000.0
            norm_distance = distance / 2000.0
            
            # Normalize velocity and acceleration
            norm_velocity = min(velocity / 1000.0, 1.0)  # Cap at reasonable values
            norm_acceleration = max(-1.0, min(acceleration / 1000.0, 1.0))  # Cap between -1 and 1
            
            # Advanced features for enhanced detection capabilities
            
            # Micro-movement intensity (based on small movements)
            micro_movement_intensity = min(distance / max(time_delta, 1e-6), 100.0) / 100.0 if distance < 5.0 else 0.0
            
            # Jitter detection (high frequency, low amplitude movements)
            jitter_factor = 0.0
            if i > 2 and distance < 3.0 and time_delta < 0.05:  # Small, fast movements
                prev_prev_dx = events[i-2]['x'] - events[i-3]['x'] if i > 2 else 0
                prev_prev_dy = events[i-2]['y'] - events[i-3]['y'] if i > 2 else 0
                direction_change = abs(dx - prev_prev_dx) + abs(dy - prev_prev_dy)
                jitter_factor = min(direction_change / 10.0, 1.0)
            
            # Movement smoothness (consistency of velocity)
            smoothness = 0.0
            if i > 1:
                velocity_change = abs(velocity - prev_velocity) if 'prev_velocity' in locals() else 0
                smoothness = max(0.0, 1.0 - (velocity_change / 100.0))
            
            # Behavioral consistency (pattern regularity)
            pattern_consistency = 0.5  # Default neutral value
            if i > 3:
                # Calculate consistency based on movement patterns
                recent_distances = [
                    math.sqrt((events[j]['x'] - events[j-1]['x'])**2 + (events[j]['y'] - events[j-1]['y'])**2)
                    for j in range(max(1, i-3), i+1)
                ]
                if len(recent_distances) > 1:
                    avg_distance = sum(recent_distances) / len(recent_distances)
                    variance = sum((d - avg_distance)**2 for d in recent_distances) / len(recent_distances)
                    pattern_consistency = max(0.0, min(1.0, 1.0 - (variance / 100.0)))

            features.append([
                norm_x, norm_y,  # Normalized position (2 features)
                norm_dx, norm_dy,  # Normalized delta movement (2 features)
                norm_distance, norm_velocity, norm_acceleration,  # Normalized movement metrics (3 features)
                angle / math.pi, curvature / math.pi,  # Normalized direction metrics (2 features)
                min(time_delta, 1.0),  # Normalized timing (1 feature)
                (curr_event['timestamp'] / 1000.0) % 60.0 / 60.0,  # Normalized absolute time (1 feature)
                micro_movement_intensity,  # Micro-movement detection (1 feature)
                jitter_factor,  # Jitter/tremor detection (1 feature)
                smoothness,  # Movement smoothness (1 feature)
                pattern_consistency  # Behavioral consistency (1 feature)
            ])  # Total: 15 features
        
        return np.array(features, dtype=np.float32)
    
    def extract_typing_features(self, events: List[Dict]) -> np.ndarray:
        """Extract features from typing events with proper scaling"""
        features = []
        
        for i, event in enumerate(events):
            # Basic event features
            event_type = 1.0 if event['type'] == 'keydown' else 0.0
            key_code = (hash(event.get('code', '')) % 1000) / 1000.0  # Normalize to 0-1
            
            # Timing features - convert to seconds and normalize
            if i > 0:
                time_since_last = (event['timestamp'] - events[i-1]['timestamp']) / 1000.0
                time_since_last = min(time_since_last, 2.0) / 2.0  # Normalize to 0-1 (cap at 2 seconds)
            else:
                time_since_last = 0.0
            
            # Error detection features
            is_correct = 1.0 if event.get('correct', True) else 0.0
            error_type = 0.0  # Default no error
            if event.get('error_type'):
                error_map = {'typo': 0.33, 'extra_char': 0.66, 'missing_char': 1.0}
                error_type = error_map.get(event['error_type'], 0.0)
            
            # Text length and position - normalize
            text_length = min(len(event.get('current_text', '')), 1000) / 1000.0  # Normalize to 0-1
            
            # Advanced typing features for enhanced detection capabilities
            
            # Typing rhythm analysis
            typing_rhythm = 0.5  # Default neutral rhythm
            if i > 2:
                recent_timings = [
                    (events[j]['timestamp'] - events[j-1]['timestamp']) / 1000.0
                    for j in range(max(1, i-2), i+1)
                ]
                if len(recent_timings) > 1:
                    avg_timing = sum(recent_timings) / len(recent_timings)
                    timing_variance = sum((t - avg_timing)**2 for t in recent_timings) / len(recent_timings)
                    typing_rhythm = max(0.0, min(1.0, 1.0 - (timing_variance / 0.1)))
            
            # Keystroke pressure simulation (based on timing patterns)
            keystroke_pressure = min(1.0 / max(time_since_last * 2.0, 1e-6), 1.0) if time_since_last > 0 else 0.5
            
            # Fatigue indicator (increases over time)
            fatigue_level = min((event['timestamp'] / 1000.0) / 300.0, 1.0)  # Normalize to 5 minutes
            
            features.append([
                event_type, key_code,  # Event basics (2 features)
                time_since_last,  # Normalized timing (1 feature)
                is_correct, error_type,  # Error features (2 features)
                text_length,  # Normalized context (1 feature)
                (event['timestamp'] / 1000.0) % 60.0 / 60.0,  # Normalized absolute time (1 feature)
                typing_rhythm,  # Typing rhythm consistency (1 feature)
                keystroke_pressure,  # Keystroke pressure simulation (1 feature)
                fatigue_level  # Fatigue indicator (1 feature)
            ])  # Total: 10 features
        
        return np.array(features, dtype=np.float32)
    
    def extract_scroll_features(self, events: List[Dict]) -> np.ndarray:
        """Extract features from scrolling events with proper scaling"""
        features = []
        
        for i, event in enumerate(events):
            # Basic scroll features - normalize
            scroll_y = min(max(event.get('scroll_y', 0), 0), 10000) / 10000.0  # Normalize to 0-1
            scroll_delta = max(-1000, min(event.get('scroll_delta_y', 0), 1000)) / 1000.0  # Normalize to -1 to 1
            
            # Event type
            is_scroll_stop = 1.0 if event['type'] == 'scroll_stop' else 0.0
            is_point_reached = 1.0 if event['type'] == 'point_reached' else 0.0
            
            # Timing features - convert to seconds and normalize
            if i > 0:
                time_since_last = (event['timestamp'] - events[i-1]['timestamp']) / 1000.0
                time_since_last = min(time_since_last, 2.0) / 2.0  # Normalize to 0-1
            else:
                time_since_last = 0.0
            
            # Velocity and acceleration - normalize
            if i > 0 and time_since_last > 0:
                velocity = abs(scroll_delta) / max(time_since_last * 2.0, 1e-6)  # Denormalize time for calculation
                velocity = min(velocity, 1.0)  # Cap at 1.0
            else:
                velocity = 0.0
            
            if i > 1:
                prev_delta = max(-1000, min(events[i-1].get('scroll_delta_y', 0), 1000)) / 1000.0
                prev_time = (events[i-1]['timestamp'] - events[i-2]['timestamp']) / 1000.0
                prev_velocity = abs(prev_delta) / max(prev_time, 1e-6)
                acceleration = max(-1.0, min((velocity - prev_velocity) / max(time_since_last * 2.0, 1e-6), 1.0))
            else:
                acceleration = 0.0
            
            # Advanced scrolling features for enhanced detection capabilities
            
            # Scroll momentum analysis
            momentum_factor = 0.5  # Default neutral momentum
            if i > 1 and abs(scroll_delta) > 0.1:
                momentum_factor = min(abs(scroll_delta) * velocity, 1.0)
            
            # Reading pause detection
            reading_pause_indicator = 0.0
            if time_since_last > 0.5:  # Pause longer than 500ms
                reading_pause_indicator = min(time_since_last / 2.0, 1.0)
            
            # Scroll direction consistency
            direction_consistency = 0.5  # Default neutral
            if i > 2:
                recent_deltas = [
                    max(-1000, min(events[j].get('scroll_delta_y', 0), 1000)) / 1000.0
                    for j in range(max(0, i-2), i+1)
                ]
                if len(recent_deltas) > 1:
                    direction_changes = sum(1 for j in range(1, len(recent_deltas)) 
                                          if recent_deltas[j] * recent_deltas[j-1] < 0)
                    direction_consistency = max(0.0, 1.0 - (direction_changes / len(recent_deltas)))
            
            # Scroll fatigue (decreasing scroll intensity over time)
            scroll_fatigue = min((event['timestamp'] / 1000.0) / 600.0, 1.0)  # Normalize to 10 minutes
            
            features.append([
                scroll_y, scroll_delta,  # Normalized position and movement (2 features)
                velocity, acceleration,  # Normalized movement metrics (2 features)
                is_scroll_stop, is_point_reached,  # Event types (2 features)
                time_since_last,  # Normalized timing (1 feature)
                (event['timestamp'] / 1000.0) % 60.0 / 60.0,  # Normalized absolute time (1 feature)
                momentum_factor,  # Scroll momentum analysis (1 feature)
                reading_pause_indicator,  # Reading pause detection (1 feature)
                direction_consistency,  # Direction consistency (1 feature)
                scroll_fatigue  # Scroll fatigue indicator (1 feature)
            ])  # Total: 12 features
        
        return np.array(features, dtype=np.float32)

class AdvancedMouseMovementLSTM(nn.Module):
    """Advanced LSTM network with transformer attention for learning complex mouse movement patterns"""
    
    def __init__(self, input_size=15, hidden_size=192, num_layers=3, output_size=6):
        super(AdvancedMouseMovementLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # Enhanced LSTM with bidirectional processing
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, 
                           batch_first=True, dropout=0.25, bidirectional=True)
        
        # Transformer encoder for advanced pattern recognition
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=hidden_size * 2, nhead=12, dim_feedforward=512, 
            dropout=0.15, activation='gelu', batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=2)
        
        # Multi-head attention for temporal dependencies
        self.attention = nn.MultiheadAttention(hidden_size * 2, num_heads=12, dropout=0.1, batch_first=True)
        
        # Advanced feature extraction layers
        self.feature_extractor = nn.Sequential(
            nn.Linear(hidden_size * 2, 256),
            nn.GELU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.GELU(),
            nn.Dropout(0.15),
            nn.Linear(128, 64),
            nn.GELU()
        )
        
        # Multiple output heads for different aspects of movement
        self.position_head = nn.Linear(64, 2)  # x, y coordinates
        self.velocity_head = nn.Linear(64, 2)  # velocity components
        self.acceleration_head = nn.Linear(64, 2)  # acceleration components
        
        # Layer normalization for stability
        self.layer_norm = nn.LayerNorm(hidden_size * 2)
        
    def forward(self, x):
        batch_size = x.size(0)
        
        # LSTM processing with bidirectional output
        lstm_out, _ = self.lstm(x)
        
        # Apply layer normalization
        lstm_out = self.layer_norm(lstm_out)
        
        # Transformer encoding for advanced pattern recognition
        transformer_out = self.transformer(lstm_out)
        
        # Self-attention mechanism
        attn_out, attn_weights = self.attention(transformer_out, transformer_out, transformer_out)
        
        # Combine transformer and attention outputs
        combined = transformer_out + attn_out
        
        # Take the last output for prediction
        final_features = combined[:, -1, :]
        
        # Extract high-level features
        features = self.feature_extractor(final_features)
        
        # Generate multiple outputs
        position = torch.tanh(self.position_head(features))  # Position delta
        velocity = torch.tanh(self.velocity_head(features))  # Velocity
        acceleration = torch.tanh(self.acceleration_head(features))  # Acceleration
        
        # Combine outputs
        output = torch.cat([position, velocity, acceleration], dim=1)
        
        return output, attn_weights

# Keep the old class name for backward compatibility
MouseMovementLSTM = AdvancedMouseMovementLSTM

class AdvancedTypingPatternLSTM(nn.Module):
    """Advanced LSTM network for learning complex typing patterns with rhythm and error modeling"""
    
    def __init__(self, input_size=10, hidden_size=128, num_layers=3, output_size=10):
        super(AdvancedTypingPatternLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # Enhanced LSTM with GRU layers for better gradient flow
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, 
                           batch_first=True, dropout=0.25, bidirectional=True)
        self.gru = nn.GRU(hidden_size * 2, hidden_size, 1, batch_first=True)
        
        # Attention mechanism for keystroke dependencies
        self.attention = nn.MultiheadAttention(hidden_size, num_heads=8, dropout=0.15, batch_first=True)
        
        # Feature extraction with residual connections
        self.feature_layers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(hidden_size, hidden_size),
                nn.GELU(),
                nn.Dropout(0.2)
            ) for _ in range(2)
        ])
        
        # Single comprehensive output head for all 10 typing features
        self.output_head = nn.Linear(hidden_size, output_size)  # All 10 typing features
        
        self.layer_norm = nn.LayerNorm(hidden_size)
        
    def forward(self, x):
        # LSTM processing
        lstm_out, _ = self.lstm(x)
        
        # GRU refinement
        gru_out, _ = self.gru(lstm_out)
        
        # Apply layer normalization
        gru_out = self.layer_norm(gru_out)
        
        # Attention mechanism
        attn_out, _ = self.attention(gru_out, gru_out, gru_out)
        
        # Residual connection
        combined = gru_out + attn_out
        
        # Take the last output
        features = combined[:, -1, :]
        
        # Apply feature extraction layers with residual connections
        for layer in self.feature_layers:
            residual = features
            features = layer(features) + residual
        
        # Generate comprehensive output for all typing features
        output = torch.sigmoid(self.output_head(features))
        
        return output

# Keep the old class name for backward compatibility
TypingPatternLSTM = AdvancedTypingPatternLSTM

class AdvancedScrollingPatternLSTM(nn.Module):
    """Advanced LSTM network for learning complex scrolling patterns with momentum and reading behavior"""
    
    def __init__(self, input_size=12, hidden_size=112, num_layers=3, output_size=12):
        super(AdvancedScrollingPatternLSTM, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # Enhanced LSTM with bidirectional processing
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, 
                           batch_first=True, dropout=0.25, bidirectional=True)
        
        # Convolutional layer for local pattern recognition
        self.conv1d = nn.Conv1d(hidden_size * 2, hidden_size, kernel_size=3, padding=1)
        
        # Attention mechanism for scroll context
        self.attention = nn.MultiheadAttention(hidden_size, num_heads=8, dropout=0.15, batch_first=True)
        
        # Feature extraction with batch normalization
        self.feature_extractor = nn.Sequential(
            nn.Linear(hidden_size, 96),
            nn.BatchNorm1d(96),
            nn.GELU(),
            nn.Dropout(0.2),
            nn.Linear(96, 64),
            nn.BatchNorm1d(64),
            nn.GELU(),
            nn.Dropout(0.15)
        )
        
        # Single comprehensive output head for all 12 scrolling features
        self.output_head = nn.Linear(64, output_size)  # All 12 scrolling features
        
        self.layer_norm = nn.LayerNorm(hidden_size)
        
    def forward(self, x):
        # LSTM processing
        lstm_out, _ = self.lstm(x)
        
        # Convolutional processing for local patterns
        conv_input = lstm_out.transpose(1, 2)  # (batch, features, seq_len)
        conv_out = F.relu(self.conv1d(conv_input))
        conv_out = conv_out.transpose(1, 2)  # Back to (batch, seq_len, features)
        
        # Apply layer normalization
        conv_out = self.layer_norm(conv_out)
        
        # Attention mechanism
        attn_out, _ = self.attention(conv_out, conv_out, conv_out)
        
        # Combine conv and attention outputs
        combined = conv_out + attn_out
        
        # Take the last output
        features = combined[:, -1, :]
        
        # Extract features
        features = self.feature_extractor(features)
        
        # Generate comprehensive output for all scrolling features
        output = torch.sigmoid(self.output_head(features))
        
        return output

# Keep the old class name for backward compatibility
ScrollingPatternLSTM = AdvancedScrollingPatternLSTM

class DataAugmenter:
    """Advanced data augmentation for training data enhancement"""
    
    def __init__(self, config: InteractionConfig):
        self.config = config
        
    def augment_mouse_data(self, mouse_sequences: List[np.ndarray]) -> List[np.ndarray]:
        """Augment mouse movement data with realistic variations"""
        augmented = []
        
        for sequence in mouse_sequences:
            # Original sequence
            augmented.append(sequence)
            
            # Time scaling augmentation
            time_scaled = self._time_scale_sequence(sequence, scale_factor=random.uniform(0.8, 1.2))
            augmented.append(time_scaled)
            
            # Noise injection
            noisy = self._add_realistic_noise(sequence, noise_level=0.02)
            augmented.append(noisy)
            
            # Tremor simulation
            if self.config.simulate_hand_tremor:
                tremor_added = self._add_hand_tremor(sequence)
                augmented.append(tremor_added)
        
        return augmented
    
    def augment_typing_data(self, typing_sequences: List[np.ndarray]) -> List[np.ndarray]:
        """Augment typing data with realistic variations"""
        augmented = []
        
        for sequence in typing_sequences:
            # Original sequence
            augmented.append(sequence)
            
            # Rhythm variation
            rhythm_varied = self._vary_typing_rhythm(sequence)
            augmented.append(rhythm_varied)
            
            # Fatigue simulation
            fatigued = self._simulate_typing_fatigue(sequence)
            augmented.append(fatigued)
        
        return augmented
    
    def augment_scroll_data(self, scroll_sequences: List[np.ndarray]) -> List[np.ndarray]:
        """Augment scrolling data with realistic variations"""
        augmented = []
        
        for sequence in scroll_sequences:
            # Original sequence
            augmented.append(sequence)
            
            # Momentum variation
            momentum_varied = self._vary_scroll_momentum(sequence)
            augmented.append(momentum_varied)
            
            # Reading pause simulation
            reading_paused = self._add_reading_pauses(sequence)
            augmented.append(reading_paused)
        
        return augmented
    
    def _time_scale_sequence(self, sequence: np.ndarray, scale_factor: float) -> np.ndarray:
        """Scale timing features in a sequence"""
        scaled = sequence.copy()
        # Assuming timing features are in specific columns
        timing_columns = [2, 6, 10]  # Adjust based on feature structure
        for col in timing_columns:
            if col < scaled.shape[1]:
                scaled[:, col] *= scale_factor
        return scaled
    
    def _add_realistic_noise(self, sequence: np.ndarray, noise_level: float) -> np.ndarray:
        """Add realistic noise to movement data"""
        noisy = sequence.copy()
        noise = np.random.normal(0, noise_level, sequence.shape)
        # Only add noise to position and movement features
        position_columns = [0, 1, 2, 3]  # x, y, dx, dy
        for col in position_columns:
            if col < noisy.shape[1]:
                noisy[:, col] += noise[:, col]
        return noisy
    
    def _add_hand_tremor(self, sequence: np.ndarray) -> np.ndarray:
        """Add realistic hand tremor to mouse movements"""
        tremor_seq = sequence.copy()
        seq_len = len(sequence)
        
        # Generate tremor signal
        t = np.linspace(0, seq_len / 60.0, seq_len)  # Assuming 60 FPS
        tremor_x = self.config.tremor_amplitude * np.sin(2 * np.pi * self.config.tremor_frequency * t)
        tremor_y = self.config.tremor_amplitude * np.cos(2 * np.pi * self.config.tremor_frequency * t)
        
        # Add tremor to position columns
        if tremor_seq.shape[1] > 1:
            tremor_seq[:, 0] += tremor_x  # x position
            tremor_seq[:, 1] += tremor_y  # y position
        
        return tremor_seq
    
    def _vary_typing_rhythm(self, sequence: np.ndarray) -> np.ndarray:
        """Vary typing rhythm while maintaining overall characteristics"""
        varied = sequence.copy()
        rhythm_factor = random.uniform(0.8, 1.2)
        
        # Vary timing features
        if varied.shape[1] > 2:
            varied[:, 2] *= rhythm_factor  # Inter-keystroke timing
        
        return varied
    
    def _simulate_typing_fatigue(self, sequence: np.ndarray) -> np.ndarray:
        """Simulate typing fatigue effects"""
        fatigued = sequence.copy()
        
        # Gradually increase timing as fatigue sets in
        fatigue_progression = np.linspace(1.0, 1.3, len(sequence))
        
        if fatigued.shape[1] > 2:
            fatigued[:, 2] *= fatigue_progression  # Slow down over time
        
        return fatigued
    
    def _vary_scroll_momentum(self, sequence: np.ndarray) -> np.ndarray:
        """Vary scrolling momentum patterns"""
        varied = sequence.copy()
        momentum_factor = random.uniform(0.7, 1.3)
        
        # Vary scroll delta and velocity
        if varied.shape[1] > 1:
            varied[:, 1] *= momentum_factor  # Scroll delta
        if varied.shape[1] > 2:
            varied[:, 2] *= momentum_factor  # Velocity
        
        return varied
    
    def _add_reading_pauses(self, sequence: np.ndarray) -> np.ndarray:
        """Add realistic reading pauses to scroll sequences"""
        paused = sequence.copy()
        
        # Randomly add pauses (increase timing)
        for i in range(len(paused)):
            if random.random() < 0.1:  # 10% chance of reading pause
                if paused.shape[1] > 6:
                    paused[i, 6] += random.uniform(0.5, 2.0)  # Add pause time
        
        return paused

class HumanInteractionAI:
    """Advanced AI model for human-like interaction generation with enhanced anti-detection capabilities"""
    
    def __init__(self, config: InteractionConfig = None):
        self.config = config or InteractionConfig()
        self.feature_extractor = FeatureExtractor()
        
        # Initialize enhanced models with proper input/output sizes
        self.mouse_model = AdvancedMouseMovementLSTM(input_size=15, output_size=6)
        self.typing_model = AdvancedTypingPatternLSTM(input_size=10, output_size=10)
        self.scrolling_model = AdvancedScrollingPatternLSTM(input_size=12, output_size=12)
        
        # Training data storage with enhanced features
        self.mouse_data = []
        self.typing_data = []
        self.scrolling_data = []
        
        # Behavioral fingerprinting storage
        self.behavioral_profile = {
            'mouse_patterns': defaultdict(list),
            'typing_patterns': defaultdict(list),
            'scroll_patterns': defaultdict(list),
            'session_characteristics': {},
            'user_preferences': {}
        }
        
        # Environmental state tracking
        self.environmental_state = {
            'session_start_time': time.time(),
            'actions_performed': 0,
            'current_stress_level': 0.0,
            'fatigue_level': 0.0,
            'attention_level': 1.0,
            'cognitive_load': 0.0
        }
        
        # Advanced data augmentation
        self.data_augmenter = DataAugmenter(self.config)
        
        # Device configuration with optimization
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.mouse_model.to(self.device)
        self.typing_model.to(self.device)
        self.scrolling_model.to(self.device)
        
        # Enable optimizations for inference
        if self.device.type == 'cuda':
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
        
        logger.info(f"Initialized Advanced HumanInteractionAI on device: {self.device}")
        logger.info(f"Enhanced features enabled: {self._get_enabled_features()}")
    
    def _get_enabled_features(self) -> str:
        """Get a summary of enabled advanced features"""
        features = []
        if self.config.behavioral_fingerprinting:
            features.append("Behavioral Fingerprinting")
        if self.config.environmental_adaptation:
            features.append("Environmental Adaptation")
        if self.config.simulate_hand_tremor:
            features.append("Hand Tremor Simulation")
        if self.config.attention_drift_simulation:
            features.append("Attention Drift")
        if self.config.human_error_simulation:
            features.append("Human Error Simulation")
        return ", ".join(features) if features else "Basic Mode"
    
    def load_training_data(self, data_directory: str = 'data/'):
        """Load training data from JSON files and extract features"""
        logger.info(f"Loading training data from {data_directory}")
        
        if not os.path.exists(data_directory):
            logger.error(f"Data directory {data_directory} does not exist")
            return
        
        for filename in os.listdir(data_directory):
            if filename.endswith('.json'):
                filepath = os.path.join(data_directory, filename)
                try:
                    with open(filepath, 'r') as file:
                        interaction_data = json.load(file)
                        self._process_interaction_data(interaction_data)
                except Exception as e:
                    logger.error(f"Error loading {filepath}: {e}")

        logger.info(f"Loaded {len(self.mouse_data)} mouse sequences, "
                   f"{len(self.typing_data)} typing sequences, "
                   f"{len(self.scrolling_data)} scrolling sequences")
    
    def _process_interaction_data(self, data: Dict):
        """Process a single interaction data file"""
        test_type = data.get('test_type', '')
        events = data.get('events', [])
        
        if not events:
            return
        
        try:
            if test_type == 'mouse_movement':
                mouse_events = [event for event in events if event.get('type') == 'mousemove']
                if len(mouse_events) > 10:
                    mouse_features = self.feature_extractor.extract_mouse_features(mouse_events)
                    if mouse_features.size > 0:
                        self.mouse_data.append(mouse_features)
            elif test_type == 'typing':
                typing_events = [event for event in events if event.get('type') in ['keydown', 'keyup', 'input_change']]
                if len(typing_events) > 5:
                    typing_features = self.feature_extractor.extract_typing_features(typing_events)
                    if typing_features.size > 0:
                        self.typing_data.append(typing_features)
            elif test_type == 'scrolling':
                scroll_events = [event for event in events if event.get('type') in ['scroll', 'scroll_stop', 'point_reached']]
                if len(scroll_events) > 3:
                    scroll_features = self.feature_extractor.extract_scroll_features(scroll_events)
                    if scroll_features.size > 0:
                        self.scrolling_data.append(scroll_features)
        except Exception as e:
            logger.error(f"An error occurred during feature extraction: {e}", exc_info=True)
    
    def train_models(self, epochs: int = 50, batch_size: int = 256, learning_rate: float = 0.001):
        """Train all models on the loaded data"""
        logger.info("Starting model training...")
        
        # Train mouse model
        if self.mouse_data:
            logger.info("Training mouse movement model...")
            self._train_mouse_model(epochs, batch_size, learning_rate)
        
        # Train typing model
        if self.typing_data:
            logger.info("Training typing pattern model...")
            self._train_typing_model(epochs, batch_size, learning_rate)
        
        # Train scrolling model
        if self.scrolling_data:
            logger.info("Training scrolling pattern model...")
            self._train_scrolling_model(epochs, batch_size, learning_rate)
        
        logger.info("Training completed!")
    
    def _train_mouse_model(self, epochs: int, batch_size: int, learning_rate: float):
        """Train the mouse movement model with optimized GPU utilization"""
        # Prepare sequences
        sequences = []
        targets = []
        
        for data in self.mouse_data:
            if len(data) < 10:
                continue
            
            # Create sliding windows
            window_size = 10
            for i in range(len(data) - window_size):
                seq = data[i:i+window_size]
                next_point = data[i+window_size]
                
                # Create 6-dimensional target: [position_delta_x, position_delta_y, velocity_x, velocity_y, acceleration_x, acceleration_y]
                angle = next_point[7] * math.pi  # Convert back from normalized angle
                velocity = next_point[5]  # norm_velocity
                acceleration = next_point[6]  # norm_acceleration
                
                target = [
                    next_point[2],  # norm_dx (position delta)
                    next_point[3],  # norm_dy (position delta)
                    velocity * math.cos(angle),  # velocity_x component
                    velocity * math.sin(angle),  # velocity_y component
                    acceleration * math.cos(angle),  # acceleration_x component
                    acceleration * math.sin(angle)   # acceleration_y component
                ]
                sequences.append(seq)
                targets.append(target)
        
        if not sequences:
            logger.warning("No mouse training sequences available")
            return
        
        # Convert to numpy arrays first for efficient processing
        sequences_np = np.array(sequences, dtype=np.float32)
        targets_np = np.array(targets, dtype=np.float32)
        
        logger.info(f"Mouse training data shape: {sequences_np.shape}, targets: {targets_np.shape}")
        logger.info(f"Data range - sequences: [{sequences_np.min():.3f}, {sequences_np.max():.3f}], targets: [{targets_np.min():.3f}, {targets_np.max():.3f}]")
        
        # Split data on CPU to avoid GPU memory issues
        X_train_np, X_val_np, y_train_np, y_val_np = train_test_split(
            sequences_np, targets_np, test_size=0.2, random_state=42
        )
        
        # Convert to tensors and move to GPU once
        X_train = torch.from_numpy(X_train_np).to(self.device, non_blocking=True)
        X_val = torch.from_numpy(X_val_np).to(self.device, non_blocking=True)
        y_train = torch.from_numpy(y_train_np).to(self.device, non_blocking=True)
        y_val = torch.from_numpy(y_val_np).to(self.device, non_blocking=True)
        
        # Optimize batch size for GPU utilization
        optimal_batch_size = min(max(batch_size, 64), len(X_train) // 4)
        logger.info(f"Using optimized batch size: {optimal_batch_size}")
        
        # Training setup with optimized parameters
        optimizer = torch.optim.AdamW(self.mouse_model.parameters(), lr=learning_rate, weight_decay=1e-4)
        criterion = nn.MSELoss()
        scheduler = torch.optim.lr_scheduler.OneCycleLR(
            optimizer, max_lr=learning_rate*5, epochs=epochs, 
            steps_per_epoch=len(X_train)//optimal_batch_size + 1
        )
        
        # Enable mixed precision training for better GPU utilization (fixed API)
        scaler = torch.amp.GradScaler('cuda') if self.device.type == 'cuda' else None
        
        # Training loop with optimizations
        best_val_loss = float('inf')
        for epoch in range(epochs):
            self.mouse_model.train()
            total_loss = 0
            num_batches = 0
            
            # Shuffle indices for better training
            indices = torch.randperm(len(X_train), device=self.device)
            
            # Mini-batch training with optimizations
            for i in range(0, len(X_train), optimal_batch_size):
                batch_indices = indices[i:i+optimal_batch_size]
                batch_X = X_train[batch_indices]
                batch_y = y_train[batch_indices]
                
                # Use mixed precision if available
                if scaler is not None:
                    with torch.amp.autocast('cuda'):
                        outputs, _ = self.mouse_model(batch_X)
                        loss = criterion(outputs, batch_y)
                    
                    optimizer.zero_grad()
                    scaler.scale(loss).backward()
                    scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(self.mouse_model.parameters(), max_norm=1.0)
                    scaler.step(optimizer)
                    scaler.update()
                else:
                    outputs, _ = self.mouse_model(batch_X)
                    loss = criterion(outputs, batch_y)
                    
                    optimizer.zero_grad()
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.mouse_model.parameters(), max_norm=1.0)
                    optimizer.step()
                
                scheduler.step()
                total_loss += loss.item()
                num_batches += 1
            
            # Validation with optimized batch processing
            self.mouse_model.eval()
            val_loss = 0
            val_batches = 0
            with torch.no_grad():
                for i in range(0, len(X_val), optimal_batch_size):
                    batch_X_val = X_val[i:i+optimal_batch_size]
                    batch_y_val = y_val[i:i+optimal_batch_size]
                    
                    if scaler is not None:
                        with torch.amp.autocast('cuda'):
                            val_outputs, _ = self.mouse_model(batch_X_val)
                            batch_val_loss = criterion(val_outputs, batch_y_val)
                    else:
                        val_outputs, _ = self.mouse_model(batch_X_val)
                        batch_val_loss = criterion(val_outputs, batch_y_val)
                    
                    val_loss += batch_val_loss.item()
                    val_batches += 1
            
            avg_val_loss = val_loss / val_batches if val_batches > 0 else float('inf')
            
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                torch.save(self.mouse_model.state_dict(), 'mouse_model_best.pth')
            
            if epoch % 5 == 0:  # More frequent logging
                avg_train_loss = total_loss / num_batches if num_batches > 0 else 0
                logger.info(f"Mouse Model - Epoch {epoch}: Train Loss: {avg_train_loss:.6f}, "
                           f"Val Loss: {avg_val_loss:.6f}, LR: {scheduler.get_last_lr()[0]:.6f}")
    
    def _train_typing_model(self, epochs: int, batch_size: int, learning_rate: float):
        """Train the typing pattern model with optimized GPU utilization"""
        sequences = []
        targets = []
        
        for data in self.typing_data:
            if len(data) < 5:
                continue
            
            window_size = 5
            for i in range(len(data) - window_size):
                seq = data[i:i+window_size]
                next_event = data[i+window_size]
                
                # Create 10-dimensional target to match all typing features:
                target = [
                    next_event[0],  # event_type
                    next_event[1],  # key_code
                    next_event[2],  # time_since_last
                    next_event[3],  # is_correct
                    next_event[4],  # error_type
                    next_event[5],  # text_length
                    next_event[6],  # absolute_time
                    next_event[7],  # typing_rhythm
                    next_event[8],  # keystroke_pressure
                    next_event[9]   # fatigue_level
                ]
                sequences.append(seq)
                targets.append(target)
        
        if not sequences:
            logger.warning("No typing training sequences available")
            return
        
        # Convert to numpy arrays first for efficient processing
        sequences_np = np.array(sequences, dtype=np.float32)
        targets_np = np.array(targets, dtype=np.float32)
        
        logger.info(f"Typing training data shape: {sequences_np.shape}, targets: {targets_np.shape}")
        
        # Split data on CPU to avoid GPU memory issues
        X_train_np, X_val_np, y_train_np, y_val_np = train_test_split(
            sequences_np, targets_np, test_size=0.2, random_state=42
        )
        
        # Convert to tensors and move to GPU once
        X_train = torch.from_numpy(X_train_np).to(self.device, non_blocking=True)
        X_val = torch.from_numpy(X_val_np).to(self.device, non_blocking=True)
        y_train = torch.from_numpy(y_train_np).to(self.device, non_blocking=True)
        y_val = torch.from_numpy(y_val_np).to(self.device, non_blocking=True)
        
        # Optimize batch size for GPU utilization
        optimal_batch_size = min(max(batch_size, 64), len(X_train) // 4)
        logger.info(f"Using optimized batch size for typing: {optimal_batch_size}")
        
        # Training setup with optimized parameters
        optimizer = torch.optim.AdamW(self.typing_model.parameters(), lr=learning_rate, weight_decay=1e-4)
        criterion = nn.MSELoss()
        scheduler = torch.optim.lr_scheduler.OneCycleLR(
            optimizer, max_lr=learning_rate*5, epochs=epochs, 
            steps_per_epoch=len(X_train)//optimal_batch_size + 1
        )
        
        # Enable mixed precision training for better GPU utilization
        scaler = torch.amp.GradScaler('cuda') if self.device.type == 'cuda' else None
        
        best_val_loss = float('inf')
        for epoch in range(epochs):
            self.typing_model.train()
            total_loss = 0
            num_batches = 0
            
            # Shuffle indices for better training
            indices = torch.randperm(len(X_train), device=self.device)
            
            for i in range(0, len(X_train), optimal_batch_size):
                batch_indices = indices[i:i+optimal_batch_size]
                batch_X = X_train[batch_indices]
                batch_y = y_train[batch_indices]
                
                # Use mixed precision if available
                if scaler is not None:
                    with torch.amp.autocast('cuda'):
                        outputs = self.typing_model(batch_X)
                        loss = criterion(outputs, batch_y)
                    
                    optimizer.zero_grad()
                    scaler.scale(loss).backward()
                    scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(self.typing_model.parameters(), max_norm=1.0)
                    scaler.step(optimizer)
                    scaler.update()
                else:
                    outputs = self.typing_model(batch_X)
                    loss = criterion(outputs, batch_y)
                    
                    optimizer.zero_grad()
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.typing_model.parameters(), max_norm=1.0)
                    optimizer.step()
                
                scheduler.step()
                total_loss += loss.item()
                num_batches += 1
            
            # Validation with optimized batch processing
            self.typing_model.eval()
            val_loss = 0
            val_batches = 0
            with torch.no_grad():
                for i in range(0, len(X_val), optimal_batch_size):
                    batch_X_val = X_val[i:i+optimal_batch_size]
                    batch_y_val = y_val[i:i+optimal_batch_size]
                    
                    if scaler is not None:
                        with torch.amp.autocast('cuda'):
                            val_outputs = self.typing_model(batch_X_val)
                            batch_val_loss = criterion(val_outputs, batch_y_val)
                    else:
                        val_outputs = self.typing_model(batch_X_val)
                        batch_val_loss = criterion(val_outputs, batch_y_val)
                    
                    val_loss += batch_val_loss.item()
                    val_batches += 1
            
            avg_val_loss = val_loss / val_batches if val_batches > 0 else float('inf')
            
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                torch.save(self.typing_model.state_dict(), 'typing_model_best.pth')
            
            if epoch % 5 == 0:  # More frequent logging
                avg_train_loss = total_loss / num_batches if num_batches > 0 else 0
                logger.info(f"Typing Model - Epoch {epoch}: Train Loss: {avg_train_loss:.6f}, "
                           f"Val Loss: {avg_val_loss:.6f}, LR: {scheduler.get_last_lr()[0]:.6f}")
    
    def _train_scrolling_model(self, epochs: int, batch_size: int, learning_rate: float):
        """Train the scrolling pattern model with optimized GPU utilization"""
        sequences = []
        targets = []
        
        for data in self.scrolling_data:
            if len(data) < 3:
                continue
            
            window_size = 3
            for i in range(len(data) - window_size):
                seq = data[i:i+window_size]
                next_event = data[i+window_size]
                
                # Create 12-dimensional target to match all scrolling features:
                target = [
                    next_event[0],  # scroll_y
                    next_event[1],  # scroll_delta
                    next_event[2],  # velocity
                    next_event[3],  # acceleration
                    next_event[4],  # is_scroll_stop
                    next_event[5],  # is_point_reached
                    next_event[6],  # time_since_last
                    next_event[7],  # absolute_time
                    next_event[8],  # momentum_factor
                    next_event[9],  # reading_pause_indicator
                    next_event[10], # direction_consistency
                    next_event[11]  # scroll_fatigue
                ]
                sequences.append(seq)
                targets.append(target)
        
        if not sequences:
            logger.warning("No scrolling training sequences available")
            return
        
        # Convert to numpy arrays first for efficient processing
        sequences_np = np.array(sequences, dtype=np.float32)
        targets_np = np.array(targets, dtype=np.float32)
        
        logger.info(f"Scrolling training data shape: {sequences_np.shape}, targets: {targets_np.shape}")
        
        # Split data on CPU to avoid GPU memory issues
        X_train_np, X_val_np, y_train_np, y_val_np = train_test_split(
            sequences_np, targets_np, test_size=0.2, random_state=42
        )
        
        # Convert to tensors and move to GPU once
        X_train = torch.from_numpy(X_train_np).to(self.device, non_blocking=True)
        X_val = torch.from_numpy(X_val_np).to(self.device, non_blocking=True)
        y_train = torch.from_numpy(y_train_np).to(self.device, non_blocking=True)
        y_val = torch.from_numpy(y_val_np).to(self.device, non_blocking=True)
        
        # Optimize batch size for GPU utilization
        optimal_batch_size = min(max(batch_size, 64), len(X_train) // 4)
        logger.info(f"Using optimized batch size for scrolling: {optimal_batch_size}")
        
        # Training setup with optimized parameters
        optimizer = torch.optim.AdamW(self.scrolling_model.parameters(), lr=learning_rate, weight_decay=1e-4)
        criterion = nn.MSELoss()
        scheduler = torch.optim.lr_scheduler.OneCycleLR(
            optimizer, max_lr=learning_rate*5, epochs=epochs, 
            steps_per_epoch=len(X_train)//optimal_batch_size + 1
        )
        
        # Enable mixed precision training for better GPU utilization
        scaler = torch.amp.GradScaler('cuda') if self.device.type == 'cuda' else None
        
        best_val_loss = float('inf')
        for epoch in range(epochs):
            self.scrolling_model.train()
            total_loss = 0
            num_batches = 0
            
            # Shuffle indices for better training
            indices = torch.randperm(len(X_train), device=self.device)
            
            for i in range(0, len(X_train), optimal_batch_size):
                batch_indices = indices[i:i+optimal_batch_size]
                batch_X = X_train[batch_indices]
                batch_y = y_train[batch_indices]
                
                # Use mixed precision if available
                if scaler is not None:
                    with torch.amp.autocast('cuda'):
                        outputs = self.scrolling_model(batch_X)
                        loss = criterion(outputs, batch_y)
                    
                    optimizer.zero_grad()
                    scaler.scale(loss).backward()
                    scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(self.scrolling_model.parameters(), max_norm=1.0)
                    scaler.step(optimizer)
                    scaler.update()
                else:
                    outputs = self.scrolling_model(batch_X)
                    loss = criterion(outputs, batch_y)
                    
                    optimizer.zero_grad()
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(self.scrolling_model.parameters(), max_norm=1.0)
                    optimizer.step()
                
                scheduler.step()
                total_loss += loss.item()
                num_batches += 1
            
            # Validation with optimized batch processing
            self.scrolling_model.eval()
            val_loss = 0
            val_batches = 0
            with torch.no_grad():
                for i in range(0, len(X_val), optimal_batch_size):
                    batch_X_val = X_val[i:i+optimal_batch_size]
                    batch_y_val = y_val[i:i+optimal_batch_size]
                    
                    if scaler is not None:
                        with torch.amp.autocast('cuda'):
                            val_outputs = self.scrolling_model(batch_X_val)
                            batch_val_loss = criterion(val_outputs, batch_y_val)
                    else:
                        val_outputs = self.scrolling_model(batch_X_val)
                        batch_val_loss = criterion(val_outputs, batch_y_val)
                    
                    val_loss += batch_val_loss.item()
                    val_batches += 1
            
            avg_val_loss = val_loss / val_batches if val_batches > 0 else float('inf')
            
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                torch.save(self.scrolling_model.state_dict(), 'scrolling_model_best.pth')
            
            if epoch % 5 == 0:  # More frequent logging
                avg_train_loss = total_loss / num_batches if num_batches > 0 else 0
                logger.info(f"Scrolling Model - Epoch {epoch}: Train Loss: {avg_train_loss:.6f}, "
                           f"Val Loss: {avg_val_loss:.6f}, LR: {scheduler.get_last_lr()[0]:.6f}")
    
    def save_models(self, directory: str = 'saved_models/'):
        """Save all trained models"""
        os.makedirs(directory, exist_ok=True)
        
        torch.save(self.mouse_model.state_dict(), os.path.join(directory, 'mouse_model.pth'))
        torch.save(self.typing_model.state_dict(), os.path.join(directory, 'typing_model.pth'))
        torch.save(self.scrolling_model.state_dict(), os.path.join(directory, 'scrolling_model.pth'))
        
        # Save configuration
        config_dict = {
            'mouse_smoothness': self.config.mouse_smoothness,
            'mouse_speed_variance': self.config.mouse_speed_variance,
            'mouse_error_rate': self.config.mouse_error_rate,
            'typing_speed_wpm': self.config.typing_speed_wpm,
            'typing_error_rate': self.config.typing_error_rate,
            'typing_correction_delay': self.config.typing_correction_delay,
            'scroll_smoothness': self.config.scroll_smoothness,
            'scroll_overshoot_rate': self.config.scroll_overshoot_rate,
            'reaction_time_base': self.config.reaction_time_base,
            'reaction_time_variance': self.config.reaction_time_variance,
            'add_micro_movements': self.config.add_micro_movements,
            'randomize_timing': self.config.randomize_timing,
            'simulate_fatigue': self.config.simulate_fatigue
        }
        
        with open(os.path.join(directory, 'config.json'), 'w') as f:
            json.dump(config_dict, f, indent=2)
        
        logger.info(f"Models saved to {directory}")
    
    def load_models(self, directory: str = 'saved_models/'):
        """Load pre-trained models"""
        try:
            self.mouse_model.load_state_dict(torch.load(
                os.path.join(directory, 'mouse_model.pth'), map_location=self.device
            ))
            self.typing_model.load_state_dict(torch.load(
                os.path.join(directory, 'typing_model.pth'), map_location=self.device
            ))
            self.scrolling_model.load_state_dict(torch.load(
                os.path.join(directory, 'scrolling_model.pth'), map_location=self.device
            ))
            
            # Load configuration
            config_path = os.path.join(directory, 'config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config_dict = json.load(f)
                    for key, value in config_dict.items():
                        setattr(self.config, key, value)
            
            logger.info(f"Models loaded from {directory}")
        except Exception as e:
            logger.error(f"Error loading models: {e}")

if __name__ == "__main__":
    # Example usage
    config = InteractionConfig()
    ai = HumanInteractionAI(config)
    
    # Load and train on data
    ai.load_training_data('data/')
    ai.train_models(epochs=50, batch_size=64, learning_rate=0.001)
    
    # Save the trained models
    ai.save_models()