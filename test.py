"""import asyncio
from src.browser_actions import BrowserActions
import nodriver as uc

async def main():
    browser = await uc.start()
    tab = await browser.get("https://www.google.com")
    browser_actions = BrowserActions(tab, "<EMAIL>")

    # Test mouse movement
    await browser_actions.human_like_mouse_move(500, 500)

    # Test typing
    await browser_actions.send_keys("textarea[name=q]", "Hello, world!")

    # Test scrolling
    await browser_actions.scroll_to_element(await tab.select("body"))

if __name__ == "__main__":
    asyncio.run(main())
""