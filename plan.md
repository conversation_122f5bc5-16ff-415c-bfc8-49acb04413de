# Plan for Implementing "Complete More Activities" Daily Task

This document outlines the detailed plan for creating a new daily task to automate the completion of "more activities" on the Microsoft Rewards page, incorporating human-like interactions and robust error handling.

## Phase 1: Initial Setup and Information Gathering

1.  **Update `src/browser_actions.py`**:
    I have already added new utility functions to `src/browser_actions.py` to facilitate extracting text, attributes, and lists of elements, as well as waiting for class changes. These functions are crucial for interacting with the dynamic HTML content of the rewards page.
    *   `get_text_content(self, selector, timeout=10)`: To extract text from elements (e.g., level, points).
    *   `get_attribute(self, selector, attribute_name, timeout=10)`: To get attribute values (e.g., href for activity links).
    *   `get_elements_by_selector(self, selector, timeout=10)`: To retrieve multiple elements (e.g., all activity cards).
    *   `wait_for_element_class_change(self, element_selector, expected_class, timeout=10, interval=0.5)`: To confirm activity completion by monitoring class changes.

2.  **Create New Daily Task File**:
    I will create a new Python file, `automation_steps/daily_tasks/complete_more_activities.py`, which will house the logic for this new daily task. This file will define a class `CompleteMoreActivitiesStep` that inherits from `automation_steps/base_step.py::BaseStep`.

3.  **Integrate New Task into `src/bot.py`**:
    I will modify `src/bot.py` to import and execute the `CompleteMoreActivitiesStep` after a successful login and session validation. This ensures the new task runs as part of the bot's daily operations.

## Phase 2: Implementing `CompleteMoreActivitiesStep` Logic

The `execute` method within `CompleteMoreActivitiesStep` will orchestrate the entire process:

1.  **Get Initial Account Information**:
    *   Retrieve the current Level using `self.browser_actions.get_text_content` with the selector `p.profileDescription.level`.
    *   Retrieve the initial Available Points using `self.browser_actions.get_text_content` with the selector `div[title="Available points"] .pointsValue span`.
    *   Retrieve the initial Today's Points using `self.browser_actions.get_text_content` with the selector `div[title="Today's points"] .pointsValue span`.
    *   Log these initial values for tracking.

2.  **Scroll to Activities Section**:
    *   Use `self.tab.evaluate` with JavaScript to scroll the `div#more-activities` element into the viewport. This provides a more precise and human-like scroll than a generic page scroll.
    *   Introduce a human-like delay after scrolling to simulate reading time.

3.  **Identify and Process Activities**:
    *   Initialize `activities_completed_count = 0`.
    *   Implement a `while` loop that continues as long as new donable activities are found or `max_activity_attempts` is not reached.
    *   Inside the loop, re-fetch all potential activity cards using `self.browser_actions.get_elements_by_selector` with the selector `div#more-activities mee-card`. This ensures we have the most up-to-date DOM.
    *   Initialize `donable_activity_found_in_iteration = False` at the beginning of each outer loop iteration.
    *   Iterate through each `mee-card` element:
        *   **Scroll to Activity**: Before any checks, call `await self.browser_actions.scroll_to_element(card_element)` to bring the specific card into view.
        *   Check if the activity card's parent container (`div.rewards-card-container`) has the `locked-card` class. If it does, skip this activity.
        *   Check for the presence of a `span` with `class="mee-icon mee-icon-AddMedium"` within the card. This indicates an activity that can be completed.
        *   Check for the presence of a `span` with `class="mee-icon mee-icon-SkypeCircleCheck"` within the card. If found, the activity is already completed; skip it.
        *   Extract the points associated with the activity from the `span.c-heading.pointsString`. If no points or 0 points, skip.
        *   If the activity is donable and has points:
            *   Extract the `href` attribute from the `a` tag within the card, which is the link to click.
            *   Click the activity link using `self.browser_actions.click`.
            *   Set `donable_activity_found_in_iteration = True`.

4.  **Handle New Tab**: Since activities open in a new page, I will:
    *   Identify the newly opened tab by comparing `browser.tabs` before and after the click.
    *   Switch the nodriver context to this new tab.
    *   Wait for a random duration (1-3 seconds) on the new tab to simulate engagement.
    *   Close the new tab using `new_tab.close()`.
    *   Switch back to the original main tab (`self.tab`) to continue processing.

5.  **Wait for Completion Icon**: After returning to the main page, wait for the `mee-icon-AddMedium` span of the just-completed activity to change to `mee-icon-SkypeCircleCheck` using `self.browser_actions.wait_for_element_class_change`. This will require extracting the `data-bi-id` from the card to reliably target it.

6.  **Verify Points Credited**:
    *   Retrieve the new available points from the page.
    *   Implement a loop that checks the available points every 0.5 seconds for up to 10 seconds.
    *   Break the loop if the `new_available_points` are at least `initial_available_points + activity_points`.
    *   Log whether the points were successfully credited or if the timeout was reached. The task explicitly states "Do not try to redo it even if the points are not credited."
    *   Update `initial_available_points` for the next iteration of the outer loop.

7.  **Loop Termination**: If `donable_activity_found_in_iteration` remains `False` after checking all currently visible cards in an outer loop iteration, it means no new donable activities were found, and the `while` loop should break.

8.  **Final Logging**: After processing all activities, log the total number of activities completed and the final available points.

## Phase 3: Advanced AI Model Enhancement for Human-like Interactions

*   **Enhanced Neural Network Architecture**: Upgraded LSTM models with attention mechanisms, transformer layers, and advanced regularization for better pattern learning.
*   **Advanced Movement Generation**: Implemented sophisticated Bezier curve generation with micro-movements, jitter, and realistic acceleration/deceleration patterns.
*   **Bot Detection Evasion**: Added advanced anti-detection features including behavioral fingerprinting, timing randomization, and human error simulation.
*   **Contextual Adaptation**: Models now adapt behavior based on page context, user fatigue simulation, and environmental factors.
*   **Enhanced Training Process**: Improved data augmentation, advanced loss functions, and multi-objective optimization for better model performance.

## Phase 4: Refinements and Best Practices

*   **Error Handling**: Implement comprehensive `try-except` blocks throughout the new code, utilizing `self.bot.log_error` to capture and log any exceptions, including saving the page HTML for debugging.
*   **Human-like Delays**: Continue to use `random.uniform` for all delays to mimic human browsing behavior and avoid bot detection.
*   **Stealth**: Ensure all new interactions and element selections align with the existing stealth mechanisms in `src/stealth_browser.py`.
*   **Logging**: Use the `src/logger.py::logger` for all informational, debug, warning, and error messages, ensuring consistent and clear output.

## `CompleteMoreActivitiesStep` Flow Diagram

```mermaid
graph TD
    start[Start: CompleteMoreActivitiesStep] --> getInfo{Get Initial Account Info}
    getInfo --> logInfo[Log Level, Available Points, Today's Points]
    logInfo --> scroll[Scroll to #more-activities]
    scroll --> getActivities{Get all mee-card activities}
    
    getActivities --> loopActivities{Loop through each activity card}
    
    loopActivities --> checkDonable{Is activity donable?}
    checkDonable -- No --> loopActivities
    checkDonable -- Yes --> extractInfo[Extract activity points and link]
    
    extractInfo --> clickLink[Click activity link]
    clickLink --> newTabCheck{New tab opened?}
    
    newTabCheck -- Yes --> switchTab[Switch to new tab]
    switchTab --> waitTab[Wait 1-3 seconds]
    waitTab --> closeTab[Close new tab]
    closeTab --> switchBack[Switch back to main tab]
    switchBack --> waitIcon[Wait for activity icon: SkypeCircleCheck]
    waitIcon --> getPoints[Get new Available Points]
    getPoints --> creditWait{Wait up to 10s for points to credit}
    
    creditWait -- Credited --> logSuccess[Log success]
    creditWait -- Timeout --> logFail[Log failure - no retry]
    
    logSuccess --> loopActivities
    logFail --> loopActivities
    
    loopActivities --> doneCheck{All activities processed?}
    doneCheck -- Yes --> logTotal[Log total completed and final points]
    logTotal --> stepEnd[End: CompleteMoreActivitiesStep]
```