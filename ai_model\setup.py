"""
Setup script for Human Interaction AI Model

This script helps set up the environment and dependencies for the AI model.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✓ Python version: {version.major}.{version.minor}.{version.micro}")
    return True

def check_gpu_availability():
    """Check if GPU is available for training"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0)
            print(f"✓ GPU available: {gpu_name} ({gpu_count} device(s))")
            return True
        else:
            print("⚠️  No GPU detected - training will use CPU (slower)")
            return False
    except ImportError:
        print("⚠️  PyTorch not installed - cannot check GPU availability")
        return False

def install_requirements():
    """Install required packages using uv"""
    print("Installing required packages...")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ requirements.txt not found")
        return False
    
    try:
        # Check if uv is available
        try:
            subprocess.run(["uv", "--version"], check=True, capture_output=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ uv is not available. Please install uv first.")
            print("   Visit: https://docs.astral.sh/uv/getting-started/installation/")
            return False
        
        # Install PyTorch first (with CUDA support if available)
        print("Installing PyTorch...")
        if platform.system() == "Windows":
            # Use cu124 for CUDA 12.x compatibility (supports CUDA 12.0-12.9)
            torch_command = [
                "uv", "pip", "install", 
                "torch", "torchvision", "torchaudio", 
                "--index-url", "https://download.pytorch.org/whl/cu124"
            ]
        else:
            torch_command = [
                "uv", "pip", "install", 
                "torch", "torchvision", "torchaudio"
            ]
        
        subprocess.run(torch_command, check=True)
        
        # Install other requirements
        print("Installing other dependencies...")
        subprocess.run([
            "uv", "pip", "install", 
            "-r", str(requirements_file)
        ], check=True)
        
        print("✓ All packages installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install packages: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("Creating directory structure...")
    
    base_dir = Path(__file__).parent
    directories = [
        "saved_models",
        "training_plots", 
        "test_results",
        "example_models"
    ]
    
    for dir_name in directories:
        dir_path = base_dir / dir_name
        dir_path.mkdir(exist_ok=True)
        print(f"✓ Created directory: {dir_path}")
    
    return True

def verify_installation():
    """Verify that the installation works"""
    print("Verifying installation...")
    
    try:
        # Test imports
        import torch
        import numpy as np
        import matplotlib.pyplot as plt
        import seaborn as sns
        from sklearn.preprocessing import StandardScaler
        
        print("✓ All core packages imported successfully")
        
        # Test AI model import
        sys.path.append(str(Path(__file__).parent.parent))
        from ai_model import HumanInteractionAI, InteractionConfig
        
        print("✓ AI model imports working")
        
        # Test basic functionality
        config = InteractionConfig()
        ai_model = HumanInteractionAI(config)
        
        print(f"✓ AI model initialized on device: {ai_model.device}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

def download_sample_data():
    """Download or create sample data for testing"""
    print("Setting up sample data...")
    
    # Create a simple sample data file for testing
    sample_data = {
        "test_type": "mouse_movement",
        "test_id": "sample-mouse-001",
        "timestamp_start": 0,
        "timestamp_end": 2000,
        "settings": {"num_points": 5},
        "events": [
            {"type": "mousemove", "timestamp": 0, "x": 0, "y": 0},
            {"type": "mousemove", "timestamp": 100, "x": 50, "y": 25},
            {"type": "mousemove", "timestamp": 200, "x": 100, "y": 50},
            {"type": "mousemove", "timestamp": 300, "x": 150, "y": 75},
            {"type": "mousemove", "timestamp": 400, "x": 200, "y": 100},
            {"type": "point_reached", "timestamp": 500, "point_id": "point-0", 
             "target_x": 200, "target_y": 100, "actual_x": 200, "actual_y": 100}
        ]
    }
    
    # Create data directory if it doesn't exist
    data_dir = Path(__file__).parent.parent / "data"
    data_dir.mkdir(exist_ok=True)
    
    # Save sample data
    import json
    sample_file = data_dir / "sample_mouse_data.json"
    with open(sample_file, 'w') as f:
        json.dump(sample_data, f, indent=2)
    
    print(f"✓ Created sample data file: {sample_file}")
    return True

def print_next_steps():
    """Print next steps for the user"""
    print("\n" + "=" * 60)
    print("🎉 SETUP COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    print("\nNext steps:")
    print("1. 📊 Collect training data:")
    print("   cd ..")
    print("   python backend/app.py")
    print("   # Open browser to localhost:5000 and record interactions")
    print()
    print("2. 🤖 Train the AI model:")
    print("   python ai_model/train_model.py --visualize")
    print()
    print("3. 🧪 Test the trained model:")
    print("   python ai_model/test_model.py --visualize --benchmark")
    print()
    print("4. 📖 See examples:")
    print("   python ai_model/example_usage.py")
    print()
    print("5. 🔗 Integrate with your automation:")
    print("   from ai_model import HumanInteractionAI, HumanInteractionGenerator")
    print("   # See example_usage.py for detailed examples")
    print()
    print("📚 Documentation: ai_model/README.md")
    print("🐛 Issues: Check the troubleshooting section in README.md")

def main():
    """Main setup function"""
    print("🚀 Human Interaction AI Model Setup")
    print("=" * 60)
    print("This script will set up the environment for the AI model.")
    print()
    
    success = True
    
    # Check Python version
    if not check_python_version():
        success = False
    
    # Install requirements
    if success and not install_requirements():
        success = False
    
    # Check GPU availability
    if success:
        check_gpu_availability()
    
    # Create directories
    if success and not create_directories():
        success = False
    
    # Verify installation
    if success and not verify_installation():
        success = False
    
    # Setup sample data
    if success and not download_sample_data():
        success = False
    
    if success:
        print_next_steps()
    else:
        print("\n❌ Setup failed. Please check the errors above and try again.")
        print("💡 Common solutions:")
        print("   - Ensure you have Python 3.8+ installed")
        print("   - Ensure you have uv installed: https://docs.astral.sh/uv/getting-started/installation/")
        print("   - Try running with administrator/sudo privileges")
        print("   - Check your internet connection for package downloads")
        print("   - Install packages manually: uv pip install torch numpy matplotlib")

if __name__ == "__main__":
    main()