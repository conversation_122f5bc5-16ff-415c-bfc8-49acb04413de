# PyTorch with CUDA 12.x support (compatible with CUDA 12.1-12.4)
torch>=2.7.0
torchvision>=0.22.0
torchaudio>=2.7.0
--index-url https://download.pytorch.org/whl/cu128

# Core ML and Data Science
numpy>=1.24.0
scikit-learn>=1.3.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0

# Data Processing
pandas>=1.4.0

# Async and Utilities
asyncio-mqtt>=0.11.0

# Development and Testing
pytest>=7.0.0
pytest-asyncio>=0.21.0

# Optional: For enhanced performance
# numba>=0.56.0  # Uncomment for JIT compilation
# cupy>=11.0.0   # Uncomment for GPU-accelerated NumPy operations