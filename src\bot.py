import asyncio
import random
import nodriver as uc
import urllib.parse
from .stealth_browser import StealthBrowser
from .utils import log_error
from .user_data_extractor import UserDataExtractor # Import UserDataExtractor
from .logger import logger # Import the new logger
from automation_steps.login_steps import (
    EnterEmailStep,
    ClickNextEmailStep,
    HandleCodePageStep,
    EnterPasswordStep,
    ClickNextPasswordStep,
    HandleStaySignedInStep
)
from automation_steps.daily_tasks.utils import get_remaining_desktop_searches
from automation_steps.daily_tasks.complete_more_activities import CompleteMoreActivitiesStep

import time

class RewardsBot:
    def __init__(self, email, password):
        self.email = email
        self.password = password
        self.stealth_browser = StealthBrowser()
        self.browser = None
        self.tab = None
        self.base_url = 'https://rewards.microsoft.com'
        self.login_url_check = 'login.live.com'
        self.rewards_url_check = 'rewards.bing.com'
        self.user_data_extractor = None # Initialize UserDataExtractor here
        self.log_error = log_error

    async def start_browser(self):
        """Launches a non-headless browser instance with stealth."""
        try:
            logger.info("Launching browser with stealth features...", extra={'email': self.email})
            self.browser, self.tab = await self.stealth_browser.start_stealth_session()
            # Update the email in browser_actions after the bot's email is known
            self.stealth_browser.browser_actions.email = self.email
            # Initialize UserDataExtractor with the tab, email, and browser_actions instance
            self.user_data_extractor = UserDataExtractor(self.tab, self.email, self.stealth_browser.browser_actions)
            self.session_loaded = await self.stealth_browser.load_session_cookies(self.email)
            return True
        except Exception as e:
            await self.log_error(self.email, f"Failed to start browser: {e}")
            self.browser = None
            self.tab = None
            return False

    async def navigate_and_sign_in(self, is_new_session):
        """Navigates to the rewards page and performs the sign-in process with human-like interactions."""
        if not self.tab:
            await self.log_error(self.email, "Browser tab not initialized. Call start_browser first.")
            return False
        
        try:
            initial_target_url = self.base_url
            if not is_new_session:
                initial_target_url = f"https://{self.rewards_url_check}"
                logger.info(f"Session loaded. Navigating directly to {initial_target_url} to check session validity...", extra={'email': self.email})
            else:
                logger.info(f"No existing session found. Navigating to {initial_target_url} to begin sign-in process...", extra={'email': self.email})

            await self.stealth_browser.navigate_with_stealth(initial_target_url)

            logger.debug("Monitoring for rewards page or login redirect...", extra={'email': self.email})
            start_time = asyncio.get_event_loop().time()
            session_valid = False
            while asyncio.get_event_loop().time() - start_time < 30:
                points_button_found = await self.stealth_browser.browser_actions.wait_for_element('#userPointsBreakdown', timeout=1)
                if points_button_found:
                    logger.debug("Session validated: 'Points breakdown' button found.", extra={'email': self.email})
                    session_valid = True
                    break

                current_url = self.tab.url
                parsed_url = urllib.parse.urlparse(current_url)
                current_hostname = parsed_url.hostname

                if self.rewards_url_check in current_hostname:
                    logger.debug("Session validated: URL settled on rewards page.", extra={'email': self.email})
                    session_valid = True
                    break
                elif self.login_url_check in current_hostname:
                    logger.info("Redirected to login page. Proceeding with login flow.", extra={'email': self.email})
                    email_input = await self.tab.select("input[type='email'][id='usernameEntry']", timeout=5)
                    if email_input:
                        logger.debug("Email input field found. Initiating login sequence.", extra={'email': self.email})
                        return await self._execute_login_flow()
                    else:
                        page_html = await self.tab.get_content()
                        await self.log_error(self.email, f"Redirected to login page but email input field not found. Unexpected behavior. Current URL: {self.tab.url}", page_html)
                        return False
                
                await asyncio.sleep(0.5)

            if session_valid:
                return True
            else:
                page_html = await self.tab.get_content()
                await self.log_error(self.email, f"Session validation failed: Neither rewards page nor login page detected within timeout. Current URL: {self.tab.url}", page_html)
                return False

        except Exception as e:
            page_html = await self.tab.get_content() if self.tab else None
            await self.log_error(self.email, f"Error during initial navigation or login check: {e}", page_html)
            return False

    async def _execute_login_flow(self):
        """Executes the sequence of login steps."""
        login_steps = [
            EnterEmailStep(self),
            ClickNextEmailStep(self),
            HandleCodePageStep(self),
            EnterPasswordStep(self),
            ClickNextPasswordStep(self),
            HandleStaySignedInStep(self)
        ]

        for step in login_steps:
            success = await step.execute()
            if not success:
                logger.warning(f"Login step failed: {step.__class__.__name__}", extra={'email': self.email})
                return False
        
        logger.info("Sign-in process completed. Verifying final URL...", extra={'email': self.email})
        
        final_url = self.tab.url
        if self.rewards_url_check in final_url:
            logger.info("Successfully redirected to rewards page after login.", extra={'email': self.email})
            await self.stealth_browser.save_session_cookies(self.email)
            return True
        else:
            page_html = await self.tab.get_content()
            await self.log_error(self.email, f"Login completed but not on expected rewards page. Current URL: {final_url}", page_html)
            return False


    async def close_browser(self):
        """Closes the browser instance."""
        await self.stealth_browser.close_session()
        self.browser = None
        self.tab = None

    async def run(self):
        """Runs the full automation flow."""
        await self.start_browser()
        success = await self.navigate_and_sign_in(not self.session_loaded)
        if success:
            logger.info("Successfully logged in and navigated to rewards page.", extra={'email': self.email})
            
            logger.info("Attempting to extract user data from Bing.com home page.", extra={'email': self.email})
            user_data_bing_home = await self.user_data_extractor.get_data(["available_points", "today_points", "level"])
            logger.info(f"User data from Bing.com home page: {user_data_bing_home}", extra={'email': self.email})
            
            more_activities_task = CompleteMoreActivitiesStep(self)
            await more_activities_task.execute()



            logger.info("Keeping browser open for 5 seconds to observe final state.", extra={'email': self.email})
            await asyncio.sleep(5)
        await self.close_browser()
