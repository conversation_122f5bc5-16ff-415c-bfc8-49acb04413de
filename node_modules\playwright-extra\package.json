{"name": "playwright-extra", "version": "4.3.6", "description": "Teach playwright new tricks through plugins.", "repository": "berstend/puppeteer-extra", "homepage": "https://github.com/berstend/puppeteer-extra/tree/master/packages/playwright-extra#readme", "author": "be<PERSON><PERSON>", "license": "MIT", "typings": "dist/index.d.ts", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "files": ["dist"], "scripts": {"clean": "rimraf dist/*", "prebuild": "run-s clean", "build": "run-s build:tsc build:rollup ambient-dts", "build:tsc": "tsc --module commonjs", "build:rollup": "rollup -c rollup.config.ts", "docs": "echo \"No docs\"", "test": "yarn playwright test --config test/playwright.config.ts", "test-ci": "run-s test", "ambient-dts": "run-s ambient-dts-copy ambient-dts-fix-path", "ambient-dts-copy": "copyfiles -u 1 \"src/**/*.d.ts\" dist", "ambient-dts-fix-path": "replace-in-files --string='/// <reference path=\"../src/' --replacement='/// <reference path=\"../dist/' 'dist/**/*.d.ts'"}, "keywords": ["playwright", "playwright-extra", "stealth", "recaptcha", "user-preferences", "chrome", "headless", "pupeteer"], "engines": {"node": ">=12"}, "devDependencies": {"@playwright/test": "^1.23.1", "@types/debug": "^4.1.7", "@types/node": "^18.0.0", "esbuild": "^0.14.47", "esbuild-register": "^3.3.3", "npm-run-all": "^4.1.5", "playwright": "1.24.2", "prettier": "^2.7.1", "puppeteer-extra-plugin": "^3.2.3", "puppeteer-extra-plugin-anonymize-ua": "^2.4.5", "rimraf": "^3.0.0", "rollup": "^1.27.5", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-plugin-typescript2": "^0.25.2", "typescript": "4.4.3"}, "dependencies": {"debug": "^4.3.4"}, "peerDependencies": {"playwright": "*", "playwright-core": "*"}, "peerDependenciesMeta": {"playwright": {"optional": true}, "playwright-core": {"optional": true}}, "gitHead": "2f4a357f233b35a7a20f16ce007f5ef3f62765b9"}