import logging
import os
from logging.handlers import RotatingFileHandler
import colorlog # Import colorlog

# Custom formatter to handle optional 'email' field
class CustomFormatter(logging.Formatter):
    def format(self, record):
        if not hasattr(record, 'email'):
            record.email = 'N/A' # Default value if email is not provided
        return super().format(record)

def setup_logging(log_file='bot_activity.log', console_level=logging.INFO, file_level=logging.DEBUG):
    """
    Sets up a custom logger with handlers for both console and file output.
    Console output is concise and colored, while file output is detailed.
    """
    log_dir = 'logs'
    os.makedirs(log_dir, exist_ok=True)
    log_path = os.path.join(log_dir, log_file)

    logger = logging.getLogger('RewardsBotLogger')
    logger.setLevel(logging.DEBUG)

    if not logger.handlers:
        # Console Handler (for user-friendly, colored output)
        # Format: [LEVEL] [EMAIL] MESSAGE
        console_formatter = colorlog.ColoredFormatter(
            '%(log_color)s%(levelname)-8s%(reset)s %(blue)s[%(email)s]%(reset)s %(message)s',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            },
            secondary_log_colors={},
            style='%'
        )
        console_handler = logging.StreamHandler()
        console_handler.setLevel(console_level)
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)

        # File Handler (for detailed logging)
        # Format: TIMESTAMP - LOGGER_NAME - LEVEL - [EMAIL] - MESSAGE
        file_formatter = CustomFormatter('%(asctime)s - %(name)s - %(levelname)s - [%(email)s] - %(message)s')
        file_handler = RotatingFileHandler(log_path, maxBytes=5*1024*1024, backupCount=5, encoding='utf-8')
        file_handler.setLevel(file_level)
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)

    return logger

logger = setup_logging()

# Example usage (for testing purposes, will be removed later)
if __name__ == '__main__':
    logger.debug("This is a debug message.", extra={'email': '<EMAIL>'})
    logger.info("This is an info message.", extra={'email': '<EMAIL>'})
    logger.warning("This is a warning message.", extra={'email': '<EMAIL>'})
    logger.error("This is an error message.", extra={'email': '<EMAIL>'})
    logger.critical("This is a critical message.", extra={'email': '<EMAIL>'})
    logger.info("This is an info message without email.")