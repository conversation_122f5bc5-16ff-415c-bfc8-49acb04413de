{"name": "kind-of", "description": "Get the native type of a value.", "version": "2.0.1", "homepage": "https://github.com/jonschlinkert/kind-of", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/kind-of", "bugs": {"url": "https://github.com/jonschlinkert/kind-of/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha", "prepublish": "browserify -o browser.js -e index.js -s index --bare"}, "devDependencies": {"benchmarked": "^0.1.3", "chalk": "^0.5.1", "glob": "^4.3.5", "mocha": "^2.2.5", "should": "^4.6.1", "type-of": "^2.0.1", "typeof": "^1.0.0"}, "keywords": ["arguments", "array", "boolean", "check", "date", "function", "is", "is-type", "is-type-of", "kind", "kind-of", "number", "object", "regexp", "string", "test", "type", "type-of", "typeof", "types"], "dependencies": {"is-buffer": "^1.0.2"}}