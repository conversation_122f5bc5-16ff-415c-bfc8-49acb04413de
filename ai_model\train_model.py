"""
Training Script for Human Interaction AI Model

This script trains the AI model on collected human interaction data and provides
comprehensive evaluation and visualization of the training process.
"""

import os
import sys
import json
import argparse
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import torch
from datetime import datetime
import logging
from typing import Dict, List, Any

# Add the parent directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from ai_model.human_interaction_ai import HumanInteractionAI, InteractionConfig
    from ai_model.interaction_generator import HumanInteractionGenerator
    from ai_model.utils.data_loader import load_interaction_data
except ImportError:
    from human_interaction_ai import HumanInteractionAI, InteractionConfig
    from interaction_generator import HumanInteractionGenerator
    sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'utils'))
    from utils.data_loader import load_interaction_data

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'training_log_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TrainingVisualizer:
    """Visualizes training progress and model performance"""
    
    def __init__(self, save_dir: str = 'ai_model/training_plots/'):
        self.save_dir = save_dir
        os.makedirs(save_dir, exist_ok=True)
        
        # Set up plotting style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
    
    def plot_training_curves(self, mouse_losses: List[float], typing_losses: List[float], 
                           scrolling_losses: List[float]):
        """Plot training loss curves for all models"""
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # Mouse model losses
        if mouse_losses:
            axes[0].plot(mouse_losses, label='Training Loss', linewidth=2)
            axes[0].set_title('Mouse Movement Model Training')
            axes[0].set_xlabel('Epoch')
            axes[0].set_ylabel('Loss')
            axes[0].grid(True, alpha=0.3)
            axes[0].legend()
        
        # Typing model losses
        if typing_losses:
            axes[1].plot(typing_losses, label='Training Loss', linewidth=2)
            axes[1].set_title('Typing Pattern Model Training')
            axes[1].set_xlabel('Epoch')
            axes[1].set_ylabel('Loss')
            axes[1].grid(True, alpha=0.3)
            axes[1].legend()
        
        # Scrolling model losses
        if scrolling_losses:
            axes[2].plot(scrolling_losses, label='Training Loss', linewidth=2)
            axes[2].set_title('Scrolling Pattern Model Training')
            axes[2].set_xlabel('Epoch')
            axes[2].set_ylabel('Loss')
            axes[2].grid(True, alpha=0.3)
            axes[2].legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, 'training_curves.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_data_distribution(self, ai_model: HumanInteractionAI):
        """Plot distribution of training data"""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # Mouse data analysis
        if ai_model.mouse_data:
            mouse_features = np.concatenate(ai_model.mouse_data, axis=0)
            
            # Velocity distribution
            velocities = mouse_features[:, 5]  # Velocity column
            axes[0, 0].hist(velocities, bins=50, alpha=0.7, edgecolor='black')
            axes[0, 0].set_title('Mouse Velocity Distribution')
            axes[0, 0].set_xlabel('Velocity (pixels/ms)')
            axes[0, 0].set_ylabel('Frequency')
            
            # Distance distribution
            distances = mouse_features[:, 4]  # Distance column
            axes[1, 0].hist(distances, bins=50, alpha=0.7, edgecolor='black')
            axes[1, 0].set_title('Mouse Movement Distance Distribution')
            axes[1, 0].set_xlabel('Distance (pixels)')
            axes[1, 0].set_ylabel('Frequency')
        
        # Typing data analysis
        if ai_model.typing_data:
            typing_features = np.concatenate(ai_model.typing_data, axis=0)
            
            # Inter-keystroke timing
            timings = typing_features[:, 2]  # Time since last column
            axes[0, 1].hist(timings[timings < 1000], bins=50, alpha=0.7, edgecolor='black')  # Filter outliers
            axes[0, 1].set_title('Inter-keystroke Timing Distribution')
            axes[0, 1].set_xlabel('Time (ms)')
            axes[0, 1].set_ylabel('Frequency')
            
            # Error rate
            error_rates = typing_features[:, 4]  # Error type column
            error_counts = np.bincount(error_rates.astype(int))
            axes[1, 1].bar(range(len(error_counts)), error_counts, alpha=0.7)
            axes[1, 1].set_title('Typing Error Distribution')
            axes[1, 1].set_xlabel('Error Type (0=None, 1=Typo, 2=Extra, 3=Missing)')
            axes[1, 1].set_ylabel('Count')
        
        # Scrolling data analysis
        if ai_model.scrolling_data:
            scroll_features = np.concatenate(ai_model.scrolling_data, axis=0)
            
            # Scroll velocity distribution
            scroll_velocities = scroll_features[:, 2]  # Velocity column
            axes[0, 2].hist(scroll_velocities, bins=50, alpha=0.7, edgecolor='black')
            axes[0, 2].set_title('Scroll Velocity Distribution')
            axes[0, 2].set_xlabel('Velocity (pixels/ms)')
            axes[0, 2].set_ylabel('Frequency')
            
            # Scroll delta distribution
            scroll_deltas = scroll_features[:, 1]  # Scroll delta column
            axes[1, 2].hist(scroll_deltas, bins=50, alpha=0.7, edgecolor='black')
            axes[1, 2].set_title('Scroll Delta Distribution')
            axes[1, 2].set_xlabel('Scroll Delta (pixels)')
            axes[1, 2].set_ylabel('Frequency')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, 'data_distribution.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def plot_generated_samples(self, generator: HumanInteractionGenerator):
        """Plot samples of generated interactions"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # Generate sample mouse path
        mouse_path = generator.generate_mouse_path((0, 0), (500, 300))
        x_coords = [p[0] for p in mouse_path.points]
        y_coords = [p[1] for p in mouse_path.points]
        
        axes[0, 0].plot(x_coords, y_coords, 'b-', linewidth=2, alpha=0.7)
        axes[0, 0].scatter([0, 500], [0, 300], c=['green', 'red'], s=100, zorder=5)
        axes[0, 0].set_title('Generated Mouse Path')
        axes[0, 0].set_xlabel('X Coordinate')
        axes[0, 0].set_ylabel('Y Coordinate')
        axes[0, 0].grid(True, alpha=0.3)
        axes[0, 0].legend(['Path', 'Start', 'End'])
        
        # Plot mouse path timing
        axes[0, 1].plot(mouse_path.timings, 'g-', linewidth=2)
        axes[0, 1].set_title('Mouse Movement Timing')
        axes[0, 1].set_xlabel('Point Index')
        axes[0, 1].set_ylabel('Time (seconds)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Generate sample typing sequence
        typing_seq = generator.generate_typing_sequence("Hello, this is a test of human-like typing!")
        
        # Plot inter-keystroke intervals
        if len(typing_seq.timings) > 1:
            intervals = [typing_seq.timings[i] - typing_seq.timings[i-1] 
                        for i in range(1, len(typing_seq.timings))]
            axes[1, 0].plot(intervals, 'r-', linewidth=2)
            axes[1, 0].set_title('Generated Typing Intervals')
            axes[1, 0].set_xlabel('Keystroke Index')
            axes[1, 0].set_ylabel('Interval (seconds)')
            axes[1, 0].grid(True, alpha=0.3)
        
        # Generate sample scroll sequence
        scroll_seq = generator.generate_scroll_sequence(1000, 0)
        
        # Plot scroll deltas
        axes[1, 1].plot(scroll_seq.scroll_deltas, 'purple', linewidth=2)
        axes[1, 1].set_title('Generated Scroll Sequence')
        axes[1, 1].set_xlabel('Scroll Event Index')
        axes[1, 1].set_ylabel('Scroll Delta (pixels)')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.save_dir, 'generated_samples.png'), dpi=300, bbox_inches='tight')
        plt.close()

class ModelEvaluator:
    """Evaluates trained models and generates performance metrics"""
    
    def __init__(self, ai_model: HumanInteractionAI):
        self.ai_model = ai_model
    
    def evaluate_models(self) -> Dict[str, Any]:
        """Evaluate all trained models and return metrics"""
        metrics = {}
        
        # Evaluate mouse model
        if self.ai_model.mouse_data:
            metrics['mouse'] = self._evaluate_mouse_model()
        
        # Evaluate typing model
        if self.ai_model.typing_data:
            metrics['typing'] = self._evaluate_typing_model()
        
        # Evaluate scrolling model
        if self.ai_model.scrolling_data:
            metrics['scrolling'] = self._evaluate_scrolling_model()
        
        return metrics
    
    def _evaluate_mouse_model(self) -> Dict[str, float]:
        """Evaluate mouse movement model"""
        # This is a simplified evaluation - in practice, you'd want more sophisticated metrics
        total_sequences = len(self.ai_model.mouse_data)
        total_points = sum(len(seq) for seq in self.ai_model.mouse_data)
        avg_sequence_length = total_points / total_sequences if total_sequences > 0 else 0
        
        return {
            'total_sequences': total_sequences,
            'total_points': total_points,
            'avg_sequence_length': avg_sequence_length
        }
    
    def _evaluate_typing_model(self) -> Dict[str, float]:
        """Evaluate typing pattern model"""
        total_sequences = len(self.ai_model.typing_data)
        total_keystrokes = sum(len(seq) for seq in self.ai_model.typing_data)
        avg_sequence_length = total_keystrokes / total_sequences if total_sequences > 0 else 0
        
        return {
            'total_sequences': total_sequences,
            'total_keystrokes': total_keystrokes,
            'avg_sequence_length': avg_sequence_length
        }
    
    def _evaluate_scrolling_model(self) -> Dict[str, float]:
        """Evaluate scrolling pattern model"""
        total_sequences = len(self.ai_model.scrolling_data)
        total_events = sum(len(seq) for seq in self.ai_model.scrolling_data)
        avg_sequence_length = total_events / total_sequences if total_sequences > 0 else 0
        
        return {
            'total_sequences': total_sequences,
            'total_events': total_events,
            'avg_sequence_length': avg_sequence_length
        }

def create_training_report(ai_model: HumanInteractionAI, metrics: Dict[str, Any], 
                          config: InteractionConfig, training_time: float):
    """Create a comprehensive training report"""
    report = {
        'training_timestamp': datetime.now().isoformat(),
        'training_duration_seconds': training_time,
        'model_configuration': {
            'mouse_smoothness': config.mouse_smoothness,
            'mouse_speed_variance': config.mouse_speed_variance,
            'mouse_error_rate': config.mouse_error_rate,
            'typing_speed_wpm': config.typing_speed_wpm,
            'typing_error_rate': config.typing_error_rate,
            'typing_correction_delay': config.typing_correction_delay,
            'scroll_smoothness': config.scroll_smoothness,
            'scroll_overshoot_rate': config.scroll_overshoot_rate,
            'reaction_time_base': config.reaction_time_base,
            'reaction_time_variance': config.reaction_time_variance,
            'add_micro_movements': config.add_micro_movements,
            'randomize_timing': config.randomize_timing,
            'simulate_fatigue': config.simulate_fatigue
        },
        'training_data_summary': {
            'mouse_sequences': len(ai_model.mouse_data),
            'typing_sequences': len(ai_model.typing_data),
            'scrolling_sequences': len(ai_model.scrolling_data)
        },
        'model_metrics': metrics,
        'device_used': str(ai_model.device)
    }
    
    # Save report
    report_path = f'training_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
    with open(report_path, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"Training report saved to {report_path}")
    return report

def main():
    """Main training function"""
    parser = argparse.ArgumentParser(description='Train Human Interaction AI Model')
    parser.add_argument('--data-dir', default='data/', help='Directory containing training data')
    parser.add_argument('--epochs', type=int, default=50, help='Number of training epochs')
    parser.add_argument('--batch-size', type=int, default=256, help='Batch size for training (will be optimized automatically)')
    parser.add_argument('--learning-rate', type=float, default=0.001, help='Learning rate')
    parser.add_argument('--save-dir', default='saved_models/', help='Directory to save models')
    parser.add_argument('--config-file', help='JSON file with custom configuration')
    parser.add_argument('--visualize', action='store_true', help='Generate training visualizations')
    
    args = parser.parse_args()
    
    logger.info("Starting Human Interaction AI Model Training")
    logger.info(f"Arguments: {args}")
    
    # Load configuration
    if args.config_file and os.path.exists(args.config_file):
        with open(args.config_file, 'r') as f:
            config_dict = json.load(f)
        config = InteractionConfig(**config_dict)
        logger.info(f"Loaded configuration from {args.config_file}")
    else:
        config = InteractionConfig()
        logger.info("Using default configuration")
    
    # Initialize AI model
    ai_model = HumanInteractionAI(config)
    
    # Load training data
    logger.info(f"Loading training data from {args.data_dir}")
    start_time = datetime.now()
    ai_model.load_training_data(args.data_dir)
    
    if not any([ai_model.mouse_data, ai_model.typing_data, ai_model.scrolling_data]):
        logger.error("No training data found! Please ensure data files exist in the specified directory.")
        return
    
    # Initialize visualizer if requested
    visualizer = None
    if args.visualize:
        visualizer = TrainingVisualizer()
        visualizer.plot_data_distribution(ai_model)
        logger.info("Generated data distribution plots")
    
    # Train models
    logger.info("Starting model training...")
    training_start = datetime.now()
    ai_model.train_models(
        epochs=args.epochs,
        batch_size=args.batch_size,
        learning_rate=args.learning_rate
    )
    training_end = datetime.now()
    training_time = (training_end - training_start).total_seconds()
    
    # Save models
    logger.info(f"Saving models to {args.save_dir}")
    ai_model.save_models(args.save_dir)
    
    # Evaluate models
    logger.info("Evaluating trained models...")
    evaluator = ModelEvaluator(ai_model)
    metrics = evaluator.evaluate_models()
    
    # Generate visualizations
    if args.visualize:
        logger.info("Generating training visualizations...")
        generator = HumanInteractionGenerator(ai_model)
        visualizer.plot_generated_samples(generator)
        logger.info("Generated sample interaction plots")
    
    # Create training report
    report = create_training_report(ai_model, metrics, config, training_time)
    
    # Print summary
    total_time = (datetime.now() - start_time).total_seconds()
    logger.info("=" * 60)
    logger.info("TRAINING COMPLETED SUCCESSFULLY")
    logger.info("=" * 60)
    logger.info(f"Total time: {total_time:.2f} seconds")
    logger.info(f"Training time: {training_time:.2f} seconds")
    logger.info(f"Mouse sequences: {len(ai_model.mouse_data)}")
    logger.info(f"Typing sequences: {len(ai_model.typing_data)}")
    logger.info(f"Scrolling sequences: {len(ai_model.scrolling_data)}")
    logger.info(f"Models saved to: {args.save_dir}")
    if args.visualize:
        logger.info("Visualizations saved to: ai_model/training_plots/")
    logger.info("=" * 60)

if __name__ == "__main__":
    main()