<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Interaction Page</title>
    <style>
        body {
            font-family: sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 200vh; /* Make page scrollable */
            margin: 0;
        }
        #testInput {
            margin-top: 20px;
            padding: 10px;
            width: 300px;
        }
        .spacer {
            height: 1000px; /* Add space to force scrolling */
        }
        #scrollTarget {
            padding: 20px;
            background-color: lightcoral;
            color: white;
            text-align: center;
        }
        #draggableTarget {
            position: fixed;
            top: 50px;
            right: 50px;
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: 2px solid #333;
            border-radius: 8px;
            cursor: move;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
            user-select: none;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <h1>Test Interaction Page</h1>
    <input type="text" id="testInput" placeholder="Type here...">
    
    <!-- Draggable target element -->
    <div id="draggableTarget">DRAG</div>
    
    <div class="spacer"></div>
    <div id="scrollTarget">Scroll Target Element</div>
    
    <script>
        // Make the target draggable
        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;
        
        const dragElement = document.getElementById('draggableTarget');
        
        dragElement.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', dragMove);
        document.addEventListener('mouseup', dragEnd);
        
        function dragStart(e) {
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;
            
            if (e.target === dragElement) {
                isDragging = true;
            }
        }
        
        function dragMove(e) {
            if (isDragging) {
                e.preventDefault();
                
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;
                
                xOffset = currentX;
                yOffset = currentY;
                
                setTranslate(currentX, currentY, dragElement);
                
                // Auto-scroll when dragging near edges
                const margin = 50;
                const scrollSpeed = 10;
                
                if (e.clientX < margin) {
                    window.scrollBy(-scrollSpeed, 0);
                } else if (e.clientX > window.innerWidth - margin) {
                    window.scrollBy(scrollSpeed, 0);
                }
                
                if (e.clientY < margin) {
                    window.scrollBy(0, -scrollSpeed);
                } else if (e.clientY > window.innerHeight - margin) {
                    window.scrollBy(0, scrollSpeed);
                }
            }
        }
        
        function dragEnd(e) {
            initialX = currentX;
            initialY = currentY;
            
            isDragging = false;
        }
        
        function setTranslate(xPos, yPos, el) {
            el.style.transform = `translate3d(${xPos}px, ${yPos}px, 0)`;
        }
        
        // Mouse position indicator
        const mouseIndicator = document.createElement('div');
        mouseIndicator.style.cssText = `
            position: fixed;
            width: 10px;
            height: 10px;
            background: lime;
            border: 2px solid black;
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            transform: translate(-50%, -50%);
        `;
        document.body.appendChild(mouseIndicator);
        
        document.addEventListener('mousemove', (e) => {
            mouseIndicator.style.left = e.clientX + 'px';
            mouseIndicator.style.top = e.clientY + 'px';
        });

        // Add the missing setRandomTargetPosition function
        function setRandomTargetPosition() {
            const scrollTarget = document.getElementById('scrollTarget');
            if (!scrollTarget) return 0;
            
            // Generate a random position for the scroll target
            const minPos = 200;
            const maxPos = 800;
            const randomPos = Math.floor(Math.random() * (maxPos - minPos)) + minPos;
            
            // Move the scroll target to a random position
            scrollTarget.style.position = 'relative';
            scrollTarget.style.top = randomPos + 'px';
            
            return randomPos;
        }

        // Set random target position on page load
        window.addEventListener('load', () => {
            setRandomTargetPosition();
        });
    </script>
</body>
</html>