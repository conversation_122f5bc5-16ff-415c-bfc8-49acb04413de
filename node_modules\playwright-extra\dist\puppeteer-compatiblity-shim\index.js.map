{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/puppeteer-compatiblity-shim/index.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAAyB;AACzB,MAAM,KAAK,GAAG,IAAA,eAAK,EAAC,mCAAmC,CAAC,CAAA;AAqBjD,MAAM,gBAAgB,GAAG,CAAC,GAAY,EAAkB,EAAE;IAC/D,OAAO,SAAS,IAAK,GAAe,CAAA;AACtC,CAAC,CAAA;AAFY,QAAA,gBAAgB,oBAE5B;AACM,MAAM,iBAAiB,GAAG,CAAC,GAAY,EAAmB,EAAE;IACjE,OAAO,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAK,GAAgB,CAAC,CAAA;AAC3E,CAAC,CAAA;AAFY,QAAA,iBAAiB,qBAE7B;AACM,MAAM,mBAAmB,GAAG,CAAC,GAAY,EAAqB,EAAE;IACrE,OAAO,YAAY,IAAK,GAAkB,CAAA;AAC5C,CAAC,CAAA;AAFY,QAAA,mBAAmB,uBAE/B;AACM,MAAM,iBAAiB,GAAG,CAAC,GAAa,EAA2B,EAAE;IAC1E,OAAO,CAAC,CAAC,GAAG,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,CAAE,GAAW,CAAC,YAAY,CAAA;AACxE,CAAC,CAAA;AAFY,QAAA,iBAAiB,qBAE7B;AAED,MAAM,KAAK,GAAG;IACZ,YAAY,EAAE,IAAI,GAAG,EAAsC;IAC3D,UAAU,EAAE;QACV,IAAI,EAAE,IAAI,GAAG,EAAqC;QAClD,OAAO,EAAE,IAAI,GAAG,EAA6B;KAC9C;CACF,CAAA;AAED,oFAAoF;AACpF,SAAgB,kBAAkB,CAEhC,MAAa;IACb,IAAI,CAAC,MAAM,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;QACzC,OAAO,MAAM,CAAA;KACd;IACD,IAAI,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;QAClC,OAAO,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,CAAU,CAAA;KAC/C;IACD,IAAI,IAAA,yBAAiB,EAAC,MAAM,CAAC,EAAE;QAC7B,OAAO,MAAM,CAAA;KACd;IACD,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;IACpD,IAAI,IAAA,wBAAgB,EAAC,MAAM,CAAC,IAAI,IAAA,yBAAiB,EAAC,MAAM,CAAC,EAAE;QACzD,MAAM,IAAI,GAAG,cAAc,CAAC,MAAM,CAAC,CAAA;QACnC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QACpC,OAAO,IAAa,CAAA;KACrB;IACD,IAAI,IAAA,2BAAmB,EAAC,MAAM,CAAC,EAAE;QAC/B,MAAM,IAAI,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAA;QACtC,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QACpC,OAAO,IAAa,CAAA;KACrB;IACD,KAAK,CAAC,0BAA0B,EAAE,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAA;IAC1D,OAAO,MAAM,CAAA;AACf,CAAC;AAzBD,gDAyBC;AAED,qCAAqC;AACrC,MAAM,cAAc,GAAG;IACrB,IAAI,EAAE,KAAK,EAAE,GAAG,IAAW,EAAE,EAAE;QAC7B,KAAK,CAAC,yBAAyB,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;IAChD,CAAC;IACD,EAAE,EAAE,CAAC,GAAG,IAAW,EAAE,EAAE;QACrB,KAAK,CAAC,yBAAyB,EAAE,IAAI,EAAE,IAAI,CAAC,CAAA;IAC9C,CAAC;CACe,CAAA;AAEX,KAAK,UAAU,iBAAiB,CAAC,IAAwB;IAC9D,IAAI,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;IAC7C,IAAI,OAAO,EAAE;QACX,KAAK,CAAC,iCAAiC,CAAC,CAAA;QACxC,OAAO,OAAO,CAAA;KACf;IACD,KAAK,CAAC,4BAA4B,CAAC,CAAA;IACnC,MAAM,OAAO,GAAG,IAAA,yBAAiB,EAAC,IAAI,CAAC;QACrC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE;QACvB,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA;IAClB,IAAI;QACF,OAAO,GAAG,MAAM,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;QAC3C,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;QACxC,OAAO,OAAO,CAAA;KACf;IAAC,OAAO,GAAQ,EAAE;QACjB,KAAK,CAAC,kDAAkD,EAAE,GAAG,CAAC,OAAO,CAAC,CAAA;QACtE,KAAK,CACH,kHAAkH,CACnH,CAAA;KACF;IACD,OAAO,cAAc,CAAA;AACvB,CAAC;AArBD,8CAqBC;AAEM,KAAK,UAAU,oBAAoB,CAAC,OAAmB;IAC5D,IAAI,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;IACnD,IAAI,OAAO,EAAE;QACX,KAAK,CAAC,oCAAoC,CAAC,CAAA;QAC3C,OAAO,OAAO,CAAA;KACf;IACD,KAAK,CAAC,+BAA+B,CAAC,CAAA;IACtC,IAAI;QACF,OAAO,GAAG,MAAM,OAAO,CAAC,oBAAoB,EAAE,CAAA;QAC9C,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAC9C,OAAO,OAAO,CAAA;KACf;IAAC,OAAO,GAAQ,EAAE;QACjB,KAAK,CAAC,qDAAqD,EAAE,GAAG,CAAC,OAAO,CAAC,CAAA;QACzE,KAAK,CACH,qHAAqH,CACtH,CAAA;KACF;IACD,OAAO,cAAc,CAAA;AACvB,CAAC;AAlBD,oDAkBC;AAED,SAAgB,cAAc,CAAC,IAAwB;IACrD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IACxD,MAAM,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,EAAE;QAC3B,GAAG,CAAC,MAAM,EAAE,IAAI;YACd,IAAI,IAAI,KAAK,cAAc,IAAI,IAAI,KAAK,cAAc,EAAE;gBACtD,OAAO,IAAI,CAAA;aACZ;YACD,KAAK,CAAC,YAAY,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;YAChC,IAAI,IAAI,KAAK,SAAS,EAAE;gBACtB,OAAO,GAAG,EAAE,CAAC,CAAC;oBACZ,IAAI,EAAE,KAAK,EAAE,MAAc,EAAE,MAAW,EAAE,EAAE;wBAC1C,MAAM,OAAO,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,CAAA;wBAC7C,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,MAAa,EAAE,MAAM,CAAC,CAAA;oBAClD,CAAC;oBACD,EAAE,EAAE,CAAC,KAAa,EAAE,QAAa,EAAE,EAAE;wBACnC,iBAAiB,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;4BACrC,OAAO,CAAC,EAAE,CAAC,KAAY,EAAE,QAAQ,CAAC,CAAA;wBACpC,CAAC,CAAC,CAAA;oBACJ,CAAC;iBACF,CAAC,CAAA;aACH;YACD,IAAI,IAAI,KAAK,cAAc,EAAE;gBAC3B,OAAO,KAAK,EAAE,OAAgB,EAAE,EAAE;oBAChC,MAAM,OAAO,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,CAAA;oBAC7C,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,mBAAmB,EAAE;wBAC7C,OAAO;qBACR,CAAC,CAAA;gBACJ,CAAC,CAAA;aACF;YACD,IAAI,IAAI,KAAK,cAAc,EAAE;gBAC3B,OAAO,KAAK,EAAE,SAAiB,EAAE,iBAAuB,EAAE,EAAE;oBAC1D,MAAM,OAAO,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,CAAA;oBAC7C,OAAO,MAAM,OAAO,CAAC,IAAI,CAAC,gCAAgC,EAAE;wBAC1D,SAAS;wBACT,iBAAiB;qBAClB,CAAC,CAAA;gBACJ,CAAC,CAAA;aACF;YACD,IAAI,IAAI,KAAK,SAAS,EAAE;gBACtB,IAAI,IAAA,wBAAgB,EAAC,IAAI,CAAC,EAAE;oBAC1B,OAAO,GAAG,EAAE;wBACV,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC,OAAO,EAAE,CAAA;wBACtC,IAAI,CAAC,OAAO,EAAE;4BACZ,KAAK,CACH,4EAA4E,CAC7E,CAAA;4BACD,wEAAwE;4BACxE,OAAO,GAAG,IAAW,CAAA;yBACtB;wBACD,OAAO,kBAAkB,CAAC,OAAO,CAAC,CAAA;oBACpC,CAAC,CAAA;iBACF;aACF;YACD,IAAI,IAAI,KAAK,uBAAuB,EAAE;gBACpC,IAAI,IAAA,wBAAgB,EAAC,IAAI,CAAC,EAAE;oBAC1B,OAAO,KAAK,WAAW,YAA0B,EAAE,GAAG,IAAW;wBAC/D,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAA;oBACxD,CAAC,CAAA;iBACF;aACF;YACD,2GAA2G;YAC3G,IAAI,IAAI,KAAK,WAAW,EAAE;gBACxB,OAAO,KAAK,EAAE,OAAgB,EAAE,EAAE;oBAChC,MAAM,OAAO,GAAG,MAAM,iBAAiB,CAAC,IAAI,CAAC,CAAA;oBAC7C,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAA;oBACrD,OAAO,IAAI,CAAC,SAAS,CAAA;gBACvB,CAAC,CAAA;aACF;YACD,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAClC,CAAC;KACF,CAAC,CAAA;IACF,OAAO,IAAI,CAAA;AACb,CAAC;AAxED,wCAwEC;AAED,SAAgB,iBAAiB,CAAC,OAAmB;IACnD,MAAM,KAAK,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IACxD,MAAM,IAAI,GAAG,IAAI,KAAK,CAAC,OAAO,EAAE;QAC9B,GAAG,CAAC,MAAM,EAAE,IAAI;YACd,IAAI,IAAI,KAAK,cAAc,IAAI,IAAI,KAAK,cAAc,EAAE;gBACtD,OAAO,IAAI,CAAA;aACZ;YACD,KAAK,CAAC,eAAe,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;YACnC,IAAI,IAAI,KAAK,OAAO,EAAE;gBACpB,OAAO,GAAG,EAAE,CACV,OAAO;qBACJ,QAAQ,EAAE;qBACV,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;aACnE;YACD,IAAI,IAAI,KAAK,WAAW,EAAE;gBACxB,OAAO,KAAK,IAAI,EAAE;oBAChB,MAAM,OAAO,GAAG,MAAM,oBAAoB,CAAC,OAAO,CAAC,CAAA;oBACnD,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAA;oBACrD,OAAO,IAAI,CAAC,SAAS,CAAA;gBACvB,CAAC,CAAA;aACF;YACD,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;QAClC,CAAC;KACF,CAAC,CAAA;IACF,OAAO,IAAI,CAAA;AACb,CAAC;AAzBD,8CAyBC"}