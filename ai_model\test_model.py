"""
Testing and Demonstration Script for Human Interaction AI Model

This script tests the trained AI model and demonstrates its capabilities
in generating human-like interactions.
"""

import os
import sys
import json
import time
import asyncio
import argparse
import matplotlib.pyplot as plt
import numpy as np
from typing import List, Tuple, Dict
import logging

# Add the parent directory to the path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from ai_model.human_interaction_ai import HumanInteractionAI, InteractionConfig
    from ai_model.interaction_generator import HumanInteractionGenerator, MousePath, TypingSequence, ScrollSequence
except ImportError:
    from human_interaction_ai import HumanInteractionAI, InteractionConfig
    from interaction_generator import HumanInteractionGenerator, MousePath, TypingSequence, ScrollSequence

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InteractionTester:
    """Tests and demonstrates the AI model capabilities"""
    
    def __init__(self, ai_model: HumanInteractionAI):
        self.ai_model = ai_model
        self.generator = HumanInteractionGenerator(ai_model)
    
    def test_mouse_generation(self, num_tests: int = 10) -> List[MousePath]:
        """Test mouse movement generation with various scenarios"""
        logger.info(f"Testing mouse movement generation with {num_tests} scenarios...")
        
        test_scenarios = [
            # Short movements
            ((100, 100), (200, 150)),
            ((50, 300), (150, 250)),
            
            # Medium movements
            ((0, 0), (400, 300)),
            ((200, 100), (600, 400)),
            
            # Long movements
            ((0, 0), (800, 600)),
            ((100, 500), (700, 100)),
            
            # Diagonal movements
            ((0, 0), (500, 500)),
            ((500, 0), (0, 500)),
            
            # Small precise movements
            ((300, 300), (310, 305)),
            ((150, 200), (155, 198))
        ]
        
        paths = []
        for i, (start, end) in enumerate(test_scenarios[:num_tests]):
            logger.info(f"  Generating path {i+1}: {start} -> {end}")
            path = self.generator.generate_mouse_path(start, end)
            paths.append(path)
            
            # Log some statistics
            distance = np.sqrt((end[0] - start[0])**2 + (end[1] - start[1])**2)
            logger.info(f"    Distance: {distance:.1f}px, Duration: {path.total_duration:.3f}s, Points: {len(path.points)}")
        
        return paths
    
    def test_typing_generation(self, test_texts: List[str] = None) -> List[TypingSequence]:
        """Test typing pattern generation with various texts"""
        if test_texts is None:
            test_texts = [
                "Hello World!",
                "The quick brown fox jumps over the lazy dog.",
                "This is a test of human-like typing with errors and corrections.",
                "Programming is fun and challenging at the same time.",
                "AI models can learn complex patterns from human behavior data.",
                "<EMAIL>",
                "Password123!",
                "https://www.example.com/path/to/page",
                "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
                "Testing special characters: @#$%^&*()_+-=[]{}|;':\",./<>?"
            ]
        
        logger.info(f"Testing typing generation with {len(test_texts)} texts...")
        
        sequences = []
        for i, text in enumerate(test_texts):
            logger.info(f"  Generating typing for: '{text[:30]}{'...' if len(text) > 30 else ''}'")
            sequence = self.generator.generate_typing_sequence(text)
            sequences.append(sequence)
            
            # Log statistics
            total_time = sequence.timings[-1] if sequence.timings else 0
            wpm = (len(text.split()) / total_time * 60) if total_time > 0 else 0
            error_count = len(sequence.errors)
            logger.info(f"    Duration: {total_time:.2f}s, WPM: {wpm:.1f}, Errors: {error_count}")
        
        return sequences
    
    def test_scroll_generation(self, num_tests: int = 8) -> List[ScrollSequence]:
        """Test scrolling pattern generation with various scenarios"""
        logger.info(f"Testing scroll generation with {num_tests} scenarios...")
        
        test_scenarios = [
            # Short scrolls
            (0, 200),
            (100, 300),
            
            # Medium scrolls
            (0, 800),
            (200, 1000),
            
            # Long scrolls
            (0, 2000),
            (500, 2500),
            
            # Reverse scrolls
            (1000, 200),
            (1500, 0)
        ]
        
        sequences = []
        for i, (start, target) in enumerate(test_scenarios[:num_tests]):
            logger.info(f"  Generating scroll {i+1}: {start} -> {target}")
            sequence = self.generator.generate_scroll_sequence(target, start)
            sequences.append(sequence)
            
            # Log statistics
            total_distance = abs(target - start)
            total_time = sequence.timings[-1] if sequence.timings else 0
            scroll_events = len(sequence.scroll_deltas)
            logger.info(f"    Distance: {total_distance}px, Duration: {total_time:.2f}s, Events: {scroll_events}")
        
        return sequences
    
    def analyze_human_likeness(self, mouse_paths: List[MousePath], 
                             typing_sequences: List[TypingSequence],
                             scroll_sequences: List[ScrollSequence]) -> Dict:
        """Analyze the human-likeness of generated interactions"""
        logger.info("Analyzing human-likeness of generated interactions...")
        
        analysis = {}
        
        # Mouse movement analysis
        if mouse_paths:
            mouse_analysis = self._analyze_mouse_paths(mouse_paths)
            analysis['mouse'] = mouse_analysis
        
        # Typing analysis
        if typing_sequences:
            typing_analysis = self._analyze_typing_sequences(typing_sequences)
            analysis['typing'] = typing_analysis
        
        # Scrolling analysis
        if scroll_sequences:
            scroll_analysis = self._analyze_scroll_sequences(scroll_sequences)
            analysis['scrolling'] = scroll_analysis
        
        return analysis
    
    def _analyze_mouse_paths(self, paths: List[MousePath]) -> Dict:
        """Analyze mouse path characteristics"""
        all_velocities = []
        all_accelerations = []
        path_smoothness = []
        
        for path in paths:
            if len(path.points) < 3:
                continue
            
            # Calculate velocities and accelerations
            velocities = []
            for i in range(1, len(path.points)):
                dx = path.points[i][0] - path.points[i-1][0]
                dy = path.points[i][1] - path.points[i-1][1]
                dt = path.timings[i] - path.timings[i-1]
                if dt > 0:
                    velocity = np.sqrt(dx**2 + dy**2) / dt
                    velocities.append(velocity)
            
            accelerations = []
            for i in range(1, len(velocities)):
                dt = path.timings[i+1] - path.timings[i]
                if dt > 0:
                    acceleration = (velocities[i] - velocities[i-1]) / dt
                    accelerations.append(acceleration)
            
            all_velocities.extend(velocities)
            all_accelerations.extend(accelerations)
            
            # Calculate smoothness (variance in velocity)
            if velocities:
                smoothness = 1.0 / (1.0 + np.var(velocities))
                path_smoothness.append(smoothness)
        
        return {
            'avg_velocity': np.mean(all_velocities) if all_velocities else 0,
            'velocity_std': np.std(all_velocities) if all_velocities else 0,
            'avg_acceleration': np.mean(all_accelerations) if all_accelerations else 0,
            'acceleration_std': np.std(all_accelerations) if all_accelerations else 0,
            'avg_smoothness': np.mean(path_smoothness) if path_smoothness else 0,
            'total_paths': len(paths)
        }
    
    def _analyze_typing_sequences(self, sequences: List[TypingSequence]) -> Dict:
        """Analyze typing sequence characteristics"""
        all_intervals = []
        error_rates = []
        correction_delays = []
        
        for sequence in sequences:
            if len(sequence.timings) < 2:
                continue
            
            # Calculate inter-keystroke intervals
            intervals = []
            for i in range(1, len(sequence.timings)):
                interval = sequence.timings[i] - sequence.timings[i-1]
                intervals.append(interval)
            
            all_intervals.extend(intervals)
            
            # Calculate error rate
            total_chars = len([k for k in sequence.keys if k != 'Backspace'])
            error_rate = len(sequence.errors) / total_chars if total_chars > 0 else 0
            error_rates.append(error_rate)
            
            # Calculate correction delays
            for correction in sequence.corrections:
                correction_delays.append(correction['delay'])
        
        return {
            'avg_interval': np.mean(all_intervals) if all_intervals else 0,
            'interval_std': np.std(all_intervals) if all_intervals else 0,
            'avg_error_rate': np.mean(error_rates) if error_rates else 0,
            'avg_correction_delay': np.mean(correction_delays) if correction_delays else 0,
            'total_sequences': len(sequences)
        }
    
    def _analyze_scroll_sequences(self, sequences: List[ScrollSequence]) -> Dict:
        """Analyze scroll sequence characteristics"""
        all_deltas = []
        all_intervals = []
        pause_frequencies = []
        
        for sequence in sequences:
            all_deltas.extend([abs(d) for d in sequence.scroll_deltas])
            
            # Calculate intervals
            for i in range(1, len(sequence.timings)):
                interval = sequence.timings[i] - sequence.timings[i-1]
                all_intervals.append(interval)
            
            # Calculate pause frequency
            pause_count = sum(1 for p in sequence.pauses if p > 0)
            pause_frequency = pause_count / len(sequence.pauses) if sequence.pauses else 0
            pause_frequencies.append(pause_frequency)
        
        return {
            'avg_scroll_delta': np.mean(all_deltas) if all_deltas else 0,
            'scroll_delta_std': np.std(all_deltas) if all_deltas else 0,
            'avg_interval': np.mean(all_intervals) if all_intervals else 0,
            'avg_pause_frequency': np.mean(pause_frequencies) if pause_frequencies else 0,
            'total_sequences': len(sequences)
        }
    
    def visualize_results(self, mouse_paths: List[MousePath], 
                         typing_sequences: List[TypingSequence],
                         scroll_sequences: List[ScrollSequence],
                         save_dir: str = 'ai_model/test_results/'):
        """Create visualizations of test results"""
        os.makedirs(save_dir, exist_ok=True)
        
        # Mouse path visualization
        if mouse_paths:
            self._plot_mouse_paths(mouse_paths, save_dir)
        
        # Typing visualization
        if typing_sequences:
            self._plot_typing_patterns(typing_sequences, save_dir)
        
        # Scrolling visualization
        if scroll_sequences:
            self._plot_scroll_patterns(scroll_sequences, save_dir)
    
    def _plot_mouse_paths(self, paths: List[MousePath], save_dir: str):
        """Plot mouse movement paths"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # Plot first few paths
        for i, path in enumerate(paths[:4]):
            row, col = i // 2, i % 2
            x_coords = [p[0] for p in path.points]
            y_coords = [p[1] for p in path.points]
            
            axes[row, col].plot(x_coords, y_coords, 'b-', linewidth=2, alpha=0.7)
            axes[row, col].scatter([x_coords[0]], [y_coords[0]], c='green', s=100, zorder=5, label='Start')
            axes[row, col].scatter([x_coords[-1]], [y_coords[-1]], c='red', s=100, zorder=5, label='End')
            axes[row, col].set_title(f'Mouse Path {i+1}')
            axes[row, col].set_xlabel('X Coordinate')
            axes[row, col].set_ylabel('Y Coordinate')
            axes[row, col].grid(True, alpha=0.3)
            axes[row, col].legend()
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'mouse_paths.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_typing_patterns(self, sequences: List[TypingSequence], save_dir: str):
        """Plot typing pattern analysis"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # Collect all intervals
        all_intervals = []
        for sequence in sequences:
            if len(sequence.timings) > 1:
                intervals = [sequence.timings[i] - sequence.timings[i-1] 
                           for i in range(1, len(sequence.timings))]
                all_intervals.extend(intervals)
        
        # Plot interval distribution
        if all_intervals:
            axes[0, 0].hist(all_intervals, bins=30, alpha=0.7, edgecolor='black')
            axes[0, 0].set_title('Inter-keystroke Interval Distribution')
            axes[0, 0].set_xlabel('Interval (seconds)')
            axes[0, 0].set_ylabel('Frequency')
        
        # Plot error rates
        error_rates = []
        for sequence in sequences:
            total_chars = len([k for k in sequence.keys if k != 'Backspace'])
            error_rate = len(sequence.errors) / total_chars if total_chars > 0 else 0
            error_rates.append(error_rate * 100)  # Convert to percentage
        
        if error_rates:
            axes[0, 1].bar(range(len(error_rates)), error_rates, alpha=0.7)
            axes[0, 1].set_title('Error Rate by Sequence')
            axes[0, 1].set_xlabel('Sequence Index')
            axes[0, 1].set_ylabel('Error Rate (%)')
        
        # Plot typing speed over time for first sequence
        if sequences and len(sequences[0].timings) > 10:
            sequence = sequences[0]
            speeds = []
            window_size = 10
            for i in range(window_size, len(sequence.timings)):
                chars_in_window = window_size
                time_window = sequence.timings[i] - sequence.timings[i-window_size]
                if time_window > 0:
                    wpm = (chars_in_window / 5) / (time_window / 60)  # Assuming 5 chars per word
                    speeds.append(wpm)
            
            if speeds:
                axes[1, 0].plot(speeds, linewidth=2)
                axes[1, 0].set_title('Typing Speed Over Time (First Sequence)')
                axes[1, 0].set_xlabel('Time Window')
                axes[1, 0].set_ylabel('WPM')
        
        # Plot correction delays
        correction_delays = []
        for sequence in sequences:
            for correction in sequence.corrections:
                correction_delays.append(correction['delay'])
        
        if correction_delays:
            axes[1, 1].hist(correction_delays, bins=20, alpha=0.7, edgecolor='black')
            axes[1, 1].set_title('Correction Delay Distribution')
            axes[1, 1].set_xlabel('Delay (seconds)')
            axes[1, 1].set_ylabel('Frequency')
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'typing_patterns.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_scroll_patterns(self, sequences: List[ScrollSequence], save_dir: str):
        """Plot scrolling pattern analysis"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # Plot scroll deltas
        all_deltas = []
        for sequence in sequences:
            all_deltas.extend(sequence.scroll_deltas)
        
        if all_deltas:
            axes[0, 0].hist(all_deltas, bins=30, alpha=0.7, edgecolor='black')
            axes[0, 0].set_title('Scroll Delta Distribution')
            axes[0, 0].set_xlabel('Scroll Delta (pixels)')
            axes[0, 0].set_ylabel('Frequency')
        
        # Plot scroll timing for first sequence
        if sequences and len(sequences[0].timings) > 1:
            sequence = sequences[0]
            intervals = [sequence.timings[i] - sequence.timings[i-1] 
                        for i in range(1, len(sequence.timings))]
            axes[0, 1].plot(intervals, linewidth=2)
            axes[0, 1].set_title('Scroll Timing (First Sequence)')
            axes[0, 1].set_xlabel('Scroll Event Index')
            axes[0, 1].set_ylabel('Interval (seconds)')
        
        # Plot cumulative scroll position for first sequence
        if sequences:
            sequence = sequences[0]
            cumulative_position = [0]
            for delta in sequence.scroll_deltas:
                cumulative_position.append(cumulative_position[-1] + delta)
            
            axes[1, 0].plot(cumulative_position, linewidth=2)
            axes[1, 0].set_title('Cumulative Scroll Position (First Sequence)')
            axes[1, 0].set_xlabel('Scroll Event Index')
            axes[1, 0].set_ylabel('Position (pixels)')
        
        # Plot pause frequency
        pause_frequencies = []
        for sequence in sequences:
            pause_count = sum(1 for p in sequence.pauses if p > 0)
            pause_frequency = pause_count / len(sequence.pauses) if sequence.pauses else 0
            pause_frequencies.append(pause_frequency * 100)  # Convert to percentage
        
        if pause_frequencies:
            axes[1, 1].bar(range(len(pause_frequencies)), pause_frequencies, alpha=0.7)
            axes[1, 1].set_title('Pause Frequency by Sequence')
            axes[1, 1].set_xlabel('Sequence Index')
            axes[1, 1].set_ylabel('Pause Frequency (%)')
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_dir, 'scroll_patterns.png'), dpi=300, bbox_inches='tight')
        plt.close()

def benchmark_performance(generator: HumanInteractionGenerator, num_iterations: int = 100):
    """Benchmark the performance of interaction generation"""
    logger.info(f"Benchmarking performance with {num_iterations} iterations...")
    
    # Mouse movement benchmark
    start_time = time.time()
    for _ in range(num_iterations):
        generator.generate_mouse_path((0, 0), (500, 300))
    mouse_time = time.time() - start_time
    
    # Typing benchmark
    start_time = time.time()
    for _ in range(num_iterations):
        generator.generate_typing_sequence("Hello, this is a test!")
    typing_time = time.time() - start_time
    
    # Scrolling benchmark
    start_time = time.time()
    for _ in range(num_iterations):
        generator.generate_scroll_sequence(1000, 0)
    scroll_time = time.time() - start_time
    
    logger.info("Performance Benchmark Results:")
    logger.info(f"  Mouse generation: {mouse_time/num_iterations*1000:.2f} ms per path")
    logger.info(f"  Typing generation: {typing_time/num_iterations*1000:.2f} ms per sequence")
    logger.info(f"  Scroll generation: {scroll_time/num_iterations*1000:.2f} ms per sequence")
    
    return {
        'mouse_ms_per_generation': mouse_time/num_iterations*1000,
        'typing_ms_per_generation': typing_time/num_iterations*1000,
        'scroll_ms_per_generation': scroll_time/num_iterations*1000
    }

def main():
    """Main testing function"""
    parser = argparse.ArgumentParser(description='Test Human Interaction AI Model')
    parser.add_argument('--model-dir', default='saved_models/', 
                       help='Directory containing trained models')
    parser.add_argument('--config-file', help='JSON file with custom configuration')
    parser.add_argument('--output-dir', default='ai_model/test_results/', 
                       help='Directory to save test results')
    parser.add_argument('--visualize', action='store_true', 
                       help='Generate test visualizations')
    parser.add_argument('--benchmark', action='store_true', 
                       help='Run performance benchmark')
    parser.add_argument('--num-tests', type=int, default=10, 
                       help='Number of test cases per interaction type')
    
    args = parser.parse_args()
    
    logger.info("Starting Human Interaction AI Model Testing")
    logger.info(f"Arguments: {args}")
    
    # Load configuration
    if args.config_file and os.path.exists(args.config_file):
        with open(args.config_file, 'r') as f:
            config_dict = json.load(f)
        config = InteractionConfig(**config_dict)
        logger.info(f"Loaded configuration from {args.config_file}")
    else:
        config = InteractionConfig()
        logger.info("Using default configuration")
    
    # Initialize AI model
    ai_model = HumanInteractionAI(config)
    
    # Load trained models
    if os.path.exists(args.model_dir):
        logger.info(f"Loading models from {args.model_dir}")
        ai_model.load_models(args.model_dir)
    else:
        logger.warning(f"Model directory {args.model_dir} not found. Using untrained models.")
    
    # Initialize tester
    tester = InteractionTester(ai_model)
    
    # Run tests
    logger.info("Running interaction generation tests...")
    
    # Test mouse movements
    mouse_paths = tester.test_mouse_generation(args.num_tests)
    
    # Test typing
    typing_sequences = tester.test_typing_generation()
    
    # Test scrolling
    scroll_sequences = tester.test_scroll_generation(args.num_tests)
    
    # Analyze results
    analysis = tester.analyze_human_likeness(mouse_paths, typing_sequences, scroll_sequences)
    
    # Print analysis results
    logger.info("Human-likeness Analysis Results:")
    for interaction_type, metrics in analysis.items():
        logger.info(f"  {interaction_type.upper()}:")
        for metric, value in metrics.items():
            if isinstance(value, float):
                logger.info(f"    {metric}: {value:.4f}")
            else:
                logger.info(f"    {metric}: {value}")
    
    # Generate visualizations
    if args.visualize:
        logger.info("Generating test visualizations...")
        tester.visualize_results(mouse_paths, typing_sequences, scroll_sequences, args.output_dir)
        logger.info(f"Visualizations saved to {args.output_dir}")
    
    # Run benchmark
    if args.benchmark:
        benchmark_results = benchmark_performance(tester.generator)
        logger.info("Benchmark completed.")
    
    # Save test report
    test_report = {
        'test_timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'configuration': config.__dict__,
        'test_parameters': {
            'num_mouse_tests': len(mouse_paths),
            'num_typing_tests': len(typing_sequences),
            'num_scroll_tests': len(scroll_sequences)
        },
        'analysis_results': analysis
    }
    
    if args.benchmark:
        test_report['benchmark_results'] = benchmark_results
    
    report_path = os.path.join(args.output_dir, f'test_report_{int(time.time())}.json')
    os.makedirs(args.output_dir, exist_ok=True)
    with open(report_path, 'w') as f:
        json.dump(test_report, f, indent=2)
    
    logger.info(f"Test report saved to {report_path}")
    logger.info("Testing completed successfully!")

if __name__ == "__main__":
    main()