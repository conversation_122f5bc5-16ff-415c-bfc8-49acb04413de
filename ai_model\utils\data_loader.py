import json
import os

def load_interaction_data(directory='data/'):
    """
    Loads and parses JSON datasets from the specified directory.

    Args:
        directory (str): The path to the directory containing JSON interaction data files.

    Returns:
        list: A list of dictionaries, where each dictionary represents a parsed JSON dataset.

    Example Usage with AI Frameworks (PyTorch/TensorFlow):

    # Assuming you have a list of file paths or you iterate through them
    # For PyTorch:
    # from torch.utils.data import Dataset, DataLoader
    # class InteractionDataset(Dataset):
    #     def __init__(self, data_dir):
    #         self.data = load_interaction_data(data_dir)
    #
    #     def __len__(self):
    #         return len(self.data)
    #
    #     def __getitem__(self, idx):
    #         # Process self.data[idx] for your model input
    #         # Example: extract features, convert to tensors
    #         interaction = self.data[idx]
    #         # For example, if you want mouse movements:
    #         # mouse_events = interaction.get('mouse_movement', [])
    #         # return torch.tensor(mouse_events)
    #         return interaction # Or a processed version

    # dataset = InteractionDataset('data/')
    # dataloader = DataLoader(dataset, batch_size=32, shuffle=True)
    # for batch in dataloader:
    #     # Your training loop here
    #     pass

    # For TensorFlow:
    # import tensorflow as tf
    # def data_generator(data_dir):
    #     for interaction in load_interaction_data(data_dir):
    #         # Process interaction for your model input
    #         # Example: extract features, convert to tf.constant
    #         # yield {'mouse_movement': interaction.get('mouse_movement', [])}
    #         yield interaction # Or a processed version

    # dataset = tf.data.Dataset.from_generator(
    #     lambda: data_generator('data/'),
    #     output_signature={
    #         # Define the structure of your interaction data here
    #         # e.g., 'mouse_movement': tf.TensorSpec(shape=(None, 3), dtype=tf.int32),
    #         # 'typing_events': tf.TensorSpec(shape=(None, 2), dtype=tf.string),
    #         # ... based on your DATA_STRUCTURE.md
    #     }
    # )
    # dataset = dataset.batch(32).prefetch(tf.data.AUTOTUNE)
    # for batch in dataset:
    #     # Your training loop here
    #     pass
    """
    all_data = []
    for filename in os.listdir(directory):
        if filename.endswith('.json'):
            filepath = os.path.join(directory, filename)
            try:
                with open(filepath, 'r') as f:
                    data = json.load(f)
                    all_data.append(data)
            except json.JSONDecodeError as e:
                print(f"Error decoding JSON from {filepath}: {e}")
            except Exception as e:
                print(f"An error occurred while reading {filepath}: {e}")
    return all_data

if __name__ == '__main__':
    # Example of how to use the function
    print("Loading interaction data from 'data/' directory...")
    loaded_data = load_interaction_data('data/')
    print(f"Loaded {len(loaded_data)} interaction datasets.")
    if loaded_data:
        print("\nFirst dataset example:")
        # Print a snippet of the first dataset to show its structure
        # This assumes the data structure is a dictionary as per DATA_STRUCTURE.md
        first_data = loaded_data[0]
        for key, value in first_data.items():
            if isinstance(value, list) and len(value) > 5:
                print(f"  {key}: {value[:5]}... (total {len(value)} items)")
            else:
                print(f"  {key}: {value}")