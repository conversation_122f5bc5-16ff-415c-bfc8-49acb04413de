# Comprehensive Documentation: Mouse Movement and Control in Nodriver

## Overview

**Nodriver** is the official successor to Undetected-Chromedriver, designed for web automation and scraping with enhanced anti-detection capabilities. Unlike traditional automation frameworks that rely on Selenium WebDriver, nodriver communicates directly with browsers through the Chrome DevTools Protocol (CDP), providing superior performance and stealth capabilities.

## Current Mouse Movement Capabilities

### Basic Element Interaction

Nodriver provides several fundamental methods for interacting with web elements through mouse actions:

#### Standard Element Clicking

The most basic form of mouse interaction in nodriver is element-based clicking:

```python
import nodriver as uc

async def main():
    browser = await uc.start()
    page = await browser.get('https://example.com')
    
    # Find an element and click it
    element = await page.find('Submit')  # Find by text
    await element.click()  # Basic click method
    
    # Alternative: Enhanced mouse click
    await element.mouse_click()  # More reliable clicking method
```

#### Element Selection Methods

Nodriver offers multiple ways to locate elements for mouse interaction:

```python
# CSS selector-based selection
element = await page.select('button#submit')
await element.click()

# Text-based finding with best match
element = await page.find('Click here', best_match=True)
await element.mouse_click()

# Multiple element selection
elements = await page.select_all('a[href]')
for element in elements:
    await element.click()
```

### Advanced Mouse Operations via CDP

For more sophisticated mouse control, nodriver leverages the Chrome DevTools Protocol (CDP). While not directly exposed through simple APIs, advanced users can implement coordinate-based clicking and complex mouse movements.

#### CDP Mouse Event Implementation

Based on community contributions and source code analysis, here's how to implement coordinate-based mouse control:

```python
import nodriver as uc
from nodriver import cdp
import asyncio

async def click_coordinates(tab, x, y):
    """Click at specific coordinates using CDP"""
    # Move mouse to coordinates
    await tab.send(cdp.input_.dispatch_mouse_event(
        type_='mouseMoved',
        x=x,
        y=y,
        button='left'
    ))
    
    # Mouse down event
    await tab.send(cdp.input_.dispatch_mouse_event(
        type_='mousePressed',
        x=x,
        y=y,
        button='left',
        click_count=1
    ))
    
    # Mouse up event
    await tab.send(cdp.input_.dispatch_mouse_event(
        type_='mouseReleased',
        x=x,
        y=y,
        button='left',
        click_count=1
    ))

async def main():
    browser = await uc.start()
    page = await browser.get('https://example.com')
    
    # Click at coordinates (100, 200)
    await click_coordinates(page, 100, 200)
```

#### Enhanced Mouse Class Implementation

A community-contributed enhancement provides a more comprehensive mouse control system:

```python
class Mouse:
    def __init__(self, tab):
        self.tab = tab
        self._x = 0.0
        self._y = 0.0
        self._button = 'none'
    
    async def move(self, x: float, y: float, steps: int = 1):
        """Move mouse cursor with optional intermediate steps"""
        from_x = self._x
        from_y = self._y
        self._x = x
        self._y = y
        
        for i in range(1, steps + 1):
            current_x = round(from_x + (self._x - from_x) * (i / steps))
            current_y = round(from_y + (self._y - from_y) * (i / steps))
            
            await self.tab.send(cdp.input_.dispatch_mouse_event(
                type_='mouseMoved',
                x=current_x,
                y=current_y,
                button=self._button
            ))
    
    async def click(self, x: float, y: float, button: str = 'left', delay: float = 0):
        """Click at specific coordinates"""
        await self.move(x, y)
        await self.down(button=button)
        if delay:
            await asyncio.sleep(delay / 1000)
        await self.up(button=button)
    
    async def down(self, button: str = 'left'):
        """Press mouse button down"""
        self._button = button
        await self.tab.send(cdp.input_.dispatch_mouse_event(
            type_='mousePressed',
            button=button,
            x=self._x,
            y=self._y,
            click_count=1
        ))
    
    async def up(self, button: str = 'left'):
        """Release mouse button"""
        await self.tab.send(cdp.input_.dispatch_mouse_event(
            type_='mouseReleased',
            button=button,
            x=self._x,
            y=self._y,
            click_count=1
        ))
        self._button = 'none'
```

### Coordinate System and Positioning

#### Understanding the Coordinate System

Nodriver uses the standard web coordinate system:
- **Origin (0,0)**: Top-left corner of the viewport
- **X-axis**: Increases from left to right
- **Y-axis**: Increases from top to bottom
- **Units**: Pixels from the viewport origin

#### Getting Element Coordinates

While nodriver doesn't expose element positioning methods directly, you can obtain coordinates using JavaScript execution:

```python
async def get_element_position(page, element):
    """Get element position using JavaScript"""
    script = """
    const rect = arguments.getBoundingClientRect();
    return {
        x: rect.x,
        y: rect.y,
        width: rect.width,
        height: rect.height,
        centerX: rect.x + rect.width / 2,
        centerY: rect.y + rect.height / 2
    };
    """
    return await page.evaluate(script, element)
```

### Limitations and Workarounds

#### Current Limitations

1. **No Built-in Coordinate Clicking**: Unlike Selenium's ActionChains, nodriver lacks native coordinate-based clicking methods
2. **CDP Knowledge Required**: Advanced mouse operations require understanding of Chrome DevTools Protocol
3. **Limited Documentation**: Official documentation doesn't cover advanced mouse control extensively
4. **No ActionChains Equivalent**: No direct equivalent to Selenium's ActionChains class for complex mouse sequences

#### Workarounds and Solutions

**For Simple Coordinate Clicking**:
```python
async def simple_click_coordinates(page, x, y):
    """Simple coordinate clicking using JavaScript"""
    script = f"document.elementFromPoint({x}, {y}).click();"
    await page.evaluate(script)
```

**For Complex Mouse Sequences**:
```python
async def drag_and_drop(page, from_x, from_y, to_x, to_y):
    """Implement drag and drop using CDP events"""
    # Move to start position
    await page.send(cdp.input_.dispatch_mouse_event(
        type_='mouseMoved', x=from_x, y=from_y
    ))
    
    # Mouse down
    await page.send(cdp.input_.dispatch_mouse_event(
        type_='mousePressed', x=from_x, y=from_y, button='left'
    ))
    
    # Move to end position (with intermediate steps for smoothness)
    steps = 10
    for i in range(1, steps + 1):
        x = from_x + (to_x - from_x) * (i / steps)
        y = from_y + (to_y - from_y) * (i / steps)
        await page.send(cdp.input_.dispatch_mouse_event(
            type_='mouseMoved', x=x, y=y, button='left'
        ))
        await asyncio.sleep(0.01)  # Small delay for smooth movement
    
    # Mouse up
    await page.send(cdp.input_.dispatch_mouse_event(
        type_='mouseReleased', x=to_x, y=to_y, button='left'
    ))
```

### Practical Examples

#### Example 1: Clicking Specific Coordinates

```python
import nodriver as uc
from nodriver import cdp

async def click_at_coordinates():
    browser = await uc.start()
    page = await browser.get('https://example.com')
    
    # Wait for page to load
    await page.sleep(2)
    
    # Click at coordinates (300, 400)
    await page.send(cdp.input_.dispatch_mouse_event(
        type_='mouseMoved',
        x=300,
        y=400
    ))
    
    await page.send(cdp.input_.dispatch_mouse_event(
        type_='mousePressed',
        x=300,
        y=400,
        button='left',
        click_count=1
    ))
    
    await page.send(cdp.input_.dispatch_mouse_event(
        type_='mouseReleased',
        x=300,
        y=400,
        button='left',
        click_count=1
    ))
    
    await page.sleep(2)
    browser.stop()

if __name__ == '__main__':
    uc.loop().run_until_complete(click_at_coordinates())
```

#### Example 2: Smooth Mouse Movement

```python
import nodriver as uc
from nodriver import cdp
import asyncio

async def smooth_mouse_movement():
    browser = await uc.start()
    page = await browser.get('https://example.com')
    
    # Define start and end positions
    start_x, start_y = 100, 100
    end_x, end_y = 500, 300
    
    # Number of intermediate steps for smooth movement
    steps = 20
    
    for i in range(steps + 1):
        # Calculate intermediate position
        x = start_x + (end_x - start_x) * (i / steps)
        y = start_y + (end_y - start_y) * (i / steps)
        
        # Move mouse to intermediate position
        await page.send(cdp.input_.dispatch_mouse_event(
            type_='mouseMoved',
            x=x,
            y=y
        ))
        
        # Small delay for smooth movement
        await asyncio.sleep(0.05)
    
    browser.stop()

if __name__ == '__main__':
    uc.loop().run_until_complete(smooth_mouse_movement())
```

#### Example 3: Right-Click Context Menu

```python
async def right_click_coordinates(page, x, y):
    """Perform right-click at specific coordinates"""
    # Move to position
    await page.send(cdp.input_.dispatch_mouse_event(
        type_='mouseMoved',
        x=x,
        y=y
    ))
    
    # Right-click (context click)
    await page.send(cdp.input_.dispatch_mouse_event(
        type_='mousePressed',
        x=x,
        y=y,
        button='right',
        click_count=1
    ))
    
    await page.send(cdp.input_.dispatch_mouse_event(
        type_='mouseReleased',
        x=x,
        y=y,
        button='right',
        click_count=1
    ))
```

### Best Practices and Recommendations

#### Performance Considerations

1. **Minimize CDP Calls**: Combine multiple mouse events when possible to reduce overhead
2. **Use Appropriate Delays**: Add small delays between mouse events for more natural movement patterns
3. **Element-First Approach**: Prefer element-based clicking over coordinates when possible for better reliability

#### Detection Avoidance

1. **Human-like Movement**: Implement curved or stepped mouse movements rather than direct linear paths
2. **Variable Timing**: Use random delays between mouse actions to avoid detection patterns
3. **Natural Sequences**: Follow natural user interaction patterns (hover before click, etc.)

#### Error Handling

```python
async def safe_coordinate_click(page, x, y, retries=3):
    """Safe coordinate clicking with error handling"""
    for attempt in range(retries):
        try:
            await page.send(cdp.input_.dispatch_mouse_event(
                type_='mouseMoved', x=x, y=y
            ))
            await page.send(cdp.input_.dispatch_mouse_event(
                type_='mousePressed', x=x, y=y, button='left', click_count=1
            ))
            await page.send(cdp.input_.dispatch_mouse_event(
                type_='mouseReleased', x=x, y=y, button='left', click_count=1
            ))
            return True
        except Exception as e:
            if attempt == retries - 1:
                raise e
            await asyncio.sleep(0.5)
    return False
```

## Future Developments

The nodriver community is actively working on enhancements, including:

- **Native Mouse APIs**: Planned implementation of built-in coordinate-based clicking methods
- **Enhanced Element Interaction**: Improved element positioning and interaction capabilities  
- **Human-like Movement Patterns**: Built-in support for natural mouse movement simulation
- **ActionChains Equivalent**: Development of a comprehensive mouse action chaining system

## Conclusion

While nodriver currently lacks built-in coordinate-based mouse movement capabilities similar to Selenium's ActionChains, it provides robust element-based clicking functionality and supports advanced mouse control through the Chrome DevTools Protocol. For users requiring precise coordinate-based mouse control, custom implementations using CDP events offer a powerful solution, though they require more technical expertise.

The framework's focus on stealth and performance makes it an excellent choice for web automation scenarios where anti-detection capabilities are crucial, and the growing community contributions continue to expand its mouse control capabilities.