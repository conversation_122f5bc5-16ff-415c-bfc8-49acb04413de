import asyncio
import random
import re
from automation_steps.base_step import BaseStep
from src.logger import logger


class CompleteMoreActivitiesStep(BaseStep):
    async def execute(self):
        """
        Executes the daily task to complete 'more activities' on the Microsoft Rewards page.
        """
        logger.info(f"Starting 'Complete More Activities' task for {self.email}...", extra={'email': self.email})
        try:
            # 1. Get Initial Account Info using UserDataExtractor
            initial_user_data = await self.bot.user_data_extractor.get_data(["available_points", "level", "today_points"])
            level = initial_user_data.get("level")
            initial_available_points = initial_user_data.get("available_points")
            initial_today_points = initial_user_data.get("today_points")

            logger.info(f"Initial Account Info: Level: {level}, Available Points: {initial_available_points}, Today's Points: {initial_today_points}", extra={'email': self.email})

            # 2. Scroll to Activities Section
            logger.info("Scrolling to 'More Activities' section...", extra={'email': self.email})
            # Scroll to the top of the 'More Activities' section first
            await self.tab.evaluate("document.getElementById('more-activities').scrollIntoView({behavior: 'smooth', block: 'start'});")
            await asyncio.sleep(random.uniform(1, 2)) # Human-like delay after scrolling

            # 3. Identify and Process Activities
            activities_completed_count = 0
            
            MAX_ATTEMPTS = 3
            attempt_count = 0
            
            # List to hold unique identifiers (data-bi-id) of cards that need to be processed in the current attempt
            # Initially, this will be empty, and we'll populate it with all donable cards in the first attempt.
            # In subsequent attempts, it will hold only the IDs of cards that were not completed.
            cards_to_process_ids = []
            
            # List to hold unique identifiers (data-bi-id) of cards that remain donable after the current attempt
            remaining_donable_card_ids = []

            while attempt_count < MAX_ATTEMPTS:
                logger.info(f"Starting attempt {attempt_count + 1} to complete more activities.", extra={'email': self.email})
                
                current_attempt_elements = []
                if attempt_count == 0:
                    # First attempt: get all activity cards
                    all_activity_cards = await self.bot.stealth_browser.browser_actions.get_elements_by_selector('div#more-activities mee-card')
                    if not all_activity_cards:
                        logger.info("No activity cards found on the page. Ending activity processing.", extra={'email': self.email})
                        break
                    current_attempt_elements = all_activity_cards
                    logger.info(f"Found {len(current_attempt_elements)} activity cards for the first attempt.", extra={'email': self.email})
                else:
                    # Subsequent attempts: re-fetch only the remaining donable cards
                    if not cards_to_process_ids:
                        logger.info("No remaining donable activities from previous attempt. Ending activity processing.", extra={'email': self.email})
                        break # No cards left to process from previous attempt
                    
                    logger.info(f"Attempting to re-process {len(cards_to_process_ids)} remaining donable activities.", extra={'email': self.email})
                    for card_id in cards_to_process_ids:
                        try:
                            # Re-fetch the specific card element to ensure it's not stale
                            updated_card_element = await self.bot.stealth_browser.browser_actions.wait_for_element_by_xpath(f'//div[@data-bi-id="{card_id}"]', timeout=5)
                            if updated_card_element:
                                current_attempt_elements.append(updated_card_element)
                            else:
                                logger.warning(f"Could not re-find activity card with data-bi-id '{card_id}'. It might have disappeared or changed.", extra={'email': self.email})
                        except Exception as e:
                            logger.warning(f"Error re-fetching card with data-bi-id '{card_id}': {e}", extra={'email': self.email})
                            # If we can't re-fetch, it's probably gone or completed by other means, so don't add to current_attempt_elements
                
                if not current_attempt_elements:
                    logger.info("No activity cards to process in this attempt. Ending activity processing.", extra={'email': self.email})
                    break

                # Clear remaining_donable_card_ids for the current iteration
                remaining_donable_card_ids = []

                for i, card_element in enumerate(current_attempt_elements):
                    try: # This is the main try block for processing each card
                        # Get activity title and data-bi-id early
                        rewardable_container = await card_element.query_selector('div.rewards-card-container')
                        original_card_bi_id = rewardable_container.attrs.get('data-bi-id') if rewardable_container else None
                        
                        activity_title_element = await card_element.query_selector('h3.c-heading.ng-binding')
                        activity_title = activity_title_element.text_all if activity_title_element else f"Activity {original_card_bi_id or i+1}"
                        logger.info(f"Processing activity: '{activity_title}' (ID: {original_card_bi_id})", extra={'email': self.email})

                        # Scroll to the specific activity card before processing it
                        await self.bot.stealth_browser.browser_actions.scroll_to_element(card_element, block='center')
                        
                        # Check if the card is locked by looking for the 'exclusiveOverlay' div
                        exclusive_overlay = await card_element.query_selector('#exclusiveOverlay')
                        if exclusive_overlay:
                            logger.debug(f"Activity '{activity_title}' is locked (exclusiveOverlay found). Skipping.", extra={'email': self.email})
                            continue

                        # Check if it's completed or not a donable activity based on icons
                        skype_check_icon = await card_element.query_selector('span.mee-icon.mee-icon-SkypeCircleCheck')
                        add_medium_icon = await card_element.query_selector('span.mee-icon.mee-icon-AddMedium')

                        if skype_check_icon:
                            logger.debug(f"Activity '{activity_title}' is already completed (SkypeCircleCheck icon found). Skipping.", extra={'email': self.email})
                            continue
                        elif not add_medium_icon: # If not completed, and no AddMedium icon, it's not a donable activity
                            logger.debug(f"Activity '{activity_title}' is not a donable activity (no AddMedium icon). Skipping.", extra={'email': self.email})
                            continue
                        else:
                            logger.debug(f"Activity '{activity_title}' is a donable activity (AddMedium icon found). Proceeding.", extra={'email': self.email})
                        
                        # Extract points
                        points_element = await card_element.query_selector('span.c-heading.pointsString')
                        activity_points = 0
                        if points_element:
                            points_text = await self.bot.stealth_browser.browser_actions.get_text_content_from_element(points_element)
                            activity_points = int(re.search(r'\d+', points_text).group()) if points_text and re.search(r'\d+', points_text) else 0
                        
                        if activity_points == 0:
                            logger.debug(f"Activity '{activity_title}' has no points. Skipping.", extra={'email': self.email})
                            continue

                        # Get the link to click
                        activity_link_element_raw = await card_element.query_selector('a.ds-card-sec')
                        activity_link_element = None
                        # Ensure activity_link_element is a single Element object, not a list
                        if isinstance(activity_link_element_raw, list):
                            if activity_link_element_raw:
                                activity_link_element = activity_link_element_raw[0]
                        else:
                            activity_link_element = activity_link_element_raw

                        if not activity_link_element:
                            logger.warning(f"Activity '{activity_title}' is donable but no clickable link found. Skipping.", extra={'email': self.email})
                            continue
                        
                        logger.info(f"Found donable activity: '{activity_title}' with {activity_points} points.", extra={'email': self.email})

                        # Store current tabs to detect new tab by their unique identity
                        existing_tabs_before_click_ids = set(id(tab) for tab in self.bot.browser.tabs)
                        logger.info(f"Existing tabs before click: {[id(tab) for tab in self.bot.browser.tabs]}", extra={'email': self.email})
                        
                        # Click the activity link
                        activity_href = activity_link_element.attrs.get('href')
                        if not activity_href:
                            logger.warning(f"Activity '{activity_title}' link element has no href attribute. Skipping.", extra={'email': self.email})
                            continue

                        link_selector = f'a[href="{activity_href}"]'
                        
                        logger.info(f"Clicking activity: '{activity_title}' with selector '{link_selector}'...", extra={'email': self.email})
                        await self.bot.stealth_browser.browser_actions.click(link_selector)
                        await asyncio.sleep(random.uniform(1, 2)) # Short delay after click

                        # Handle New Tab
                        all_tabs_after_click = self.bot.browser.tabs
                        new_tab = None
                        for tab in all_tabs_after_click:
                            if id(tab) not in existing_tabs_before_click_ids:
                                new_tab = tab
                                break
                        
                        if new_tab:
                            logger.info(f"New tab opened for activity '{activity_title}'. Switching to new tab...", extra={'email': self.email})
                            await new_tab.activate()
                            await asyncio.sleep(random.uniform(1, 3)) # Wait on new tab
                            await new_tab.close()
                            logger.info("New tab closed. Switching back to main tab.", extra={'email': self.email})
                            await self.tab.activate() # Switch back to original tab
                            await asyncio.sleep(random.uniform(1, 2)) # Delay after switching back
                        else:
                            logger.warning(f"No new tab detected for activity '{activity_title}'. Assuming same-tab navigation or issue.", extra={'email': self.email})
                            await asyncio.sleep(random.uniform(2, 4)) # Longer wait if no new tab

                        # Wait for Completion Icon (on the original tab)
                        logger.info(f"Waiting for completion icon for activity '{activity_title}'...", extra={'email': self.email})
                        
                        # Re-fetch the specific card element to ensure it's not stale
                        # Use the original_card_bi_id obtained earlier
                        if original_card_bi_id:
                            logger.info(f"Re-fetching activity card with data-bi-id '{original_card_bi_id}' to check completion...", extra={'email': self.email})
                            updated_card_element_for_check = await self.bot.stealth_browser.browser_actions.wait_for_element_by_xpath(f'//div[@data-bi-id="{original_card_bi_id}"]', timeout=10)
                            if updated_card_element_for_check:
                                completion_icon_selector = 'span.mee-icon.mee-icon-SkypeCircleCheck'
                                completion_icon_found = await self.bot.stealth_browser.browser_actions.wait_for_child_element(updated_card_element_for_check, completion_icon_selector, timeout=15)
                                
                                if completion_icon_found:
                                    logger.info(f"Completion icon found for '{activity_title}'.", extra={'email': self.email})
                                    activities_completed_count += 1
                                else:
                                    logger.warning(f"Completion icon NOT found for '{activity_title}' within timeout. Adding to remaining for next attempt.", extra={'email': self.email})
                                    if original_card_bi_id: # Only add if we have a valid ID
                                        remaining_donable_card_ids.append(original_card_bi_id)
                                        logger.debug(f"Added '{activity_title}' (ID: {original_card_bi_id}) to remaining for next attempt.", extra={'email': self.email})
                            else:
                                logger.warning(f"Could not re-find activity card with data-bi-id '{original_card_bi_id}' for completion check. Assuming not completed and adding to remaining.", extra={'email': self.email})
                                if original_card_bi_id: # Only add if we have a valid ID
                                    remaining_donable_card_ids.append(original_card_bi_id)
                                    logger.debug(f"Added '{activity_title}' (ID: {original_card_bi_id}) to remaining for next attempt.", extra={'email': self.email})
                        else:
                            logger.warning(f"Could not find data-bi-id for activity '{activity_title}'. Cannot reliably wait for icon change. Assuming not completed and adding to remaining.", extra={'email': self.email})
                            # If no ID, we can't re-process it reliably, but for safety, let's assume it's not completed and log.
                            # We can't add it to remaining_donable_card_ids if we don't have a reliable ID to re-fetch it.
                            pass # Do not add to remaining if no ID
                            
                    except Exception as e:
                        page_html = await self.tab.get_content()
                        await self.bot.log_error(self.email, f"Error processing activity card (ID: {original_card_bi_id or 'N/A'}): {e}", page_html)
                        logger.error(f"Error processing activity card (ID: {original_card_bi_id or 'N/A'}): {e}", extra={'email': self.email})
                        # If an error occurs, and we have an ID, assume it wasn't completed and add to remaining
                        if original_card_bi_id:
                            remaining_donable_card_ids.append(original_card_bi_id)
                            logger.debug(f"Added '{activity_title}' (ID: {original_card_bi_id}) to remaining due to error.", extra={'email': self.email})
                        continue # Continue to next activity even if one fails
                
                # After processing all cards in current_attempt_elements
                # Update cards_to_process_ids for the next iteration
                cards_to_process_ids = list(set(remaining_donable_card_ids)) # Remove duplicates
                
                if not cards_to_process_ids and attempt_count == 0:
                    logger.info("All donable activities completed in the first attempt or no donable activities found. Ending activity processing.", extra={'email': self.email})
                    break
                elif not cards_to_process_ids and attempt_count > 0:
                    logger.info("All remaining donable activities completed in this attempt. Ending activity processing.", extra={'email': self.email})
                    break
                
                attempt_count += 1
                await asyncio.sleep(random.uniform(1, 3)) # Delay before re-fetching activities

            logger.info(f"Finished 'Complete More Activities' task. Total activities completed: {activities_completed_count}", extra={'email': self.email})
            # 4. Get Final Account Info using UserDataExtractor
            final_user_data = await self.bot.user_data_extractor.get_data(["available_points", "level", "today_points"])
            final_available_points = final_user_data.get("available_points")
            final_today_points = final_user_data.get("today_points")
            final_level = final_user_data.get("level")

            logger.info(f"Final Account Info: Level: {final_level}, Available Points: {final_available_points}, Today's Points: {final_today_points}", extra={'email': self.email})
            return True

        except Exception as e:
            page_html = await self.tab.get_content() if self.tab else None
            await self.bot.log_error(self.email, f"An unexpected error occurred during 'Complete More Activities' task: {e}", page_html)
            logger.critical(f"An unexpected error occurred during 'Complete More Activities' task: {e}", extra={'email': self.email})
            return False