2025-07-04 15:14:34,008 - RewardsBotLogger - INFO - [<EMAIL>] - Human behavior model loaded successfully.
2025-07-04 15:14:35,634 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#scrollSection' for up to 5 seconds...
2025-07-04 15:14:35,668 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#scrollSection' found and is visible.
2025-07-04 15:14:35,671 - RewardsBotLogger - ERROR - [<EMAIL>] - Error during human-like scrolling: list indices must be integers or slices, not str
2025-07-04 15:42:33,678 - Re<PERSON><PERSON><PERSON>otLogger - INFO - [<EMAIL>] - Human behavior model loaded successfully.
2025-07-04 15:42:37,965 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#scrollSection' for up to 5 seconds...
2025-07-04 15:42:37,994 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#scrollSection' found and is visible.
2025-07-04 15:42:37,999 - RewardsBotLogger - ERROR - [<EMAIL>] - Exception in human_like_scroll_to_random_target: list indices must be integers or slices, not str
2025-07-04 15:42:38,001 - RewardsBotLogger - ERROR - [<EMAIL>] - Stack trace: Traceback (most recent call last):
  File "D:\My_Applications\Microsoft_rewards_automation\src\browser_actions.py", line 595, in human_like_scroll_to_random_target
    target_x = pos['x'] + pos['width'] / 2
               ~~~^^^^^
TypeError: list indices must be integers or slices, not str

2025-07-04 15:49:15,147 - RewardsBotLogger - INFO - [<EMAIL>] - Human behavior model loaded successfully.
2025-07-04 15:49:18,388 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#scrollSection' for up to 5 seconds...
2025-07-04 15:49:18,433 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#scrollSection' found and is visible.
2025-07-04 15:49:18,435 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (450, 300) to (376.00000762939453, 621.5000152587891) using trained model...
2025-07-04 15:49:20,796 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (376, 621)
2025-07-04 15:49:20,799 - RewardsBotLogger - DEBUG - [<EMAIL>] - Raw scroll_info value: [['currentPosition', {'type': 'number', 'value': 0}], ['targetPosition', {'type': 'number', 'value': 1033}], ['maxScroll', {'type': 'number', 'value': 1198}]] (type: <class 'list'>)
2025-07-04 15:49:20,799 - RewardsBotLogger - ERROR - [<EMAIL>] - Unexpected scroll_info value: [['currentPosition', {'type': 'number', 'value': 0}], ['targetPosition', {'type': 'number', 'value': 1033}], ['maxScroll', {'type': 'number', 'value': 1198}]] (type: <class 'list'>)
2025-07-04 15:49:20,800 - RewardsBotLogger - ERROR - [<EMAIL>] - Stack trace: NoneType: None

2025-07-04 15:57:56,469 - RewardsBotLogger - INFO - [<EMAIL>] - Human behavior model loaded successfully.
2025-07-04 15:57:58,740 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#scrollSection' for up to 5 seconds...
2025-07-04 15:57:58,748 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#scrollSection' found and is visible.
2025-07-04 15:57:58,761 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (450, 300) to (376.00000762939453, 621.5000152587891) using trained model...
2025-07-04 15:58:00,674 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (376, 621)
2025-07-04 15:58:00,676 - RewardsBotLogger - DEBUG - [<EMAIL>] - Raw scroll_info value: {'currentPosition': 0, 'targetPosition': 648, 'maxScroll': 1198} (type: <class 'dict'>)
2025-07-04 15:58:00,677 - RewardsBotLogger - INFO - [<EMAIL>] - Scrolling from 0 to 648 (max: 1198)
2025-07-04 15:58:00,859 - RewardsBotLogger - DEBUG - [<EMAIL>] - Generated scroll sequence with 21 scroll events
2025-07-04 15:58:00,861 - RewardsBotLogger - ERROR - [<EMAIL>] - Exception in human_like_scroll_to_random_target: module 'nodriver.cdp.input_' has no attribute 'dispatch_mouse_wheel_event'
2025-07-04 15:58:00,863 - RewardsBotLogger - ERROR - [<EMAIL>] - Stack trace: Traceback (most recent call last):
  File "D:\My_Applications\Microsoft_rewards_automation\src\browser_actions.py", line 700, in human_like_scroll_to_random_target
    await self.tab.send(cdp.input_.dispatch_mouse_wheel_event(
                        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: module 'nodriver.cdp.input_' has no attribute 'dispatch_mouse_wheel_event'. Did you mean: 'dispatch_mouse_event'?

2025-07-04 16:14:52,726 - RewardsBotLogger - INFO - [<EMAIL>] - Human behavior model loaded successfully.
2025-07-04 16:15:11,487 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#scrollSection' for up to 5 seconds...
2025-07-04 16:15:11,530 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#scrollSection' found and is visible.
2025-07-04 16:15:11,537 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (450, 300) to (376.00000762939453, 621.5000152587891) using trained model...
2025-07-04 16:15:13,986 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (376, 621)
2025-07-04 16:15:13,990 - RewardsBotLogger - DEBUG - [<EMAIL>] - Raw scroll_info value: {'currentPosition': 0, 'targetPosition': 894, 'maxScroll': 1198} (type: <class 'dict'>)
2025-07-04 16:15:13,992 - RewardsBotLogger - INFO - [<EMAIL>] - Scrolling from 0 to 894 (max: 1198)
2025-07-04 16:15:14,204 - RewardsBotLogger - DEBUG - [<EMAIL>] - Generated scroll sequence with 22 scroll events
2025-07-04 16:15:14,237 - RewardsBotLogger - ERROR - [<EMAIL>] - Exception in human_like_scroll_to_random_target: 'list' object has no attribute 'get'
2025-07-04 16:15:14,241 - RewardsBotLogger - ERROR - [<EMAIL>] - Stack trace: Traceback (most recent call last):
  File "D:\My_Applications\Microsoft_rewards_automation\src\browser_actions.py", line 714, in human_like_scroll_to_random_target
    if not result.get('success'):
           ^^^^^^^^^^
AttributeError: 'list' object has no attribute 'get'

2025-07-04 16:41:54,960 - RewardsBotLogger - INFO - [<EMAIL>] - Human behavior model loaded successfully.
2025-07-04 16:42:01,415 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#scrollSection' for up to 5 seconds...
2025-07-04 16:42:01,486 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#scrollSection' found and is visible.
2025-07-04 16:42:01,617 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (450, 300) to (376.00000762939453, 621.5000152587891) using trained model...
2025-07-04 16:42:03,730 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (376, 621)
2025-07-04 16:42:03,735 - RewardsBotLogger - DEBUG - [<EMAIL>] - Raw scroll_info value: {'currentPosition': 0, 'targetPosition': 604, 'maxScroll': 1198} (type: <class 'dict'>)
2025-07-04 16:42:03,739 - RewardsBotLogger - INFO - [<EMAIL>] - Scrolling from 0 to 604 (max: 1198)
2025-07-04 16:42:03,961 - RewardsBotLogger - DEBUG - [<EMAIL>] - Generated scroll sequence with 21 scroll events
2025-07-04 16:42:03,968 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 71.19999694824219
2025-07-04 16:42:04,097 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 130.39999389648438
2025-07-04 16:42:04,219 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 229.60000610351562
2025-07-04 16:42:04,345 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 330.3999938964844
2025-07-04 16:42:04,601 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 371.20001220703125
2025-07-04 16:42:04,836 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 407.20001220703125
2025-07-04 16:42:05,061 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 444
2025-07-04 16:42:05,199 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 472
2025-07-04 16:42:05,344 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 491.20001220703125
2025-07-04 16:42:06,797 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 511.20001220703125
2025-07-04 16:42:06,975 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 526.4000244140625
2025-07-04 16:42:07,104 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 536.7999877929688
2025-07-04 16:42:07,286 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 551.2000122070312
2025-07-04 16:42:07,450 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 558.4000244140625
2025-07-04 16:42:07,516 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 565.5999755859375
2025-07-04 16:42:07,634 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 570.4000244140625
2025-07-04 16:42:07,827 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 574.4000244140625
2025-07-04 16:42:08,085 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 579.2000122070312
2025-07-04 16:42:08,210 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 581.5999755859375
2025-07-04 16:42:08,331 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 584.7999877929688
2025-07-04 16:42:08,454 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 585.5999755859375
2025-07-04 16:42:08,457 - RewardsBotLogger - INFO - [<EMAIL>] - Scroll completed. Final position: 586
2025-07-04 16:42:08,459 - RewardsBotLogger - INFO - [<EMAIL>] - Successfully scrolled near target (distance: 18px)
2025-07-04 16:53:44,908 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#scrollSection' for up to 5 seconds...
2025-07-04 16:53:44,920 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#scrollSection' found and is visible.
2025-07-04 16:53:44,939 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (376, 621) to (376.00000762939453, 621.5000152587891) using trained model...
2025-07-04 16:53:45,306 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (376, 621)
2025-07-04 16:53:45,338 - RewardsBotLogger - DEBUG - [<EMAIL>] - Raw scroll_info value: {'currentPosition': 0, 'targetPosition': 455, 'maxScroll': 1198} (type: <class 'dict'>)
2025-07-04 16:53:45,355 - RewardsBotLogger - INFO - [<EMAIL>] - Scrolling from 0 to 455 (max: 1198)
2025-07-04 16:53:45,615 - RewardsBotLogger - DEBUG - [<EMAIL>] - Generated scroll sequence with 16 scroll events
2025-07-04 16:53:45,674 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 66.4000015258789
2025-07-04 16:53:45,799 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 145.60000610351562
2025-07-04 16:53:46,813 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 192
2025-07-04 16:53:46,936 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 231.1999969482422
2025-07-04 16:53:47,178 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 288
2025-07-04 16:53:47,302 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 324.79998779296875
2025-07-04 16:53:47,537 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 357.6000061035156
2025-07-04 16:53:49,485 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 376
2025-07-04 16:53:49,716 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 396
2025-07-04 16:53:49,826 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 408
2025-07-04 16:53:50,083 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 418.3999938964844
2025-07-04 16:53:50,207 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 426.3999938964844
2025-07-04 16:53:50,362 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 432.79998779296875
2025-07-04 16:53:50,486 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 436
2025-07-04 16:53:50,594 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 439.20001220703125
2025-07-04 16:53:50,824 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 442.3999938964844
2025-07-04 16:53:50,830 - RewardsBotLogger - INFO - [<EMAIL>] - Scroll completed. Final position: 442
2025-07-04 16:53:50,835 - RewardsBotLogger - INFO - [<EMAIL>] - Successfully scrolled near target (distance: 13px)
2025-07-04 20:47:01,390 - RewardsBotLogger - INFO - [<EMAIL>] - Human behavior model loaded successfully.
2025-07-04 20:47:07,673 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (450, 300) to (809, 167) using trained model...
2025-07-04 20:47:11,114 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (809, 167)
2025-07-04 20:47:12,112 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (809, 167) to (526, 100) using trained model...
2025-07-04 20:47:14,626 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (526, 100)
2025-07-04 20:47:16,008 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (526, 100) to (227, 539) using trained model...
2025-07-04 20:47:22,005 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (227, 539)
2025-07-04 20:47:40,030 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (227, 539) to (184, 393) using trained model...
2025-07-04 20:47:41,729 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (184, 393)
2025-07-04 20:47:43,048 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (184, 393) to (504, 413) using trained model...
2025-07-04 20:47:46,910 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (504, 413)
2025-07-04 20:47:48,345 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (504, 413) to (615, 548) using trained model...
2025-07-04 20:47:50,272 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (615, 548)
2025-07-04 20:47:51,710 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (615, 548) to (465, 486) using trained model...
2025-07-04 20:47:53,215 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (465, 486)
2025-07-04 20:47:54,270 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (465, 486) to (666, 434) using trained model...
2025-07-04 20:47:56,264 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (666, 434)
2025-07-04 20:47:57,559 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (666, 434) to (538, 222) using trained model...
2025-07-04 20:47:59,775 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (538, 222)
2025-07-04 20:48:01,498 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (538, 222) to (811, 170) using trained model...
2025-07-04 20:48:03,923 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (811, 170)
2025-07-04 20:48:04,770 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (811, 170) to (381, 285) using trained model...
2025-07-04 20:48:08,310 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (381, 285)
2025-07-04 20:48:11,949 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#scrollSection' for up to 5 seconds...
2025-07-04 20:48:11,957 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#scrollSection' found and is visible.
2025-07-04 20:48:11,961 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (381, 285) to (376.00000762939453, 621.5000152587891) using trained model...
2025-07-04 20:48:17,158 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (376, 621)
2025-07-04 20:48:17,160 - RewardsBotLogger - DEBUG - [<EMAIL>] - Raw scroll_info value: {'currentPosition': 0, 'targetPosition': 852, 'maxScroll': 1198} (type: <class 'dict'>)
2025-07-04 20:48:17,160 - RewardsBotLogger - INFO - [<EMAIL>] - Scrolling from 0 to 852 (max: 1198)
2025-07-04 20:48:17,344 - RewardsBotLogger - DEBUG - [<EMAIL>] - Generated scroll sequence with 19 scroll events
2025-07-04 20:48:17,405 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 112.80000305175781
2025-07-04 20:48:17,529 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 205.60000610351562
2025-07-04 20:48:17,656 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 297.6000061035156
2025-07-04 20:48:17,903 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 400
2025-07-04 20:48:18,028 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 494.3999938964844
2025-07-04 20:48:18,168 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 580.7999877929688
2025-07-04 20:48:18,291 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 636
2025-07-04 20:48:18,539 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 678.4000244140625
2025-07-04 20:48:18,649 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 722.4000244140625
2025-07-04 20:48:18,897 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 750.4000244140625
2025-07-04 20:48:19,005 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 773.5999755859375
2025-07-04 20:48:19,130 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 784.7999877929688
2025-07-04 20:48:19,381 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 801.5999755859375
2025-07-04 20:48:19,615 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 809.5999755859375
2025-07-04 20:48:19,771 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 816.7999877929688
2025-07-04 20:48:19,924 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 821.5999755859375
2025-07-04 20:48:20,050 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 828
2025-07-04 20:48:20,296 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 831.2000122070312
2025-07-04 20:48:20,451 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 834.4000244140625
2025-07-04 20:48:20,453 - RewardsBotLogger - INFO - [<EMAIL>] - Scroll completed. Final position: 834
2025-07-04 20:48:20,453 - RewardsBotLogger - INFO - [<EMAIL>] - Successfully scrolled near target (distance: 18px)
2025-07-04 20:48:24,554 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#scrollSection' for up to 5 seconds...
2025-07-04 20:48:24,622 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#scrollSection' found and is visible.
2025-07-04 20:48:24,624 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (376, 621) to (376.00000762939453, 428.6999969482422) using trained model...
2025-07-04 20:48:26,043 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (376, 428)
2025-07-04 20:48:26,046 - RewardsBotLogger - DEBUG - [<EMAIL>] - Raw scroll_info value: {'currentPosition': 834.4000244140625, 'targetPosition': 852, 'maxScroll': 1198} (type: <class 'dict'>)
2025-07-04 20:48:26,047 - RewardsBotLogger - INFO - [<EMAIL>] - Scrolling from 834 to 852 (max: 1198)
2025-07-04 20:48:26,231 - RewardsBotLogger - DEBUG - [<EMAIL>] - Generated scroll sequence with 3 scroll events
2025-07-04 20:48:26,292 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 837.5999755859375
2025-07-04 20:48:26,447 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 841.5999755859375
2025-07-04 20:48:26,680 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 844
2025-07-04 20:48:26,682 - RewardsBotLogger - INFO - [<EMAIL>] - Scroll completed. Final position: 844
2025-07-04 20:48:26,683 - RewardsBotLogger - INFO - [<EMAIL>] - Successfully scrolled near target (distance: 8px)
2025-07-04 20:48:31,096 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#scrollSection' for up to 5 seconds...
2025-07-04 20:48:31,120 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#scrollSection' found and is visible.
2025-07-04 20:48:31,123 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (376, 428) to (376.00000762939453, 428.6999969482422) using trained model...
2025-07-04 20:48:31,350 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (376, 428)
2025-07-04 20:48:31,351 - RewardsBotLogger - DEBUG - [<EMAIL>] - Raw scroll_info value: {'currentPosition': 844, 'targetPosition': 859, 'maxScroll': 1198} (type: <class 'dict'>)
2025-07-04 20:48:31,352 - RewardsBotLogger - INFO - [<EMAIL>] - Scrolling from 844 to 859 (max: 1198)
2025-07-04 20:48:31,568 - RewardsBotLogger - DEBUG - [<EMAIL>] - Generated scroll sequence with 4 scroll events
2025-07-04 20:48:31,599 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 847.2000122070312
2025-07-04 20:48:32,686 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 849.5999755859375
2025-07-04 20:48:32,796 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 906.4000244140625
2025-07-04 20:48:32,920 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 862.4000244140625
2025-07-04 20:48:33,413 - RewardsBotLogger - INFO - [<EMAIL>] - Scroll completed. Final position: 862
2025-07-04 20:48:33,414 - RewardsBotLogger - INFO - [<EMAIL>] - Successfully scrolled near target (distance: 3px)
2025-07-04 20:48:43,602 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#scrollSection' for up to 5 seconds...
2025-07-04 20:48:43,637 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#scrollSection' found and is visible.
2025-07-04 20:48:43,642 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (376, 428) to (376.00000762939453, 621.5000152587891) using trained model...
2025-07-04 20:48:45,399 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (376, 621)
2025-07-04 20:48:45,400 - RewardsBotLogger - DEBUG - [<EMAIL>] - Raw scroll_info value: {'currentPosition': 0, 'targetPosition': 859, 'maxScroll': 1198} (type: <class 'dict'>)
2025-07-04 20:48:45,402 - RewardsBotLogger - INFO - [<EMAIL>] - Scrolling from 0 to 859 (max: 1198)
2025-07-04 20:48:45,616 - RewardsBotLogger - DEBUG - [<EMAIL>] - Generated scroll sequence with 19 scroll events
2025-07-04 20:48:45,647 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 104
2025-07-04 20:48:45,772 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 192.8000030517578
2025-07-04 20:48:45,882 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 308.79998779296875
2025-07-04 20:48:46,128 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 387.20001220703125
2025-07-04 20:48:46,252 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 466.3999938964844
2025-07-04 20:48:46,500 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 536.7999877929688
2025-07-04 20:48:46,628 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 628
2025-07-04 20:48:46,751 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 690.4000244140625
2025-07-04 20:48:48,989 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 717.5999755859375
2025-07-04 20:48:49,114 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 754.4000244140625
2025-07-04 20:48:49,240 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 776.7999877929688
2025-07-04 20:48:49,363 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 795.2000122070312
2025-07-04 20:48:49,612 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 807.2000122070312
2025-07-04 20:48:49,768 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 815.2000122070312
2025-07-04 20:48:49,892 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 823.2000122070312
2025-07-04 20:48:50,019 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 831.2000122070312
2025-07-04 20:48:50,266 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 836
2025-07-04 20:48:50,397 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 840
2025-07-04 20:48:50,640 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 842.4000244140625
2025-07-04 20:48:51,029 - RewardsBotLogger - INFO - [<EMAIL>] - Scroll completed. Final position: 842
2025-07-04 20:48:51,029 - RewardsBotLogger - INFO - [<EMAIL>] - Successfully scrolled near target (distance: 17px)
2025-07-04 20:49:01,936 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#scrollSection' for up to 5 seconds...
2025-07-04 20:49:01,941 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#scrollSection' found and is visible.
2025-07-04 20:49:01,944 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (376, 621) to (376.00000762939453, 548.6999969482422) using trained model...
2025-07-04 20:49:02,549 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (376, 548)
2025-07-04 20:49:02,550 - RewardsBotLogger - DEBUG - [<EMAIL>] - Raw scroll_info value: {'currentPosition': 0, 'targetPosition': 307, 'maxScroll': 1198} (type: <class 'dict'>)
2025-07-04 20:49:02,550 - RewardsBotLogger - INFO - [<EMAIL>] - Scrolling from 0 to 307 (max: 1198)
2025-07-04 20:49:02,746 - RewardsBotLogger - DEBUG - [<EMAIL>] - Generated scroll sequence with 13 scroll events
2025-07-04 20:49:02,779 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 88
2025-07-04 20:49:03,011 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 139.1999969482422
2025-07-04 20:49:04,014 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 175.1999969482422
2025-07-04 20:49:04,277 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 212
2025-07-04 20:49:04,508 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 236.8000030517578
2025-07-04 20:49:04,756 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 248.8000030517578
2025-07-04 20:49:04,915 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 261.6000061035156
2025-07-04 20:49:05,161 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 272.79998779296875
2025-07-04 20:49:06,158 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 279.20001220703125
2025-07-04 20:49:06,315 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 283.20001220703125
2025-07-04 20:49:06,439 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 286.3999938964844
2025-07-04 20:49:06,562 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 288.79998779296875
2025-07-04 20:49:06,810 - RewardsBotLogger - DEBUG - [<EMAIL>] - Scrolled to position: 292
2025-07-04 20:49:07,429 - RewardsBotLogger - INFO - [<EMAIL>] - Scroll completed. Final position: 292
2025-07-04 20:49:07,430 - RewardsBotLogger - INFO - [<EMAIL>] - Successfully scrolled near target (distance: 15px)
2025-07-04 20:50:11,244 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 20:50:11,251 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 20:50:11,252 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 20:50:11,258 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 20:50:11,261 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (376, 548) to (350.4785709071753, 218.09243888313765) using trained model...
2025-07-04 20:50:14,251 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (350, 218)
2025-07-04 20:51:31,112 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 20:51:31,117 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 20:51:31,117 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 20:51:31,124 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 20:51:31,125 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (350, 218) to (511.01200518357297, 202.60657657324668) using trained model...
2025-07-04 20:51:37,576 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (511, 202)
2025-07-04 20:52:25,187 - RewardsBotLogger - INFO - [<EMAIL>] - Human behavior model loaded successfully.
2025-07-04 20:52:33,312 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (450, 300) to (335, 368) using trained model...
2025-07-04 20:52:36,225 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (335, 368)
2025-07-04 20:52:39,708 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 20:52:39,716 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 20:52:39,716 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 20:52:39,721 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 20:52:39,723 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (335, 368) to (391.2192598051653, 135.37179717675437) using trained model...
2025-07-04 20:52:44,792 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (391, 135)
2025-07-04 21:00:30,019 - RewardsBotLogger - INFO - [<EMAIL>] - Best trained models loaded successfully.
2025-07-04 21:00:30,020 - RewardsBotLogger - INFO - [<EMAIL>] - Human behavior model loaded successfully.
2025-07-04 21:00:39,887 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 21:00:39,954 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 21:00:39,955 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 21:00:39,960 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 21:01:28,176 - RewardsBotLogger - INFO - [<EMAIL>] - Best trained models loaded successfully.
2025-07-04 21:01:28,177 - RewardsBotLogger - INFO - [<EMAIL>] - Human behavior model loaded successfully.
2025-07-04 21:01:30,846 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 21:01:30,854 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 21:01:30,854 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 21:01:30,862 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 21:01:30,870 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (450, 300) to (618.0832356232669, 206.17120573673523) using trained model...
2025-07-04 21:01:32,806 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (618, 206)
2025-07-04 21:01:39,036 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 21:01:39,041 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 21:01:39,042 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 21:01:39,047 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 21:01:39,052 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (618, 206) to (657.450192973096, 109.84122363517702) using trained model...
2025-07-04 21:01:40,547 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (657, 109)
2025-07-04 21:03:37,089 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (657, 109) to (131, 169) using trained model...
2025-07-04 21:03:41,967 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (131, 169)
2025-07-04 21:06:08,265 - RewardsBotLogger - INFO - [<EMAIL>] - Best trained models loaded successfully.
2025-07-04 21:06:08,266 - RewardsBotLogger - INFO - [<EMAIL>] - Human behavior model loaded successfully.
2025-07-04 21:06:18,157 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 21:06:18,166 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 21:06:18,167 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 21:06:18,173 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 21:06:18,173 - RewardsBotLogger - WARNING - [<EMAIL>] - Failed to get accurate element position: Object of type Element is not JSON serializable. Using fallback.
2025-07-04 21:06:18,176 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (450, 300) to (638.5603413535168, 161.29956969099726) using trained model...
2025-07-04 21:06:20,803 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (638, 161)
2025-07-04 21:06:33,796 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 21:06:33,801 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 21:06:33,801 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 21:06:33,807 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 21:06:33,809 - RewardsBotLogger - WARNING - [<EMAIL>] - Failed to get accurate element position: Object of type Element is not JSON serializable. Using fallback.
2025-07-04 21:06:33,811 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (638, 161) to (566.073778862331, 82.2212143950182) using trained model...
2025-07-04 21:06:34,862 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (566, 82)
2025-07-04 21:06:40,610 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 21:06:40,664 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 21:06:40,665 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 21:06:40,668 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 21:06:40,669 - RewardsBotLogger - WARNING - [<EMAIL>] - Failed to get accurate element position: Object of type Element is not JSON serializable. Using fallback.
2025-07-04 21:06:40,671 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (566, 82) to (570.5923508559949, 215.30109094630583) using trained model...
2025-07-04 21:06:42,631 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (570, 215)
2025-07-04 21:06:46,364 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 21:06:46,369 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 21:06:46,369 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 21:06:46,375 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 21:06:46,376 - RewardsBotLogger - WARNING - [<EMAIL>] - Failed to get accurate element position: Object of type Element is not JSON serializable. Using fallback.
2025-07-04 21:06:46,378 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (570, 215) to (544.7379496841419, 151.04522016174127) using trained model...
2025-07-04 21:06:47,168 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (544, 151)
2025-07-04 21:07:51,711 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 21:07:51,716 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 21:07:51,717 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#testInput' for up to 30 seconds...
2025-07-04 21:07:51,722 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#testInput' found and is visible.
2025-07-04 21:07:51,723 - RewardsBotLogger - WARNING - [<EMAIL>] - Failed to get accurate element position: Object of type Element is not JSON serializable. Using fallback.
2025-07-04 21:07:51,726 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (544, 151) to (376.28650188438803, 211.20249006752223) using trained model...
2025-07-04 21:07:53,994 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (376, 211)
2025-07-04 21:11:29,519 - RewardsBotLogger - INFO - [<EMAIL>] - Best trained models loaded successfully.
2025-07-04 21:11:29,520 - RewardsBotLogger - INFO - [<EMAIL>] - Human behavior model loaded successfully.
2025-07-04 21:22:45,804 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#draggableTarget' for up to 30 seconds...
2025-07-04 21:22:45,877 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:22:46,872 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:22:47,886 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:22:48,911 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:22:49,926 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:22:50,941 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:22:51,962 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:22:53,003 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:22:54,004 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:22:55,012 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:22:56,016 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:22:57,075 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:22:58,168 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:22:59,176 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:00,159 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:01,164 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:02,161 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:03,161 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:04,158 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:05,181 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:06,259 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:07,258 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:08,255 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:09,253 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:10,255 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:11,258 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:12,254 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:13,254 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:14,250 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:15,262 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:16,258 - RewardsBotLogger - DEBUG - [<EMAIL>] - Timeout: Element with selector '#draggableTarget' not found within 30 seconds.
2025-07-04 21:23:16,259 - RewardsBotLogger - ERROR - [<EMAIL>] - Could not find target element with selector '#draggableTarget'
2025-07-04 21:23:36,884 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#draggableTarget' for up to 30 seconds...
2025-07-04 21:23:36,888 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:37,902 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:38,986 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:40,011 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:41,046 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:42,114 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:43,113 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:44,128 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:45,127 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:46,136 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:47,133 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:48,126 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:49,115 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:50,137 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:51,158 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:52,246 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:53,248 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:54,258 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:55,235 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:56,241 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:57,255 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:58,255 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:23:59,258 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:24:00,260 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:24:01,294 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:24:02,289 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:24:03,282 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:24:04,295 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:24:05,308 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:24:06,312 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:24:07,337 - RewardsBotLogger - DEBUG - [<EMAIL>] - Timeout: Element with selector '#draggableTarget' not found within 30 seconds.
2025-07-04 21:24:07,337 - RewardsBotLogger - ERROR - [<EMAIL>] - Could not find target element with selector '#draggableTarget'
2025-07-04 21:47:58,514 - RewardsBotLogger - INFO - [<EMAIL>] - Best trained models loaded successfully.
2025-07-04 21:47:58,515 - RewardsBotLogger - INFO - [<EMAIL>] - Human behavior model loaded successfully.
2025-07-04 21:48:04,308 - RewardsBotLogger - DEBUG - [<EMAIL>] - Moving mouse from (450, 300) to (271, 240) using trained model...
2025-07-04 21:48:05,816 - RewardsBotLogger - DEBUG - [<EMAIL>] - Mouse moved to final position: (271, 240)
2025-07-04 21:48:09,714 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#draggableTarget' for up to 30 seconds...
2025-07-04 21:48:09,720 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:10,716 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:11,729 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:12,725 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:13,723 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:14,710 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:15,728 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:16,722 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:17,802 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:18,787 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:19,783 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:20,783 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:21,794 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:22,808 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:23,823 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:24,898 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:25,976 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:27,059 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:28,075 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:29,098 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:30,096 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:31,178 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:32,178 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:33,179 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:34,176 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:35,177 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:36,170 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:37,172 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:38,164 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:39,167 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:48:40,157 - RewardsBotLogger - DEBUG - [<EMAIL>] - Timeout: Element with selector '#draggableTarget' not found within 30 seconds.
2025-07-04 21:48:40,158 - RewardsBotLogger - ERROR - [<EMAIL>] - Could not find target element with selector '#draggableTarget'
2025-07-04 21:59:14,120 - RewardsBotLogger - INFO - [<EMAIL>] - Best trained models loaded successfully.
2025-07-04 21:59:14,122 - RewardsBotLogger - INFO - [<EMAIL>] - Human behavior model loaded successfully.
2025-07-04 21:59:19,449 - RewardsBotLogger - DEBUG - [<EMAIL>] - Waiting for element with selector '#draggableTarget' for up to 30 seconds...
2025-07-04 21:59:19,458 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:20,460 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:21,458 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:22,542 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:23,539 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:24,607 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:25,621 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:26,701 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:27,710 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:28,708 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:29,712 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:30,734 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:31,870 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:32,872 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:33,876 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:34,915 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:35,929 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:36,983 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:37,995 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:39,022 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:40,083 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:41,114 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:42,157 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:43,244 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:44,238 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:45,250 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:46,261 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:47,313 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:48,321 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:49,339 - RewardsBotLogger - DEBUG - [<EMAIL>] - Element '#draggableTarget' found in DOM but not visible. Retrying...
2025-07-04 21:59:50,345 - RewardsBotLogger - DEBUG - [<EMAIL>] - Timeout: Element with selector '#draggableTarget' not found within 30 seconds.
2025-07-04 21:59:50,346 - RewardsBotLogger - ERROR - [<EMAIL>] - Could not find target element with selector '#draggableTarget'
