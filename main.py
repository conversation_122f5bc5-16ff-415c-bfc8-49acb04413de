import asyncio
import sys
import os
import nodriver as uc # Import nodriver

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config import load_account_details
from src.bot import RewardsBot

async def main():
    """
    Main function to load account details and run the Rewards Bot.
    """
    account_details = load_account_details()

    if not account_details:
        print("Failed to load account details. Exiting.")
        return

    email = account_details.get("email")
    password = account_details.get("password")

    if not email or not password:
        print("Email or password not found in account.json. Exiting.")
        return

    bot = RewardsBot(email, password)
    await bot.run()

if __name__ == '__main__':
    # Use the Nodriver event loop for compatibility
    # This is the recommended way to run nodriver applications.
    uc.loop().run_until_complete(main())