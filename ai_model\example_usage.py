"""
Comprehensive Example Usage of Human Interaction AI Model

This script demonstrates all the capabilities of the Human Interaction AI model,
from training to generation to integration with browser automation.
"""

import os
import sys
import asyncio
import time
import json
from typing import List, <PERSON><PERSON>

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ai_model import (
    HumanInteractionAI, 
    InteractionConfig, 
    HumanInteractionGenerator,
    MousePath,
    TypingSequence,
    ScrollSequence,
    NoDriverIntegration
)

def example_1_basic_usage():
    """Example 1: Basic model usage with default configuration"""
    print("=" * 60)
    print("EXAMPLE 1: Basic Model Usage")
    print("=" * 60)
    
    # Initialize with default configuration
    config = InteractionConfig()
    ai_model = HumanInteractionAI(config)
    
    print(f"✓ Initialized AI model on device: {ai_model.device}")
    print(f"✓ Configuration: {config.typing_speed_wpm} WPM, {config.mouse_smoothness} smoothness")
    
    # Create generator (works even without training for demonstration)
    generator = HumanInteractionGenerator(ai_model)
    
    # Generate sample interactions
    print("\nGenerating sample interactions...")
    
    # Mouse movement
    mouse_path = generator.generate_mouse_path((100, 100), (500, 300))
    print(f"✓ Mouse path: {len(mouse_path.points)} points, {mouse_path.total_duration:.2f}s duration")
    
    # Typing sequence
    typing_seq = generator.generate_typing_sequence("Hello, World!")
    print(f"✓ Typing sequence: {len(typing_seq.keys)} keystrokes, {len(typing_seq.errors)} errors")
    
    # Scroll sequence
    scroll_seq = generator.generate_scroll_sequence(1000, 0)
    print(f"✓ Scroll sequence: {len(scroll_seq.scroll_deltas)} events")
    
    print("✓ Basic usage example completed!\n")

def example_2_custom_configuration():
    """Example 2: Custom configuration for specific use cases"""
    print("=" * 60)
    print("EXAMPLE 2: Custom Configuration")
    print("=" * 60)
    
    # Create custom configuration for a slow, careful user
    careful_config = InteractionConfig(
        mouse_smoothness=0.9,           # Very smooth movements
        mouse_speed_variance=0.1,       # Low speed variation
        mouse_error_rate=0.01,          # Very few errors
        typing_speed_wpm=45.0,          # Slower typing
        typing_error_rate=0.005,        # Very few typos
        typing_correction_delay=1.0,    # Longer correction delays
        scroll_smoothness=0.9,          # Smooth scrolling
        scroll_overshoot_rate=0.05,     # Minimal overshoot
        reaction_time_base=0.3,         # Slower reactions
        add_micro_movements=True,       # Natural micro-movements
        simulate_fatigue=True           # Fatigue simulation
    )
    
    print("Custom configuration for careful user:")
    print(f"  - Typing speed: {careful_config.typing_speed_wpm} WPM")
    print(f"  - Mouse smoothness: {careful_config.mouse_smoothness}")
    print(f"  - Error rates: {careful_config.typing_error_rate:.1%} typing, {careful_config.mouse_error_rate:.1%} mouse")
    
    # Initialize with custom config
    ai_model = HumanInteractionAI(careful_config)
    generator = HumanInteractionGenerator(ai_model)
    
    # Generate interactions with custom behavior
    print("\nGenerating interactions with careful user profile...")
    
    mouse_path = generator.generate_mouse_path((0, 0), (800, 600))
    typing_seq = generator.generate_typing_sequence("This is a careful user typing slowly and accurately.")
    
    print(f"✓ Careful mouse movement: {mouse_path.total_duration:.2f}s for long distance")
    print(f"✓ Careful typing: {len(typing_seq.errors)} errors in {len(typing_seq.keys)} keystrokes")
    
    # Create configuration for a fast, aggressive user
    aggressive_config = InteractionConfig(
        mouse_smoothness=0.6,           # Less smooth, more direct
        mouse_speed_variance=0.5,       # High speed variation
        mouse_error_rate=0.1,           # More overshooting
        typing_speed_wpm=85.0,          # Fast typing
        typing_error_rate=0.04,         # More typos
        typing_correction_delay=0.2,    # Quick corrections
        scroll_smoothness=0.5,          # Less smooth scrolling
        scroll_overshoot_rate=0.2,      # More overshoot
        reaction_time_base=0.1,         # Quick reactions
        randomize_timing=True,          # More timing variation
        simulate_fatigue=False          # No fatigue simulation
    )
    
    print("\nCustom configuration for aggressive user:")
    print(f"  - Typing speed: {aggressive_config.typing_speed_wpm} WPM")
    print(f"  - Mouse smoothness: {aggressive_config.mouse_smoothness}")
    print(f"  - Error rates: {aggressive_config.typing_error_rate:.1%} typing, {aggressive_config.mouse_error_rate:.1%} mouse")
    
    ai_model_aggressive = HumanInteractionAI(aggressive_config)
    generator_aggressive = HumanInteractionGenerator(ai_model_aggressive)
    
    mouse_path_aggressive = generator_aggressive.generate_mouse_path((0, 0), (800, 600))
    typing_seq_aggressive = generator_aggressive.generate_typing_sequence("Fast aggressive typing with more errors!")
    
    print(f"✓ Aggressive mouse movement: {mouse_path_aggressive.total_duration:.2f}s for same distance")
    print(f"✓ Aggressive typing: {len(typing_seq_aggressive.errors)} errors in {len(typing_seq_aggressive.keys)} keystrokes")
    
    print("✓ Custom configuration example completed!\n")

def example_3_training_workflow():
    """Example 3: Complete training workflow"""
    print("=" * 60)
    print("EXAMPLE 3: Training Workflow")
    print("=" * 60)
    
    # Check if training data exists
    data_dir = 'data/'
    if not os.path.exists(data_dir):
        print(f"⚠️  Training data directory '{data_dir}' not found.")
        print("   This example shows the training workflow, but requires actual data.")
        print("   Please run the interaction recorder to collect training data first.")
        return
    
    # Count available data files
    json_files = [f for f in os.listdir(data_dir) if f.endswith('.json')]
    if not json_files:
        print(f"⚠️  No JSON training files found in '{data_dir}'.")
        print("   Please collect some interaction data first.")
        return
    
    print(f"✓ Found {len(json_files)} training files in {data_dir}")
    
    # Initialize model for training
    config = InteractionConfig()
    ai_model = HumanInteractionAI(config)
    
    # Load training data
    print("Loading training data...")
    start_time = time.time()
    ai_model.load_training_data(data_dir)
    load_time = time.time() - start_time
    
    print(f"✓ Data loaded in {load_time:.2f}s:")
    print(f"  - Mouse sequences: {len(ai_model.mouse_data)}")
    print(f"  - Typing sequences: {len(ai_model.typing_data)}")
    print(f"  - Scrolling sequences: {len(ai_model.scrolling_data)}")
    
    if not any([ai_model.mouse_data, ai_model.typing_data, ai_model.scrolling_data]):
        print("⚠️  No valid training sequences found. Check data format.")
        return
    
    # Train models (reduced epochs for example)
    print("\nTraining models (reduced epochs for demo)...")
    start_time = time.time()
    ai_model.train_models(epochs=10, batch_size=16, learning_rate=0.001)
    train_time = time.time() - start_time
    
    print(f"✓ Training completed in {train_time:.2f}s")
    
    # Save models
    save_dir = 'ai_model/example_models/'
    ai_model.save_models(save_dir)
    print(f"✓ Models saved to {save_dir}")
    
    # Test the trained model
    generator = HumanInteractionGenerator(ai_model)
    
    print("\nTesting trained model...")
    mouse_path = generator.generate_mouse_path((0, 0), (400, 300))
    typing_seq = generator.generate_typing_sequence("Testing the trained model!")
    
    print(f"✓ Generated mouse path with {len(mouse_path.points)} points")
    print(f"✓ Generated typing with {len(typing_seq.keys)} keystrokes")
    print("✓ Training workflow example completed!\n")

def example_4_advanced_generation():
    """Example 4: Advanced interaction generation techniques"""
    print("=" * 60)
    print("EXAMPLE 4: Advanced Generation Techniques")
    print("=" * 60)
    
    config = InteractionConfig()
    ai_model = HumanInteractionAI(config)
    generator = HumanInteractionGenerator(ai_model)
    
    # Example 4a: Complex mouse paths
    print("4a. Complex Mouse Movements:")
    
    # Multi-point path
    waypoints = [(0, 0), (200, 100), (400, 50), (600, 200), (800, 300)]
    print(f"Generating path through {len(waypoints)} waypoints...")
    
    full_path_points = []
    full_path_timings = []
    current_time = 0.0
    
    for i in range(len(waypoints) - 1):
        segment = generator.generate_mouse_path(waypoints[i], waypoints[i + 1])
        
        # Adjust timings to be cumulative
        adjusted_timings = [t + current_time for t in segment.timings]
        
        full_path_points.extend(segment.points)
        full_path_timings.extend(adjusted_timings)
        current_time = adjusted_timings[-1]
    
    print(f"✓ Complex path: {len(full_path_points)} points, {current_time:.2f}s total")
    
    # Example 4b: Realistic typing scenarios
    print("\n4b. Realistic Typing Scenarios:")
    
    scenarios = [
        ("Email", "<EMAIL>"),
        ("Password", "MySecurePassword123!"),
        ("Search query", "how to train AI models"),
        ("Form data", "John Doe\n123 Main St\nAnytown, ST 12345"),
        ("Code snippet", "def hello_world():\n    print('Hello, World!')")
    ]
    
    for scenario_name, text in scenarios:
        typing_seq = generator.generate_typing_sequence(text, include_errors=True)
        total_time = typing_seq.timings[-1] if typing_seq.timings else 0
        wpm = (len(text.split()) / total_time * 60) if total_time > 0 else 0
        
        print(f"  {scenario_name}: {len(typing_seq.keys)} keys, {len(typing_seq.errors)} errors, {wpm:.1f} WPM")
    
    # Example 4c: Adaptive scrolling
    print("\n4c. Adaptive Scrolling Patterns:")
    
    scroll_scenarios = [
        ("Quick scan", 0, 500),
        ("Detailed reading", 0, 1500),
        ("Back to top", 2000, 0),
        ("Precise positioning", 800, 850)
    ]
    
    for scenario_name, start, target in scroll_scenarios:
        scroll_seq = generator.generate_scroll_sequence(target, start)
        total_time = scroll_seq.timings[-1] if scroll_seq.timings else 0
        distance = abs(target - start)
        
        print(f"  {scenario_name}: {distance}px in {total_time:.2f}s, {len(scroll_seq.scroll_deltas)} events")
    
    print("✓ Advanced generation example completed!\n")

def example_5_fatigue_simulation():
    """Example 5: Fatigue simulation over extended sessions"""
    print("=" * 60)
    print("EXAMPLE 5: Fatigue Simulation")
    print("=" * 60)
    
    config = InteractionConfig(simulate_fatigue=True)
    ai_model = HumanInteractionAI(config)
    generator = HumanInteractionGenerator(ai_model)
    
    print("Simulating user behavior over extended session...")
    
    # Simulate actions over time
    session_data = []
    
    for minute in range(0, 61, 10):  # Every 10 minutes for 1 hour
        # Simulate passage of time
        generator.actions_performed = minute * 20  # 20 actions per minute
        generator.session_start_time = time.time() - (minute * 60)  # Simulate time passage
        
        # Generate interactions
        mouse_path = generator.generate_mouse_path((100, 100), (500, 300))
        typing_seq = generator.generate_typing_sequence("Testing fatigue simulation at minute " + str(minute))
        
        # Calculate metrics
        avg_mouse_interval = sum(mouse_path.timings[i] - mouse_path.timings[i-1] 
                               for i in range(1, len(mouse_path.timings))) / (len(mouse_path.timings) - 1)
        
        avg_typing_interval = sum(typing_seq.timings[i] - typing_seq.timings[i-1] 
                                for i in range(1, len(typing_seq.timings))) / (len(typing_seq.timings) - 1)
        
        session_data.append({
            'minute': minute,
            'mouse_interval': avg_mouse_interval,
            'typing_interval': avg_typing_interval,
            'typing_errors': len(typing_seq.errors)
        })
        
        print(f"  Minute {minute:2d}: Mouse {avg_mouse_interval:.3f}s, Typing {avg_typing_interval:.3f}s, Errors {len(typing_seq.errors)}")
    
    # Show fatigue progression
    print("\nFatigue progression analysis:")
    initial_mouse = session_data[0]['mouse_interval']
    final_mouse = session_data[-1]['mouse_interval']
    initial_typing = session_data[0]['typing_interval']
    final_typing = session_data[-1]['typing_interval']
    
    print(f"  Mouse timing: {initial_mouse:.3f}s → {final_mouse:.3f}s ({(final_mouse/initial_mouse-1)*100:+.1f}%)")
    print(f"  Typing timing: {initial_typing:.3f}s → {final_typing:.3f}s ({(final_typing/initial_typing-1)*100:+.1f}%)")
    
    total_errors = sum(data['typing_errors'] for data in session_data)
    print(f"  Total errors increased over session: {total_errors}")
    
    print("✓ Fatigue simulation example completed!\n")

async def example_6_nodriver_integration():
    """Example 6: Integration with nodriver (simulated)"""
    print("=" * 60)
    print("EXAMPLE 6: NoDriver Integration (Simulated)")
    print("=" * 60)
    
    config = InteractionConfig()
    ai_model = HumanInteractionAI(config)
    generator = HumanInteractionGenerator(ai_model)
    
    # Create integration instance
    integration = NoDriverIntegration(generator)
    
    print("Simulating browser automation with human-like behavior...")
    
    # Simulate a typical automation workflow
    class MockDriver:
        def __init__(self):
            self.mouse_x, self.mouse_y = 0, 0
            self.scroll_position = 0
            
        async def move_mouse(self, x, y):
            self.mouse_x, self.mouse_y = x, y
            print(f"    Mouse moved to ({x:.0f}, {y:.0f})")
            
        async def type_text(self, text):
            print(f"    Typed: '{text}'")
            
        async def scroll(self, delta):
            self.scroll_position += delta
            print(f"    Scrolled {delta:.0f}px (position: {self.scroll_position:.0f})")
    
    driver = MockDriver()
    
    # Simulate human-like automation sequence
    print("\n1. Navigate to search box:")
    mouse_path = generator.generate_mouse_path((0, 0), (300, 100))
    for i, (point, timing) in enumerate(zip(mouse_path.points, mouse_path.timings)):
        if i > 0:
            await asyncio.sleep(min(0.01, timing - mouse_path.timings[i-1]))  # Speed up for demo
        await driver.move_mouse(point[0], point[1])
    
    print("\n2. Type search query:")
    typing_seq = generator.generate_typing_sequence("human-like automation")
    for i, (key, timing) in enumerate(zip(typing_seq.keys, typing_seq.timings)):
        if i > 0:
            await asyncio.sleep(min(0.01, timing - typing_seq.timings[i-1]))  # Speed up for demo
        if key == 'Backspace':
            print("    Backspace")
        else:
            await driver.type_text(key)
    
    print("\n3. Scroll to results:")
    scroll_seq = generator.generate_scroll_sequence(800, 0)
    for i, (delta, timing) in enumerate(zip(scroll_seq.scroll_deltas, scroll_seq.timings)):
        if i > 0:
            await asyncio.sleep(min(0.01, timing - scroll_seq.timings[i-1]))  # Speed up for demo
        await driver.scroll(delta)
    
    print("\n4. Click on result:")
    click_path = generator.generate_mouse_path((driver.mouse_x, driver.mouse_y), (400, 250))
    for i, (point, timing) in enumerate(zip(click_path.points, click_path.timings)):
        if i > 0:
            await asyncio.sleep(min(0.01, timing - click_path.timings[i-1]))  # Speed up for demo
        await driver.move_mouse(point[0], point[1])
    
    print("    Clicked on search result")
    print("✓ NoDriver integration example completed!\n")

def example_7_performance_analysis():
    """Example 7: Performance analysis and optimization"""
    print("=" * 60)
    print("EXAMPLE 7: Performance Analysis")
    print("=" * 60)
    
    config = InteractionConfig()
    ai_model = HumanInteractionAI(config)
    generator = HumanInteractionGenerator(ai_model)
    
    # Benchmark generation performance
    print("Benchmarking interaction generation performance...")
    
    num_iterations = 100
    
    # Mouse movement benchmark
    start_time = time.time()
    for _ in range(num_iterations):
        generator.generate_mouse_path((0, 0), (500, 300))
    mouse_time = time.time() - start_time
    
    # Typing benchmark
    start_time = time.time()
    for _ in range(num_iterations):
        generator.generate_typing_sequence("Performance test text")
    typing_time = time.time() - start_time
    
    # Scrolling benchmark
    start_time = time.time()
    for _ in range(num_iterations):
        generator.generate_scroll_sequence(1000, 0)
    scroll_time = time.time() - start_time
    
    print(f"Performance results ({num_iterations} iterations):")
    print(f"  Mouse generation: {mouse_time/num_iterations*1000:.2f} ms per path")
    print(f"  Typing generation: {typing_time/num_iterations*1000:.2f} ms per sequence")
    print(f"  Scroll generation: {scroll_time/num_iterations*1000:.2f} ms per sequence")
    
    # Memory usage analysis
    import psutil
    import os
    
    process = psutil.Process(os.getpid())
    memory_mb = process.memory_info().rss / 1024 / 1024
    print(f"  Current memory usage: {memory_mb:.1f} MB")
    
    # Quality analysis
    print("\nQuality analysis:")
    
    # Generate sample for analysis
    mouse_paths = [generator.generate_mouse_path((0, 0), (500, 300)) for _ in range(10)]
    typing_seqs = [generator.generate_typing_sequence("Quality test") for _ in range(10)]
    
    # Analyze mouse path smoothness
    smoothness_scores = []
    for path in mouse_paths:
        if len(path.points) > 2:
            velocities = []
            for i in range(1, len(path.points)):
                dx = path.points[i][0] - path.points[i-1][0]
                dy = path.points[i][1] - path.points[i-1][1]
                dt = path.timings[i] - path.timings[i-1]
                if dt > 0:
                    velocity = (dx**2 + dy**2)**0.5 / dt
                    velocities.append(velocity)
            
            if velocities:
                smoothness = 1.0 / (1.0 + (sum(velocities) / len(velocities)))
                smoothness_scores.append(smoothness)
    
    avg_smoothness = sum(smoothness_scores) / len(smoothness_scores) if smoothness_scores else 0
    print(f"  Average mouse path smoothness: {avg_smoothness:.3f}")
    
    # Analyze typing error rates
    total_chars = sum(len([k for k in seq.keys if k != 'Backspace']) for seq in typing_seqs)
    total_errors = sum(len(seq.errors) for seq in typing_seqs)
    error_rate = total_errors / total_chars if total_chars > 0 else 0
    
    print(f"  Typing error rate: {error_rate:.1%}")
    print("✓ Performance analysis example completed!\n")

def main():
    """Run all examples"""
    print("🤖 Human Interaction AI Model - Comprehensive Examples")
    print("=" * 60)
    print("This script demonstrates all capabilities of the AI model.")
    print("Each example showcases different aspects and use cases.\n")
    
    try:
        # Run synchronous examples
        example_1_basic_usage()
        example_2_custom_configuration()
        example_3_training_workflow()
        example_4_advanced_generation()
        example_5_fatigue_simulation()
        example_7_performance_analysis()
        
        # Run async example
        print("Running async example...")
        asyncio.run(example_6_nodriver_integration())
        
        print("🎉 All examples completed successfully!")
        print("\nNext steps:")
        print("1. Collect training data using the interaction recorder")
        print("2. Train the model with your data: python ai_model/train_model.py")
        print("3. Test the trained model: python ai_model/test_model.py")
        print("4. Integrate with your automation framework")
        
    except KeyboardInterrupt:
        print("\n⚠️  Examples interrupted by user")
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()