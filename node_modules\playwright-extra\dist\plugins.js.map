{"version": 3, "file": "plugins.js", "sourceRoot": "", "sources": ["../src/plugins.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAAyB;AACzB,MAAM,KAAK,GAAG,IAAA,eAAK,EAAC,0BAA0B,CAAC,CAAA;AAU/C,4CAAiD;AACjD,+EAAkE;AAElE,MAAa,UAAU;IAMrB;QALiB,aAAQ,GAAa,EAAE,CAAA;QACvB,wBAAmB,GAAqB,IAAI,GAAG,EAAE,CAAA;QACjD,0BAAqB,GACpC,IAAI,GAAG,EAAE,CAAA;IAEI,CAAC;IAEhB;;OAEG;IACH,IAAW,IAAI;QACb,OAAO,IAAI,CAAC,QAAQ,CAAA;IACtB,CAAC;IAED;;OAEG;IACH,IAAW,KAAK;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;IACvC,CAAC;IAED;;;;;OAKG;IACI,GAAG,CAAC,MAAc;;QACvB,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,EAAE;YACvC,OAAO,KAAK,CAAA;SACb;QACD,IAAI,CAAC,CAAC,MAAM,CAAC,kBAAkB,EAAE;YAC/B,MAAM,CAAC,kBAAkB,CAAC,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC,CAAA;SACvD;QACD,oGAAoG;QACpG,IAAI,CAAC,CAAC,MAAM,CAAC,0BAA0B,EAAE;YACvC,MAAM,CAAC,0BAA0B,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAA;SACjE;QACD,IAAI,MAAA,MAAM,CAAC,YAAY,0CAAE,GAAG,CAAC,iBAAiB,CAAC,EAAE;YAC/C,MAAM,CAAC,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;SACpD;QACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;QAC1B,OAAO,IAAI,CAAA;IACb,CAAC;IAED,wDAAwD;IAC9C,qBAAqB,CAAC,MAAc;QAC5C,IACE,CAAC,MAAM;YACP,OAAO,MAAM,KAAK,QAAQ;YAC1B,CAAC,MAAM,CAAC,uBAAuB,EAC/B;YACA,OAAO,CAAC,KAAK,CACX,qEAAqE,EACrE,MAAM,CACP,CAAA;YACD,OAAO,KAAK,CAAA;SACb;QACD,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;YAChB,OAAO,CAAC,KAAK,CACX,qDAAqD,EACrD,MAAM,CACP,CAAA;YACD,OAAO,KAAK,CAAA;SACb;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,0FAA0F;IACnF,aAAa,CAAC,MAAc,EAAE,MAAwB,EAAE,GAAU;QACvE,OAAO,CAAC,IAAI,CACV,qCAAqC,MAAM,gBAAgB,MAAM,CAAC,IAAI,IAAI,EAC1E,GAAG,CACJ,CAAA;IACH,CAAC;IAED;;;;;;;;OAQG;IACI,qBAAqB,CAAC,cAAsB,EAAE,IAAS;QAC5D,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,CAAA;QAClD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;;;OAQG;IACI,uBAAuB,CAC5B,cAAsB,EACtB,YAAoC;QAEpC,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,cAAc,EAAE,YAAY,CAAC,CAAA;QAC5D,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;OAGG;IACI,OAAO;QACZ,IAAI,CAAC,mBAAmB,EAAE,CAAA;QAC1B,IAAI,CAAC,KAAK,EAAE,CAAA;IACd,CAAC;IAED,mDAAmD;IACzC,cAAc,CAAC,UAA4B;QACnD,OAAO,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YACnC,8GAA8G;YAC9G,IACE,CAAC,CAAC,MAAM,CAAC,kBAAkB;gBAC3B,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,kBAAkB,CAAC,EACxC;gBACA,OAAO,MAAM,CAAC,kBAAkB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;aACtD;YACD,OAAO,UAAU,IAAI,MAAM,CAAA;QAC7B,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,kFAAkF;IACxE,2BAA2B,CACnC,MAAc,EACd,MAAe,EACf,IAAyC;QAEzC,MAAM,UAAU,GAAG,MAAM,CAAC,uBAAuB,IAAI,CAAC,MAAM,CAAC,eAAe,CAAA;QAC5E,MAAM,eAAe,GAAuB;YAC1C,WAAW;YACX,eAAe;YACf,aAAa;YACb,cAAc;YACd,aAAa;SACd,CAAA;QACD,MAAM,aAAa,GAAG,eAAe,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QACtD,IAAI,CAAC,UAAU,IAAI,CAAC,aAAa,EAAE;YACjC,OAAO,IAAI,CAAA;SACZ;QACD,KAAK,CAAC,6BAA6B,EAAE,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;QACzD,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAA,gDAAkB,EAAC,GAAU,CAAC,CAAC,CAEzD,CAAA;IACH,CAAC;IAED;;;;;;;;;OASG;IACI,QAAQ,CACb,MAAe,EACf,GAAG,IAAyC;;QAE5C,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAC3C,KAAK,CAAC,UAAU,EAAE,MAAM,EAAE;YACxB,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;YACzB,gBAAgB,EAAE,OAAO,CAAC,MAAM;SACjC,CAAC,CAAA;QACF,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC5B,IAAI;gBACF,IAAI,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;gBACxE,MAAM,MAAM,GAAG,MAAA,MAAA,MAAM,CAAC,MAAM,CAAC,0CAAE,WAAW,0CAAE,IAAI,CAAA;gBAChD,KAAK,CAAC,oBAAoB,EAAE;oBAC1B,MAAM,EAAE,MAAM,CAAC,IAAI;oBACnB,MAAM;oBACN,MAAM;iBACP,CAAC,CAAA;gBACF,IAAI,MAAM,KAAK,eAAe,EAAE;oBAC9B,CAAC;oBAAC,MAAM,CAAC,MAAM,CAAS,CAAC,GAAG,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,GAAQ,EAAE,EAAE,CACnD,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAC,CACxC,CAAA;iBACF;qBAAM;oBACL,CAAC;oBAAC,MAAM,CAAC,MAAM,CAAS,CAAC,GAAG,IAAI,CAAC,CAAA;iBAClC;aACF;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,GAAU,CAAC,CAAA;aAC/C;SACF;IACH,CAAC;IAED;;;;;;;;;;;;;;;OAeG;IACI,KAAK,CAAC,gBAAgB,CAC3B,MAAe,EACf,GAAG,IAAyC;QAE5C,MAAM,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;QAC3C,KAAK,CAAC,kBAAkB,EAAE,MAAM,EAAE;YAChC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM;YACzB,gBAAgB,EAAE,OAAO,CAAC,MAAM;SACjC,CAAC,CAAA;QAEF,IAAI,QAAQ,GAAQ,IAAI,CAAA;QACxB,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC5B,IAAI;gBACF,IAAI,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;gBACxE,QAAQ,GAAG,MAAO,MAAM,CAAC,MAAM,CAAS,CAAC,GAAG,IAAI,CAAC,CAAA;gBACjD,2FAA2F;gBAC3F,IAAI,QAAQ,KAAK,SAAS,EAAE;oBAC1B,IAAI,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAA;iBACnB;aACF;YAAC,OAAO,GAAG,EAAE;gBACZ,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,GAAU,CAAC,CAAA;gBAC9C,OAAO,QAAQ,CAAA;aAChB;SACF;QACD,OAAO,QAAQ,CAAA;IACjB,CAAC;IAED;;;;;;OAMG;IACO,KAAK;QACb,KAAK,CAAC,cAAc,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;QACjC,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ;aAC1B,MAAM,CAAC,CAAC,CAAC,EAAE,WAAC,OAAA,MAAA,CAAC,CAAC,YAAY,0CAAE,GAAG,CAAC,SAAS,CAAC,CAAA,EAAA,CAAC;aAC3C,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAA;QACnB,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;YAC1B,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAA;YAC3D,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;SACtD;QACD,KAAK,CAAC,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;IAClC,CAAC;IAED;;;;;;;;;;;;OAYG;IACO,OAAO,CAAC,IAAa;QAC7B,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ;aACvB,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;aAC5B,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;aAC5D,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,EAAE,GAAG,GAAG,CAAC,EAAE,EAAE,CAAC,CAAA;QAC7C,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IAC/D,CAAC;IAED;;OAEG;IACO,oBAAoB;QAC5B,KAAK,CAAC,sBAAsB,CAAC,CAAA;QAC7B,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QACvC,IAAI,CAAC,QAAQ;aACV,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;aAC5C,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,0CAA0C;aAChF,OAAO,CAAC,MAAM,CAAC,EAAE;YAChB,CAAC;YAAA,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE;gBAClC,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,uBAAuB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAA;gBACnD,IAAI,CAAC,GAAG,CAAC,CAAW,CAAC,CAAA;YACvB,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACN,CAAC;IAED;;;;;;;;OAQG;IACO,yBAAyB;QACjC,KAAK,CAAC,2BAA2B,CAAC,CAAA;QAElC,qDAAqD;QACrD,MAAM,sBAAsB,GAAG,CAC7B,UAAkB,EAClB,cAAsB,EACtB,EAAE;YACF,6DAA6D;YAC7D,IAAI,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;gBAClD,OAAO,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,cAAc,CAAiB,CAAA;aACtE;YAED,MAAM,gBAAgB,GAAG,CAAC,yBAAyB,CAAC,CAAA,CAAC,0BAA0B;YAC/E,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CACvD,cAAc,CAAC,UAAU,CAAC,MAAM,CAAC,CAClC,CAAA;YACD,MAAM,YAAY,GAAa,EAAE,CAAA;YACjC,mHAAmH;YACnH,IAAI,CAAC,iBAAiB,EAAE;gBACtB,YAAY,CAAC,IAAI,CACf,GAAG,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,GAAG,cAAc,CAAC,CAC3D,CAAA;aACF;YACD,oEAAoE;YACpE,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;YACjC,MAAM,YAAY,GAAG,IAAA,wBAAe,EAAe,YAAY,CAAC,CAAA;YAChE,IAAI,YAAY,EAAE;gBAChB,OAAO,YAAY,CAAA;aACpB;YAED,MAAM,WAAW,GAAG;cACZ,UAAU,aAAa,cAAc;;;EAGjD,YAAY;iBACX,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,YAAY,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;iBAC3D,IAAI,CAAC,UAAU,CAAC;;;;;;OAMZ,CAAA;YACD,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;YACzB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAA;QAChD,CAAC,CAAA;QAED,MAAM,mBAAmB,GAAG,IAAI,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC/C,MAAM,kCAAkC,GAAG,CAAC,EAC1C,IAAI,EAAE,UAAU,EAChB,YAAY,EACL,EAAO,EAAE;YAChB,IAAI,CAAC,YAAY,EAAE;gBACjB,OAAM;aACP;YACD,MAAM,iBAAiB,GAAG,CAAC,cAAsB,EAAE,IAAU,EAAE,EAAE;gBAC/D,MAAM,YAAY,GAAG,sBAAsB,CAAC,UAAU,EAAE,cAAc,CAAC,CAAA;gBACvE,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAA;gBACjE,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,CAAA;gBACjC,IAAI,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;oBACxC,KAAK,CAAC,UAAU,EAAE,+BAA+B,EAAE,MAAM,CAAC,IAAI,CAAC,CAAA;oBAC/D,OAAM;iBACP;gBACD,mBAAmB,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAA;gBACpC,KAAK,CAAC,UAAU,EAAE,2BAA2B,EAAE,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;gBACjE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAA;gBAChB,OAAO,kCAAkC,CAAC,MAAM,CAAC,CAAA;YACnD,CAAC,CAAA;YAED,IAAI,YAAY,YAAY,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;gBAC9D,OAAO,CAAC,GAAG,YAAY,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAChD,iBAAiB,CAAC,cAAc,CAAC,CAClC,CAAA;aACF;YACD,IAAI,YAAY,YAAY,GAAG,EAAE;gBAC/B,4DAA4D;gBAC5D,OAAO,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;aAC/D;QACH,CAAC,CAAA;QACD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,kCAAkC,CAAC,CAAA;IACvD,CAAC;IAED;;;OAGG;IACO,mBAAmB;QAC3B,KAAK,CAAC,qBAAqB,CAAC,CAAA;QAC5B,IAAI,CAAC,oBAAoB,EAAE,CAAA;QAC3B,IAAI,CAAC,yBAAyB,EAAE,CAAA;IAClC,CAAC;CACF;AA7YD,gCA6YC"}