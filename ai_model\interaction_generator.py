"""
Human-like Interaction Generator

This module uses the trained AI models to generate realistic human interactions
that can be used with nodriver or other browser automation frameworks.
"""

import torch
import numpy as np
import math
import time
import random
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass
import asyncio
import logging

try:
    from .human_interaction_ai import HumanInteractionAI, InteractionConfig
except ImportError:
    from human_interaction_ai import HumanInteractionAI, InteractionConfig

logger = logging.getLogger(__name__)

@dataclass
class MousePath:
    """Represents a generated mouse movement path"""
    points: List[Tuple[float, float]]  # (x, y) coordinates
    timings: List[float]  # Timing for each point
    total_duration: float

@dataclass
class TypingSequence:
    """Represents a generated typing sequence"""
    keys: List[str]
    timings: List[float]
    errors: List[Dict]  # Error information
    corrections: List[Dict]  # Correction information

@dataclass
class ScrollSequence:
    """Represents a generated scroll sequence"""
    scroll_deltas: List[float]
    timings: List[float]
    pauses: List[float]  # Pause durations

class BezierCurve:
    """Utility class for generating smooth Bezier curves for mouse movement"""
    
    @staticmethod
    def cubic_bezier(t: float, p0: Tuple[float, float], p1: Tuple[float, float], 
                    p2: Tuple[float, float], p3: Tuple[float, float]) -> Tuple[float, float]:
        """Calculate point on cubic Bezier curve at parameter t (0-1)"""
        x = (1-t)**3 * p0[0] + 3*(1-t)**2*t * p1[0] + 3*(1-t)*t**2 * p2[0] + t**3 * p3[0]
        y = (1-t)**3 * p0[1] + 3*(1-t)**2*t * p1[1] + 3*(1-t)*t**2 * p2[1] + t**3 * p3[1]
        return (x, y)
    
    @staticmethod
    def generate_control_points(start: Tuple[float, float], end: Tuple[float, float], 
                              curvature: float = 0.3) -> Tuple[Tuple[float, float], Tuple[float, float]]:
        """Generate control points for a natural-looking curve"""
        dx = end[0] - start[0]
        dy = end[1] - start[1]
        distance = math.sqrt(dx**2 + dy**2)
        
        # Add some randomness to control points
        offset1 = distance * curvature * (0.5 + random.random() * 0.5)
        offset2 = distance * curvature * (0.5 + random.random() * 0.5)
        
        # Perpendicular direction for natural curve
        perp_x = -dy / distance if distance > 0 else 0
        perp_y = dx / distance if distance > 0 else 0
        
        # Add some randomness to the perpendicular offset
        perp_offset1 = (random.random() - 0.5) * distance * 0.2
        perp_offset2 = (random.random() - 0.5) * distance * 0.2
        
        p1 = (
            start[0] + dx * 0.3 + perp_x * perp_offset1,
            start[1] + dy * 0.3 + perp_y * perp_offset1
        )
        p2 = (
            start[0] + dx * 0.7 + perp_x * perp_offset2,
            start[1] + dy * 0.7 + perp_y * perp_offset2
        )
        
        return p1, p2

class TremorGenerator:
    """Generates realistic hand tremor patterns"""
    
    def __init__(self, config: InteractionConfig):
        self.config = config
        self.tremor_phase = random.uniform(0, 2 * math.pi)
    
    def generate_tremor(self, duration: float, sample_rate: float = 60.0) -> Tuple[List[float], List[float]]:
        """Generate tremor displacement for given duration"""
        if not self.config.simulate_hand_tremor:
            return [], []
        
        num_samples = int(duration * sample_rate)
        t = np.linspace(0, duration, num_samples)
        
        # Primary tremor frequency (8-12 Hz for essential tremor)
        primary_freq = self.config.tremor_frequency
        
        # Add harmonics for more realistic tremor
        tremor_x = (self.config.tremor_amplitude * 
                   (np.sin(2 * np.pi * primary_freq * t + self.tremor_phase) +
                    0.3 * np.sin(2 * np.pi * primary_freq * 2 * t + self.tremor_phase * 1.5) +
                    0.1 * np.sin(2 * np.pi * primary_freq * 3 * t + self.tremor_phase * 2)))
        
        tremor_y = (self.config.tremor_amplitude * 
                   (np.cos(2 * np.pi * primary_freq * t + self.tremor_phase * 0.7) +
                    0.3 * np.cos(2 * np.pi * primary_freq * 2 * t + self.tremor_phase * 1.2) +
                    0.1 * np.cos(2 * np.pi * primary_freq * 3 * t + self.tremor_phase * 1.8)))
        
        # Update phase for continuity
        self.tremor_phase += 2 * np.pi * primary_freq * duration
        self.tremor_phase = self.tremor_phase % (2 * np.pi)
        
        return tremor_x.tolist(), tremor_y.tolist()

class MicroMovementGenerator:
    """Generates subtle micro-movements that humans naturally make"""
    
    def __init__(self, config: InteractionConfig):
        self.config = config
    
    def generate_micro_movements(self, base_points: List[Tuple[float, float]], 
                                intensity: float = 1.0) -> List[Tuple[float, float]]:
        """Add micro-movements to a path"""
        if not self.config.add_micro_movements:
            return base_points
        
        enhanced_points = []
        for i, (x, y) in enumerate(base_points):
            # Add subtle random displacement
            micro_x = random.gauss(0, 0.5 * intensity * self.config.mouse_micro_movement_intensity)
            micro_y = random.gauss(0, 0.5 * intensity * self.config.mouse_micro_movement_intensity)
            
            # Add occasional larger micro-movements (muscle twitches)
            if random.random() < self.config.mouse_jitter_frequency:
                micro_x += random.gauss(0, 1.5 * intensity)
                micro_y += random.gauss(0, 1.5 * intensity)
            
            enhanced_points.append((x + micro_x, y + micro_y))
        
        return enhanced_points

class AttentionDriftSimulator:
    """Simulates attention drift and focus changes"""
    
    def __init__(self, config: InteractionConfig):
        self.config = config
        self.attention_level = 1.0
        self.last_attention_change = time.time()
    
    def update_attention(self) -> float:
        """Update and return current attention level"""
        if not self.config.attention_drift_simulation:
            return 1.0
        
        current_time = time.time()
        time_since_change = current_time - self.last_attention_change
        
        # Attention naturally fluctuates every 30-120 seconds
        if time_since_change > random.uniform(30, 120):
            # Gradual attention change
            attention_change = random.gauss(0, 0.1)
            self.attention_level = max(0.3, min(1.0, self.attention_level + attention_change))
            self.last_attention_change = current_time
        
        return self.attention_level
    
    def apply_attention_effects(self, timing: float) -> float:
        """Apply attention effects to timing"""
        attention = self.update_attention()
        
        # Lower attention = slower reactions and more pauses
        attention_factor = 0.5 + 0.5 * attention
        
        # Add occasional attention lapses
        if random.random() < (1 - attention) * 0.1:
            timing += random.uniform(0.5, 2.0)  # Attention lapse pause
        
        return timing * (1 / attention_factor)

class HumanInteractionGenerator:
    """Advanced class for generating ultra-realistic human-like interactions with anti-detection features"""
    
    def __init__(self, ai_model: HumanInteractionAI):
        self.ai_model = ai_model
        self.config = ai_model.config
        
        # Set models to evaluation mode
        self.ai_model.mouse_model.eval()
        self.ai_model.typing_model.eval()
        self.ai_model.scrolling_model.eval()
        
        # Enhanced session tracking
        self.session_start_time = time.time()
        self.actions_performed = 0
        self.last_action_time = time.time()
        
        # Behavioral consistency tracking
        self.behavioral_signature = {
            'mouse_speed_preference': random.uniform(0.7, 1.3),
            'typing_rhythm_base': random.uniform(0.8, 1.2),
            'scroll_momentum_preference': random.uniform(0.6, 1.4),
            'error_tendency': random.uniform(0.5, 2.0),
            'pause_frequency': random.uniform(0.7, 1.5)
        }
        
        # Environmental adaptation
        self.environmental_factors = {
            'time_of_day_factor': self._calculate_time_factor(),
            'stress_level': random.uniform(0.0, 0.3),
            'attention_span': random.uniform(0.7, 1.0),
            'fatigue_accumulation': 0.0
        }
        
        # Advanced noise generators
        self.tremor_generator = TremorGenerator(self.config)
        self.micro_movement_generator = MicroMovementGenerator(self.config)
        self.attention_drift_simulator = AttentionDriftSimulator(self.config)
    
    def _calculate_time_factor(self) -> float:
        """Calculate performance factor based on time of day"""
        import datetime
        current_hour = datetime.datetime.now().hour
        
        # Human performance typically peaks mid-morning and early evening
        if 9 <= current_hour <= 11 or 14 <= current_hour <= 16:
            return random.uniform(1.0, 1.2)  # Peak performance
        elif 22 <= current_hour or current_hour <= 6:
            return random.uniform(0.6, 0.8)  # Tired/sleepy
        else:
            return random.uniform(0.8, 1.1)  # Normal performance
        
    def generate_mouse_path(self, start: Tuple[float, float], end: Tuple[float, float], 
                           duration: Optional[float] = None) -> MousePath:
        """Generate an ultra-realistic human-like mouse movement path with advanced anti-detection features"""
        
        # Update environmental state
        self._update_environmental_state()
        
        if duration is None:
            # Enhanced duration calculation with behavioral consistency
            distance = math.sqrt((end[0] - start[0])**2 + (end[1] - start[1])**2)
            
            # Base speed adjusted by personal preference and environmental factors
            base_speed = 280.0 * self.behavioral_signature['mouse_speed_preference']
            base_speed *= self.environmental_factors['time_of_day_factor']
            base_speed *= (1 - self.environmental_factors['fatigue_accumulation'] * 0.3)
            
            base_duration = distance / base_speed
            
            # Add variance based on distance (longer movements have more variance)
            distance_factor = min(distance / 500.0, 1.0)  # Normalize to 500px
            variance = self.config.mouse_speed_variance * (0.5 + 0.5 * distance_factor)
            
            duration = base_duration * (1 + random.uniform(-variance, variance))
            duration = max(0.08, duration)  # Minimum duration
        
        # Enhanced control point generation with multiple curves for complex paths
        if distance > 200:  # Use multi-segment path for long movements
            return self._generate_complex_mouse_path(start, end, duration)
        else:
            return self._generate_simple_mouse_path(start, end, duration)
    
    def _generate_simple_mouse_path(self, start: Tuple[float, float], end: Tuple[float, float], 
                                   duration: float) -> MousePath:
        """Generate a simple mouse path for short movements"""
        # Generate control points with enhanced realism
        p1, p2 = BezierCurve.generate_control_points(start, end, self.config.mouse_smoothness)
        
        # Adjust control points based on behavioral signature
        adjustment_factor = self.behavioral_signature['mouse_speed_preference'] - 1.0
        if adjustment_factor != 0:
            # Modify control points to reflect personal movement style
            mid_x, mid_y = (start[0] + end[0]) / 2, (start[1] + end[1]) / 2
            p1 = (p1[0] + adjustment_factor * (p1[0] - mid_x) * 0.3, 
                  p1[1] + adjustment_factor * (p1[1] - mid_y) * 0.3)
            p2 = (p2[0] + adjustment_factor * (p2[0] - mid_x) * 0.3, 
                  p2[1] + adjustment_factor * (p2[1] - mid_y) * 0.3)
        
        # Generate points with enhanced timing
        num_points = max(8, int(duration * 75))  # Higher resolution for smoother movement
        points = []
        timings = []
        
        for i in range(num_points):
            t = i / (num_points - 1)
            
            # Enhanced easing with acceleration variance
            t_eased = self._enhanced_easing(t)
            
            # Calculate point on Bezier curve
            point = BezierCurve.cubic_bezier(t_eased, start, p1, p2, end)
            points.append(point)
            
            # Enhanced timing calculation
            base_time = duration * t
            
            # Add acceleration/deceleration variance
            if self.config.mouse_acceleration_variance > 0:
                accel_factor = 1 + math.sin(t * math.pi) * self.config.mouse_acceleration_variance * 0.1
                base_time *= accel_factor
            
            # Add timing randomization
            if self.config.randomize_timing:
                time_variance = random.gauss(0, 0.015)  # Gaussian noise for more realistic variance
                base_time += time_variance
            
            # Apply attention effects
            base_time = self.attention_drift_simulator.apply_attention_effects(base_time)
            
            timings.append(max(0, base_time))
        
        # Add tremor if enabled
        if self.config.simulate_hand_tremor:
            tremor_x, tremor_y = self.tremor_generator.generate_tremor(duration)
            if len(tremor_x) == len(points):
                points = [(p[0] + tx, p[1] + ty) for p, tx, ty in zip(points, tremor_x, tremor_y)]
        
        # Add micro-movements
        points = self.micro_movement_generator.generate_micro_movements(
            points, intensity=self.environmental_factors['attention_span']
        )
        
        # Add realistic errors and corrections
        points, timings = self._add_realistic_errors(points, timings, end)
        
        # Apply environmental effects
        timings = self._apply_environmental_effects(timings)
        
        self.actions_performed += 1
        self.last_action_time = time.time()
        
        return MousePath(points=points, timings=timings, total_duration=timings[-1] if timings else 0)
    
    def _generate_complex_mouse_path(self, start: Tuple[float, float], end: Tuple[float, float], 
                                    duration: float) -> MousePath:
        """Generate a complex multi-segment path for long movements"""
        # Break long movements into segments with intermediate points
        distance = math.sqrt((end[0] - start[0])**2 + (end[1] - start[1])**2)
        num_segments = min(int(distance / 150), 4)  # Max 4 segments
        
        all_points = []
        all_timings = []
        current_time = 0
        
        # Generate intermediate waypoints
        waypoints = [start]
        for i in range(1, num_segments):
            t = i / num_segments
            # Add some randomness to waypoints
            base_x = start[0] + t * (end[0] - start[0])
            base_y = start[1] + t * (end[1] - start[1])
            
            # Add perpendicular offset for more natural path
            perp_offset = random.uniform(-30, 30)
            dx, dy = end[0] - start[0], end[1] - start[1]
            length = math.sqrt(dx**2 + dy**2)
            if length > 0:
                perp_x = -dy / length * perp_offset
                perp_y = dx / length * perp_offset
                waypoints.append((base_x + perp_x, base_y + perp_y))
            else:
                waypoints.append((base_x, base_y))
        
        waypoints.append(end)
        
        # Generate path segments
        segment_duration = duration / num_segments
        for i in range(len(waypoints) - 1):
            segment_start = waypoints[i]
            segment_end = waypoints[i + 1]
            
            # Adjust duration based on segment distance
            segment_distance = math.sqrt((segment_end[0] - segment_start[0])**2 + 
                                       (segment_end[1] - segment_start[1])**2)
            adjusted_duration = segment_duration * (segment_distance / (distance / num_segments))
            
            segment_path = self._generate_simple_mouse_path(segment_start, segment_end, adjusted_duration)
            
            # Adjust timings to be relative to overall movement
            adjusted_timings = [t + current_time for t in segment_path.timings]
            
            if i == 0:
                all_points.extend(segment_path.points)
                all_timings.extend(adjusted_timings)
            else:
                # Skip first point to avoid duplication
                all_points.extend(segment_path.points[1:])
                all_timings.extend(adjusted_timings[1:])
            
            current_time = adjusted_timings[-1] if adjusted_timings else current_time
        
        return MousePath(points=all_points, timings=all_timings, total_duration=current_time)
    
    def _enhanced_easing(self, t: float) -> float:
        """Enhanced easing function with more realistic acceleration patterns"""
        # Combine multiple easing functions for more natural movement
        cubic_ease = self._ease_in_out_cubic(t)
        
        # Add slight overshoot for very human-like movement
        if t > 0.8:
            overshoot_factor = 0.02 * math.sin((t - 0.8) * math.pi * 5)
            cubic_ease += overshoot_factor
        
        # Add micro-hesitations
        if 0.3 < t < 0.7 and random.random() < 0.1:
            hesitation = 0.01 * math.sin(t * math.pi * 10)
            cubic_ease += hesitation
        
        return max(0, min(1, cubic_ease))
    
    def _add_realistic_errors(self, points: List[Tuple[float, float]], timings: List[float], 
                             target: Tuple[float, float]) -> Tuple[List[Tuple[float, float]], List[float]]:
        """Add realistic human errors and corrections"""
        if not self.config.human_error_simulation:
            return points, timings
        
        # Determine if an error should occur
        error_probability = self.config.mouse_error_rate * self.behavioral_signature['error_tendency']
        error_probability *= (1 + self.environmental_factors['stress_level'])
        
        if random.random() < error_probability:
            # Add overshoot error
            last_point = points[-1]
            overshoot_distance = random.uniform(3, 12)
            overshoot_angle = random.uniform(0, 2 * math.pi)
            
            overshoot_x = target[0] + overshoot_distance * math.cos(overshoot_angle)
            overshoot_y = target[1] + overshoot_distance * math.sin(overshoot_angle)
            
            # Add overshoot point
            points.append((overshoot_x, overshoot_y))
            timings.append(timings[-1] + random.uniform(0.03, 0.08))
            
            # Add pause (realization of error)
            pause_duration = random.uniform(0.1, 0.3) * self.behavioral_signature['pause_frequency']
            timings.append(timings[-1] + pause_duration)
            points.append((overshoot_x, overshoot_y))  # Stay at overshoot position during pause
            
            # Add correction movement
            correction_points = self._generate_simple_mouse_path(
                (overshoot_x, overshoot_y), target, random.uniform(0.15, 0.4)
            )
            
            # Merge correction into main path
            base_time = timings[-1]
            for i, (point, timing) in enumerate(zip(correction_points.points[1:], correction_points.timings[1:])):
                points.append(point)
                timings.append(base_time + timing)
        
        return points, timings
    
    def _update_environmental_state(self):
        """Update environmental factors that affect behavior"""
        current_time = time.time()
        session_duration = current_time - self.session_start_time
        
        # Update fatigue accumulation
        if self.config.simulate_fatigue:
            # Fatigue increases over time and with actions
            time_fatigue = min(session_duration / 3600, 1.0)  # Max after 1 hour
            action_fatigue = min(self.actions_performed / 500, 1.0)  # Max after 500 actions
            self.environmental_factors['fatigue_accumulation'] = (time_fatigue + action_fatigue) / 2
        
        # Update stress level based on recent action frequency
        time_since_last = current_time - self.last_action_time
        if time_since_last < 0.5:  # Rapid actions increase stress
            self.environmental_factors['stress_level'] = min(
                self.environmental_factors['stress_level'] + 0.05, 1.0
            )
        elif time_since_last > 5.0:  # Long pauses reduce stress
            self.environmental_factors['stress_level'] = max(
                self.environmental_factors['stress_level'] - 0.02, 0.0
            )
    
    def _apply_environmental_effects(self, timings: List[float]) -> List[float]:
        """Apply environmental effects to timing"""
        if not self.config.environmental_adaptation:
            return timings
        
        # Apply fatigue effects (slower movements when tired)
        fatigue_factor = 1 + self.environmental_factors['fatigue_accumulation'] * 0.5
        
        # Apply stress effects (more erratic timing when stressed)
        stress_factor = 1 + self.environmental_factors['stress_level'] * 0.2
        
        # Apply time of day effects
        time_factor = self.environmental_factors['time_of_day_factor']
        
        adjusted_timings = []
        for timing in timings:
            adjusted = timing * fatigue_factor * stress_factor / time_factor
            
            # Add stress-induced timing variance
            if self.environmental_factors['stress_level'] > 0.3:
                stress_variance = random.gauss(0, 0.02 * self.environmental_factors['stress_level'])
                adjusted += stress_variance
            
            adjusted_timings.append(max(0, adjusted))
        
        return adjusted_timings
    
    def generate_typing_sequence(self, text: str, include_errors: bool = True) -> TypingSequence:
        """Generate a human-like typing sequence for the given text"""
        
        keys = []
        timings = []
        errors = []
        corrections = []
        
        current_time = 0.0
        base_interval = 60.0 / (self.config.typing_speed_wpm * 5)  # Average time per character
        
        i = 0
        while i < len(text):
            char = text[i]
            
            # Calculate typing interval with variance
            interval = base_interval * random.uniform(0.7, 1.5)
            
            # Add reaction time for special characters
            if char in ' .,!?;:':
                interval *= random.uniform(1.2, 1.8)
            elif char.isupper():
                interval *= random.uniform(1.1, 1.4)
            
            # Simulate typing errors
            should_error = (include_errors and 
                          random.random() < self.config.typing_error_rate and 
                          char.isalnum())
            
            if should_error:
                # Generate a typo
                error_char = self._generate_typo(char)
                keys.append(error_char)
                timings.append(current_time)
                current_time += interval
                
                # Record the error
                errors.append({
                    'position': len(keys) - 1,
                    'intended_char': char,
                    'typed_char': error_char,
                    'error_type': 'typo'
                })
                
                # Simulate realization and correction
                correction_delay = self.config.typing_correction_delay * random.uniform(0.5, 2.0)
                current_time += correction_delay
                
                # Backspace
                keys.append('Backspace')
                timings.append(current_time)
                current_time += base_interval * 0.5
                
                # Type correct character
                keys.append(char)
                timings.append(current_time)
                current_time += interval
                
                corrections.append({
                    'error_position': len(keys) - 3,
                    'backspace_position': len(keys) - 2,
                    'correction_position': len(keys) - 1,
                    'delay': correction_delay
                })
            else:
                # Normal typing
                keys.append(char)
                timings.append(current_time)
                current_time += interval
            
            i += 1
        
        # Apply fatigue effects
        if self.config.simulate_fatigue:
            fatigue_factor = self._calculate_fatigue_factor()
            for i in range(len(timings)):
                timings[i] *= fatigue_factor
        
        self.actions_performed += len(keys)
        return TypingSequence(keys=keys, timings=timings, errors=errors, corrections=corrections)
    
    def generate_scroll_sequence(self, target_position: float, current_position: float = 0.0) -> ScrollSequence:
        """Generate a human-like scrolling sequence to reach target position"""
        
        scroll_deltas = []
        timings = []
        pauses = []
        
        current_time = 0.0
        current_pos = current_position
        target_distance = target_position - current_position
        
        # Determine scroll direction
        scroll_direction = 1 if target_distance > 0 else -1
        remaining_distance = abs(target_distance)
        
        while remaining_distance > 10:  # Continue until close to target
            # Generate scroll delta with variance
            base_scroll = min(remaining_distance * 0.3, 120)  # Max 120px per scroll
            scroll_delta = base_scroll * scroll_direction * random.uniform(0.7, 1.3)
            
            # Add scroll smoothness
            if self.config.scroll_smoothness > 0:
                scroll_delta *= self.config.scroll_smoothness
            
            scroll_deltas.append(scroll_delta)
            timings.append(current_time)
            
            # Update position
            current_pos += scroll_delta
            remaining_distance = abs(target_position - current_pos)
            
            # Calculate next scroll timing
            scroll_interval = random.uniform(0.05, 0.2)  # 50-200ms between scrolls
            current_time += scroll_interval
            
            # Occasionally add pauses (human hesitation)
            if random.random() < 0.1:  # 10% chance of pause
                pause_duration = random.uniform(0.2, 1.0)
                pauses.append(pause_duration)
                current_time += pause_duration
            else:
                pauses.append(0.0)
        
        # Add overshoot if configured
        if (self.config.scroll_overshoot_rate > 0 and 
            random.random() < self.config.scroll_overshoot_rate):
            
            overshoot_distance = random.uniform(20, 80) * scroll_direction
            scroll_deltas.append(overshoot_distance)
            timings.append(current_time)
            pauses.append(0.0)
            current_time += random.uniform(0.1, 0.3)
            
            # Correction scroll
            correction_scroll = -overshoot_distance * random.uniform(0.7, 1.0)
            scroll_deltas.append(correction_scroll)
            timings.append(current_time)
            pauses.append(random.uniform(0.1, 0.5))
        
        # Apply fatigue effects
        if self.config.simulate_fatigue:
            fatigue_factor = self._calculate_fatigue_factor()
            for i in range(len(timings)):
                timings[i] *= fatigue_factor
        
        self.actions_performed += len(scroll_deltas)
        return ScrollSequence(scroll_deltas=scroll_deltas, timings=timings, pauses=pauses)
    
    def _ease_in_out_cubic(self, t: float) -> float:
        """Cubic easing function for natural movement"""
        if t < 0.5:
            return 4 * t * t * t
        else:
            return 1 - pow(-2 * t + 2, 3) / 2
    
    def _generate_typo(self, intended_char: str) -> str:
        """Generate a realistic typo for the intended character"""
        # Common typo patterns based on keyboard layout
        typo_map = {
            'a': ['s', 'q', 'w'],
            'b': ['v', 'g', 'h', 'n'],
            'c': ['x', 'd', 'f', 'v'],
            'd': ['s', 'e', 'r', 'f', 'c', 'x'],
            'e': ['w', 'r', 'd', 's'],
            'f': ['d', 'r', 't', 'g', 'v', 'c'],
            'g': ['f', 't', 'y', 'h', 'b', 'v'],
            'h': ['g', 'y', 'u', 'j', 'n', 'b'],
            'i': ['u', 'o', 'k', 'j'],
            'j': ['h', 'u', 'i', 'k', 'm', 'n'],
            'k': ['j', 'i', 'o', 'l', 'm'],
            'l': ['k', 'o', 'p'],
            'm': ['n', 'j', 'k'],
            'n': ['b', 'h', 'j', 'm'],
            'o': ['i', 'p', 'l', 'k'],
            'p': ['o', 'l'],
            'q': ['w', 'a'],
            'r': ['e', 't', 'f', 'd'],
            's': ['a', 'w', 'e', 'd', 'x', 'z'],
            't': ['r', 'y', 'g', 'f'],
            'u': ['y', 'i', 'j', 'h'],
            'v': ['c', 'f', 'g', 'b'],
            'w': ['q', 'e', 's', 'a'],
            'x': ['z', 's', 'd', 'c'],
            'y': ['t', 'u', 'h', 'g'],
            'z': ['x', 's']
        }
        
        char_lower = intended_char.lower()
        if char_lower in typo_map:
            typo_char = random.choice(typo_map[char_lower])
            return typo_char.upper() if intended_char.isupper() else typo_char
        else:
            # For characters not in map, return a random nearby character
            return chr(ord(intended_char) + random.choice([-1, 1]))
    
    def _calculate_fatigue_factor(self) -> float:
        """Calculate fatigue factor based on session duration and actions performed"""
        if not self.config.simulate_fatigue:
            return 1.0
        
        session_duration = time.time() - self.session_start_time
        
        # Fatigue increases with time and number of actions
        time_fatigue = min(session_duration / 3600, 1.0)  # Max fatigue after 1 hour
        action_fatigue = min(self.actions_performed / 1000, 1.0)  # Max fatigue after 1000 actions
        
        # Combined fatigue factor (1.0 = no fatigue, 2.0 = maximum fatigue)
        fatigue = 1.0 + (time_fatigue + action_fatigue) * 0.5
        
        return fatigue
    
    def reset_session(self):
        """Reset session tracking for fatigue simulation"""
        self.session_start_time = time.time()
        self.actions_performed = 0

# Integration with nodriver
class NoDriverIntegration:
    """Integration class for using generated interactions with nodriver"""
    
    def __init__(self, generator: HumanInteractionGenerator):
        self.generator = generator
    
    async def human_move_to(self, driver, target_x: float, target_y: float):
        """Move mouse to target position with human-like movement"""
        # Get current mouse position (you'll need to implement this based on your setup)
        current_x, current_y = await self._get_current_mouse_position(driver)
        
        # Generate human-like path
        path = self.generator.generate_mouse_path((current_x, current_y), (target_x, target_y))
        
        # Execute the movement
        for i, (point, timing) in enumerate(zip(path.points, path.timings)):
            if i > 0:
                # Wait for the appropriate time
                await asyncio.sleep(timing - path.timings[i-1])
            
            # Move mouse to point
            await driver.mouse.move(point[0], point[1])
    
    async def human_type(self, driver, text: str, element=None):
        """Type text with human-like patterns"""
        if element:
            await element.click()
        
        # Generate typing sequence
        sequence = self.generator.generate_typing_sequence(text)
        
        # Execute typing
        for i, (key, timing) in enumerate(zip(sequence.keys, sequence.timings)):
            if i > 0:
                await asyncio.sleep(timing - sequence.timings[i-1])
            
            if key == 'Backspace':
                await driver.keyboard.press('Backspace')
            else:
                await driver.keyboard.type(key)
    
    async def human_scroll_to(self, driver, target_position: float):
        """Scroll to target position with human-like behavior"""
        # Get current scroll position
        current_position = await self._get_current_scroll_position(driver)
        
        # Generate scroll sequence
        sequence = self.generator.generate_scroll_sequence(target_position, current_position)
        
        # Execute scrolling
        for i, (delta, timing, pause) in enumerate(zip(sequence.scroll_deltas, sequence.timings, sequence.pauses)):
            if i > 0:
                await asyncio.sleep(timing - sequence.timings[i-1])
            
            # Perform scroll
            await driver.mouse.wheel(delta)
            
            # Add pause if specified
            if pause > 0:
                await asyncio.sleep(pause)
    
    async def _get_current_mouse_position(self, driver) -> Tuple[float, float]:
        """Get current mouse position - implement based on your driver setup"""
        # This is a placeholder - implement based on your nodriver setup
        return (0.0, 0.0)
    
    async def _get_current_scroll_position(self, driver) -> float:
        """Get current scroll position - implement based on your driver setup"""
        # This is a placeholder - implement based on your nodriver setup
        return 0.0

# Example usage
async def example_usage():
    """Example of how to use the human interaction generator"""
    
    # Initialize AI model and generator
    config = InteractionConfig()
    ai_model = HumanInteractionAI(config)
    ai_model.load_models()  # Load pre-trained models
    
    generator = HumanInteractionGenerator(ai_model)
    nodriver_integration = NoDriverIntegration(generator)
    
    # Example with nodriver (pseudo-code)
    # driver = await nodriver.start()
    # 
    # # Human-like mouse movement
    # await nodriver_integration.human_move_to(driver, 500, 300)
    # 
    # # Human-like typing
    # await nodriver_integration.human_type(driver, "Hello, this is human-like typing!")
    # 
    # # Human-like scrolling
    # await nodriver_integration.human_scroll_to(driver, 1000)

if __name__ == "__main__":
    # Test the generator without nodriver
    config = InteractionConfig()
    ai_model = HumanInteractionAI(config)
    generator = HumanInteractionGenerator(ai_model)
    
    # Generate sample interactions
    mouse_path = generator.generate_mouse_path((0, 0), (500, 300))
    print(f"Generated mouse path with {len(mouse_path.points)} points")
    
    typing_seq = generator.generate_typing_sequence("Hello World!")
    print(f"Generated typing sequence with {len(typing_seq.keys)} keystrokes")
    
    scroll_seq = generator.generate_scroll_sequence(1000, 0)
    print(f"Generated scroll sequence with {len(scroll_seq.scroll_deltas)} scroll events")