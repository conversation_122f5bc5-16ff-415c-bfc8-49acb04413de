import json
import os

def load_account_details(file_path="account.json"):
    """
    Loads account details from a JSON file.

    Args:
        file_path (str): The path to the account JSON file.

    Returns:
        dict: A dictionary containing the account details (email, password).
              Returns None if the file is not found or there's a JSON error.
    """
    # Construct the absolute path to the account.json file
    # Assuming account.json is in the project root directory
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    account_file_path = os.path.join(base_dir, file_path)

    if not os.path.exists(account_file_path):
        print(f"Error: Account file not found at {account_file_path}")
        return None

    try:
        with open(account_file_path, 'r') as f:
            account_details = json.load(f)
            # Basic validation
            if "email" not in account_details or "password" not in account_details:
                print("Error: account.json must contain 'email' and 'password' fields.")
                return None
            if account_details["email"] == "YOUR_EMAIL_HERE" or account_details["password"] == "YOUR_PASSWORD_HERE":
                 print("Warning: Please replace 'YOUR_EMAIL_HERE' and 'YOUR_PASSWORD_HERE' in account.json with your actual credentials.")
            return account_details
    except json.JSONDecodeError:
        print(f"Error: Could not decode JSON from {account_file_path}")
        return None
    except Exception as e:
        print(f"An unexpected error occurred while reading {account_file_path}: {e}")
        return None

if __name__ == '__main__':
    # Example usage (for testing)
    details = load_account_details()
    if details:
        print("Account details loaded successfully:")
        print(f"Email: {details.get('email')}")
        print(f"Password: {details.get('password')}") # Be cautious printing passwords