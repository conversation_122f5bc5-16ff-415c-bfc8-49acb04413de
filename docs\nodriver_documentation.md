# NoDriver Complete API Documentation

## Overview

NoDriver is a sophisticated asynchronous web scraping and browser automation library for Python designed to remain undetected by anti-bot systems. It is the official successor to the undetected-chromedriver package, eliminating dependencies on Selenium and ChromeDriver while providing superior stealth capabilities and performance[1][2].

## Installation

```bash
pip install nodriver
```

## Core Architecture

NoDriver operates using three primary classes that form a hierarchical structure:

1. **Browser** - The root object managing the browser process
2. **Tab** - Individual browser tabs/windows/iframes  
3. **Element** - DOM elements within pages
4. **Config** - Configuration management

## Browser Class

The Browser class is the root of the hierarchy and manages the browser process[3].

### Browser Creation and Management

#### `Browser.create()` (classmethod)
Creates and starts a new browser instance.

```python
import nodriver as uc

async def main():
    browser = await uc.start(
        headless=False,
        user_data_dir="/path/to/profile",
        browser_executable_path="/path/to/browser",
        browser_args=['--some-arg=value'],
        sandbox=True,
        host="127.0.0.1",
        port=9222
    )
```

**Parameters:**
- `headless` (bool): Run browser in headless mode (default: False)
- `user_data_dir` (PathLike): Custom user data directory 
- `browser_executable_path` (PathLike): Path to browser executable
- `browser_args` (List[str]): Additional browser arguments
- `sandbox` (bool): Enable/disable sandbox (default: True)
- `host` (str): Remote debugging host
- `port` (int): Remote debugging port

### Browser Properties

#### `browser.main_tab`
Returns the primary tab launched with the browser[3].

#### `browser.tabs`
Returns all current tabs of type "page"[3].

#### `browser.cookies`
Returns the CookieJar instance for cookie management[3].

#### `browser.stopped`
Boolean indicating if the browser process has stopped[3].

### Navigation Methods

#### `browser.get(url, new_tab=False, new_window=False)`
Top-level navigation method similar to Selenium's get()[3].

```python
# Navigate to URL in current tab
tab = await browser.get('https://example.com')

# Open in new tab
tab = await browser.get('https://example.com', new_tab=True)

# Open in new window  
tab = await browser.get('https://example.com', new_window=True)
```

#### `browser.create_context()`
Creates a new browser context with proxy support[3].

```python
tab = await browser.create_context(
    url="https://example.com",
    proxy_server="socks://user:pass@server:port",
    new_window=True
)
```

### Utility Methods

#### `browser.wait(time=0.1)` / `browser.sleep(time=0.1)`
Asynchronous wait/sleep function[3].

#### `browser.grant_all_permissions()`
Grants all browser permissions for enhanced functionality[3].

#### `browser.tile_windows(windows=None, max_columns=0)`
Automatically arranges browser windows in a grid layout[3].

#### `browser.stop()`
Terminates the browser process[3].

## Tab Class

The Tab class controls individual browser tabs, windows, or frames[4].

### Navigation and Page Control

#### `tab.get(url)`
Navigate to a specific URL.

```python
await tab.get('https://example.com')
```

#### `tab.reload()`
Reload the current page.

#### `tab.back()` / `tab.forward()`
Browser navigation controls.

#### `tab.close()`
Close the current tab.

### Element Selection Methods

#### `tab.find(text, best_match=False, timeout=10)`
Find element by text content[4].

```python
# Find first match
element = await tab.find("Click here")

# Find best match (more accurate but slower)
element = await tab.find("Click here", best_match=True)
```

#### `tab.find_all(text, timeout=10)`
Find all elements containing specified text.

#### `tab.select(selector, timeout=10)`
Find element using CSS selector[5].

```python
element = await tab.select("input[name='username']")
```

#### `tab.select_all(selector, timeout=10)`
Find all elements matching CSS selector.

#### `tab.xpath(xpath_expression)`
Find elements using XPath[5].

```python
elements = await tab.xpath('//button[contains(text(),"confirm")]')
```

### Page Interaction Methods

#### `tab.scroll_down(pixels)` / `tab.scroll_up(pixels)`
Scroll the page vertically[6].

```python
await tab.scroll_down(150)
```

#### `tab.evaluate(javascript_code)`
Execute JavaScript code[7].

```python
result = await tab.evaluate("document.title")
await tab.evaluate("window.scrollTo(0, 500)")
```

#### `tab.send(cdp_command)`
Send Chrome DevTools Protocol commands[4].

```python
await tab.send(cdp.page.navigate(url='https://example.com'))
```

### Content Methods

#### `tab.get_content()`
Get the HTML content of the page[6].

#### `tab.save_screenshot(filename="auto", format="jpeg")`
Capture page screenshot[6].

```python
await tab.save_screenshot("page.png", format="png")
```

### Mouse and Input Methods

#### `tab.mouse_click(x, y)`
Click at specific coordinates.

#### `tab.mouse_move(x, y)`
Move mouse to coordinates.

#### `tab.mouse_drag(start_point, end_point)`
Drag from one point to another.

### Event Handling

#### `tab.add_handler(event_type, handler_function)`
Add event handlers for CDP events[8].

```python
async def response_handler(event):
    print(f"Response: {event.response}")

tab.add_handler(cdp.network.ResponseReceived, response_handler)
```

### Network and Storage

#### `tab.get_cookies()`
Get cookies for the current page.

#### `tab.set_cookies(cookies)`
Set cookies for the page.

#### `tab.get_local_storage()`
Access local storage data.

## Element Class

The Element class represents DOM elements and provides interaction methods[9].

### Element Properties

#### Core Properties
- `element.tag` / `element.tag_name`: HTML tag name
- `element.text`: Text content of element
- `element.text_all`: All text including children
- `element.attrs`: Element attributes
- `element.node_id`: Chrome DevTools node ID
- `element.parent`: Parent element
- `element.children`: Child elements

### Interaction Methods

#### `element.click()`
Click the element[9].

```python
await element.click()
```

#### `element.send_keys(text)`
Send text to input fields[9].

```python
await element.send_keys("Hello World")
```

#### `element.clear_input()`
Clear input field content[9].

#### `element.focus()`
Focus the element[9].

#### `element.select_option()`
Select an option element in select fields[9].

### Advanced Interaction

#### `element.mouse_click(button="left")` 
Native mouse click with button specification[9].

#### `element.mouse_move()`
Move mouse to element position[9].

#### `element.mouse_drag(destination, relative=False, steps=1)`
Drag element to destination[9].

#### `element.scroll_into_view()`
Scroll element into viewport[9].

### Form Methods

#### `element.send_file(*file_paths)`
Upload files to file input elements[9].

```python
await element.send_file('/path/to/file1.txt', '/path/to/file2.pdf')
```

#### `element.set_value(value)`
Set element value directly[9].

#### `element.set_text(value)`
Set text content[9].

### JavaScript Execution

#### `element.apply(javascript_function, return_by_value=True)`
Execute JavaScript on the element[9].

```python
result = await element.apply("(elem) => elem.value")
await element.apply("(elem) => elem.style.backgroundColor = 'red'")
```

#### `element(js_method)`
Call JavaScript methods on the element[9].

```python
await video_element('play')  # Calls video.play()
```

### Utility Methods

#### `element.get_position(abs=False)`
Get element position and dimensions[9].

#### `element.save_screenshot(filename="auto", format="jpeg")`
Screenshot just the element[9].

#### `element.flash(duration=0.5)`
Visually highlight the element[9].

#### `element.highlight_overlay()`
Toggle DevTools-style highlighting[9].

#### `element.get_html()`
Get element's outer HTML[9].

### Element Selection

#### `element.query_selector(selector)`
Find child element by CSS selector[9].

#### `element.query_selector_all(selector)`
Find all child elements by CSS selector[9].

## Config Class

The Config class manages browser configuration and startup parameters[10].

### Configuration Options

```python
config = Config(
    user_data_dir="/path/to/profile",
    headless=False,
    browser_executable_path="/path/to/browser",
    browser_args=['--disable-web-security'],
    sandbox=True,
    lang="en-US",
    host="127.0.0.1",
    port=9222,
    expert=False
)
```

### Configuration Methods

#### `config.add_argument(arg)`
Add custom browser arguments[10].

```python
config.add_argument('--disable-notifications')
```

#### `config.add_extension(extension_path)`
Load browser extensions[10].

```python
config.add_extension('/path/to/extension')
```

## Cookie Management

The CookieJar class provides comprehensive cookie management[3].

### Cookie Methods

#### `cookies.get_all(requests_cookie_format=False)`
Get all cookies, optionally in requests library format[3].

#### `cookies.set_all(cookies)`
Set multiple cookies[3].

#### `cookies.save(file=".session.dat", pattern=".*")`
Save cookies to file with regex pattern filtering[3].

#### `cookies.load(file=".session.dat", pattern=".*")`
Load cookies from file[3].

#### `cookies.clear()`
Clear all cookies[3].

## CDP (Chrome DevTools Protocol) Integration

NoDriver provides full access to Chrome DevTools Protocol for advanced functionality[8][4].

### Common CDP Usage

```python
# Network monitoring
await tab.send(cdp.network.enable())

# Page events
await tab.send(cdp.page.enable())

# Get response body
response_body = await tab.send(cdp.network.get_response_body(request_id))

# Set download behavior  
await tab.send(cdp.browser.set_download_behavior("allow", download_path="/downloads"))
```

## Practical Examples

### Basic Usage

```python
import nodriver as uc

async def main():
    browser = await uc.start()
    tab = await browser.get('https://example.com')
    
    # Find and click element
    button = await tab.find("Click me")
    await button.click()
    
    # Fill form
    input_field = await tab.select("input[name='username']")
    await input_field.send_keys("myusername")
    
    # Take screenshot
    await tab.save_screenshot()
    
    # Get page content
    content = await tab.get_content()
    
    browser.stop()

if __name__ == '__main__':
    uc.loop().run_until_complete(main())
```

### Session Management with Cookies

```python
async def login_with_cookies():
    browser = await uc.start()
    tab = await browser.get('https://example.com')
    
    # Try to load existing session
    try:
        await browser.cookies.load('.session.dat')
        await tab.reload()
        print("Loaded existing session")
    except FileNotFoundError:
        # Login manually
        username = await tab.select("input[name='username']")
        password = await tab.select("input[name='password']")
        
        await username.send_keys("myuser")
        await password.send_keys("mypass")
        
        login_btn = await tab.find("Login")
        await login_btn.click()
        
        # Save session
        await browser.cookies.save('.session.dat')
        print("Saved new session")
```

### Network Monitoring

```python
async def monitor_network():
    browser = await uc.start()
    tab = await browser.get('data:,')  # Blank page
    
    # Enable network monitoring
    await tab.send(cdp.network.enable())
    
    # Add response handler
    async def response_handler(event):
        print(f"Response URL: {event.response.url}")
        if 'api' in event.response.url:
            try:
                body = await tab.send(
                    cdp.network.get_response_body(event.request_id)
                )
                print(f"API Response: {body}")
            except:
                pass
    
    tab.add_handler(cdp.network.ResponseReceived, response_handler)
    
    # Navigate to target page
    await tab.get('https://example.com')
```

### File Upload

```python
async def upload_files():
    browser = await uc.start()
    tab = await browser.get('https://example.com/upload')
    
    file_input = await tab.select("input[type='file']")
    await file_input.send_file('/path/to/file1.txt', '/path/to/file2.pdf')
    
    upload_btn = await tab.find("Upload")
    await upload_btn.click()
```

### Advanced Element Interaction

```python
async def advanced_interactions():
    browser = await uc.start()
    tab = await browser.get('https://example.com')
    
    # Drag and drop
    source = await tab.select("#draggable")
    target = await tab.select("#droppable")
    await source.mouse_drag(target)
    
    # Hover effect
    menu = await tab.select(".menu-item")
    await menu.mouse_move()
    
    # Custom JavaScript
    result = await tab.evaluate("""
        return {
            title: document.title,
            url: window.location.href,
            userAgent: navigator.userAgent
        }
    """)
    
    # Element-specific JavaScript
    element = await tab.select("#my-element")
    await element.apply("(el) => el.style.backgroundColor = 'yellow'")
```

## Best Practices

### Error Handling

```python
async def robust_automation():
    browser = await uc.start()
    try:
        tab = await browser.get('https://example.com')
        
        # Wait for elements with timeout
        try:
            button = await tab.find("Submit", timeout=10)
            await button.click()
        except asyncio.TimeoutError:
            print("Button not found within timeout")
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        browser.stop()
```

### Performance Optimization

```python
# Use best_match only when necessary (it's slower)
element = await tab.find("text", best_match=False)

# Update elements in batch when possible
await element.update()  # Only call when needed

# Use appropriate timeouts
quick_element = await tab.find("text", timeout=2)
slow_element = await tab.find("complex text", timeout=30)
```

## Key Features and Advantages

1. **Stealth Capabilities**: Designed to bypass sophisticated anti-bot systems[1][2][11]
2. **No WebDriver Dependencies**: Direct browser communication via CDP[1][2]
3. **Asynchronous Architecture**: Fully async for better performance[1][2]
4. **Iframe Support**: Seamless interaction with iframe content[1][12]
5. **Cookie Persistence**: Built-in session management[13]
6. **Proxy Support**: Including SOCKS5 with authentication[3]
7. **File Upload Support**: Native file upload capabilities[9]
8. **Network Monitoring**: Full network request/response monitoring[8]
9. **Cross-Platform**: Works on Windows, macOS, and Linux[1]

NoDriver provides a comprehensive, modern approach to web automation with superior stealth capabilities and performance compared to traditional Selenium-based solutions.


Sources For Documentation:
[1] https://github.com/ultrafunkamsterdam/nodriver
[2] https://www.zenrows.com/blog/nodriver
[3] https://github.com/ultrafunkamsterdam/nodriver/blob/main/nodriver/core/browser.py
[4] https://github.com/ultrafunkamsterdam/undetected-chromedriver/discussions/2159
[5] https://zetcode.com/dom/element-click/
[6] https://4each.com.br/threads/python-getting-a-http-response-with-nodriver.48677/
[7] https://github.com/ultrafunkamsterdam/undetected-chromedriver/discussions/1798
[8] https://stackoverflow.com/questions/79472592/how-to-search-with-xpath-selector-in-nodriver-on-python
[9] https://stackoverflow.com/questions/62986339/how-can-i-call-a-class-without-passing-driver
[10] https://ultrafunkamsterdam.github.io/nodriver/_modules/nodriver/core/element.html
[11] https://www.scrapingbee.com/blog/nodriver-tutorial/
[12] https://pypi.org/project/nodriver/
[13] https://ultrafunkamsterdam.github.io/nodriver/_modules/nodriver/core/browser.html
[14] https://www.piwheels.org/project/nodriver/
[15] https://www.piloterr.com/blog/nodriver-open-source-advanced-browser-control
[16] https://deepwiki.com/ultrafunkamsterdam/nodriver/5.3-browser-automation-examples
[17] https://github.com/Ehsan-U/scrapy-nodriver
[18] https://blog.csdn.net/lwdfzr/article/details/140958590
[19] https://gridpanel.net/blog/python-web-automation-with-nodriver
[20] https://stabler.tech/blog/nodriver-a-new-webscraping-tool
[21] https://github.com/ultrafunkamsterdam/nodriver/blob/main/generate_cdp.py
[22] https://brightdata.com/blog/web-data/nodriver-web-scraping
[23] https://eodag.readthedocs.io/en/latest/drivers_generated/eodag.api.product.drivers.base.NoDriver.html
[24] https://github.com/ultrafunkamsterdam/nodriver/issues
[25] https://stackoverflow.com/questions/28431765/open-web-in-new-tab-selenium-python
[26] https://github.com/Niminem/ChromeDevToolsProtocol
[27] https://github.com/ultrafunkamsterdam/nodriver/blob/main/nodriver/cdp/input_.py
[28] https://libraries.io/pypi/nodriver-extras
[29] https://ultrafunkamsterdam.github.io/nodriver/_modules/nodriver/core/config.html
[30] https://github.com/hanks/pystructure
[31] https://github.com/ultrafunkamsterdam/undetected-chromedriver/issues/1788
[32] https://ultrafunkamsterdam.github.io/nodriver/nodriver/quickstart.html
[33] https://www.reddit.com/r/webscraping/comments/1gnpxyd/nodriver_not_navigating_to_url_when_running/
[34] https://ultrafunkamsterdam.github.io/nodriver/readme.html
[35] https://stackoverflow.com/questions/78700829/how-so-save-a-session-with-nodriver-python
[36] https://apify.com/getdataforme/platform-nodriver/api/python
[37] https://www.askpython.com/python/examples/find-all-methods-of-class
[38] https://github.com/UltrafunkAmsterdam/nodriver
[39] https://stackoverflow.com/questions/78813056/how-can-i-execute-javascript-with-nodriver-python
[40] https://ultrafunkamsterdam.github.io/nodriver/nodriver/classes/tab.html
[41] https://python.plainenglish.io/nodriver-browser-automation-without-the-browser-3c7e58bfcb51?gi=1ff2922d9e79
[42] https://blog.castle.io/from-puppeteer-stealth-to-nodriver-how-anti-detect-frameworks-evolved-to-evade-bot-detection/