import asyncio
import json
import nodriver as uc # Import nodriver
from datetime import datetime
from .logger import logger
from .browser_actions import BrowserActions # Import BrowserActions

class UserDataExtractor:
    def __init__(self, tab, email, browser_actions: BrowserActions): # Add browser_actions parameter
        self.tab = tab
        self.email = email
        self.browser_actions = browser_actions # Store browser_actions instance
        logger.debug(f"UserDataExtractor initialized for {self.email}", extra={'email': self.email})

    async def _extract_variable_data(self, var_name):
        """
        Executes JavaScript to get the value of a global variable.
        """
        script = f"(function() {{ return typeof {var_name} !== 'undefined' ? {var_name} : null; }})();"
        try:
            logger.debug(f"Executing JavaScript to extract '{var_name}': {script}", extra={'email': self.email})
            data = await self.tab.evaluate(script)
            if isinstance(data, uc.cdp.runtime.ExceptionDetails):
                logger.error(f"JavaScript evaluation for variable '{var_name}' returned an exception: {data.exception.description}", extra={'email': self.email})
                return None
            else:
                logger.debug(f"Successfully extracted raw data for '{var_name}': {data}", extra={'email': self.email})
                return data
        except Exception as e:
            logger.error(f"Failed to evaluate JavaScript for variable '{var_name}': {e}", extra={'email': self.email})
            logger.debug(f"Error details for '{var_name}' extraction: {e}", extra={'email': self.email})
            return None

    async def get_data(self, data_keys):
        """
        Parses the extracted dashboard data or user info to get specific values,
        depending on the current URL.

        Args:
            data_keys (list): A list of strings representing the data to retrieve
                               (e.g., ["available_points", "level"]).

        Returns:
            dict: A dictionary containing the requested data keys and their values,
                  or None if the data could not be found.
        """
        logger.debug(f"Attempting to get data for keys: {data_keys} for {self.email}", extra={'email': self.email})
        
        current_url = self.tab.url
        extracted_data = {}

        if "rewards.bing.com" in current_url:
            logger.info(f"Current URL is rewards.bing.com. Extracting dashboard data for {self.email}.", extra={'email': self.email})
            await asyncio.sleep(2) # Add a small delay before extraction
            dashboard_data = await self._extract_variable_data("dashboard")

            if not dashboard_data:
                logger.warning(f"Dashboard data not available for {self.email}. Cannot retrieve specific data.", extra={'email': self.email})
                return None

            logger.debug(f"Raw dashboard data received for parsing: {dashboard_data}", extra={'email': self.email})
            for key in data_keys:
                logger.debug(f"Processing data key: {key}", extra={'email': self.email})
                if key == "available_points":
                    value = self._find_value_in_json(dashboard_data, ["userStatus", "value", "availablePoints", "value"])
                    extracted_data["available_points"] = value
                    logger.debug(f"Extracted available_points: {value}", extra={'email': self.email})
                elif key == "level":
                    value = self._find_value_in_json(dashboard_data, ["userStatus", "value", "levelInfo", "value", "activeLevelName", "value"])
                    extracted_data["level"] = value
                    logger.debug(f"Extracted level: {value}", extra={'email': self.email})
                elif key == "today_points":
                    value = self._find_value_in_json(dashboard_data, ["userStatus", "value", "counters", "value", "dailyPoint", "value", 0, "value", "pointProgress", "value"])
                    extracted_data["today_points"] = value
                    logger.debug(f"Extracted today_points: {value}", extra={'email': self.email})
                elif key == "remaining-pc-searches":
                    complete_status = self._find_value_in_json(dashboard_data, ["userStatus", "value", "counters", "value", "pcSearch", "value", 0, "value", "complete", "value"])
                    max_points = self._find_value_in_json(dashboard_data, ["userStatus", "value", "counters", "value", "pcSearch", "value", 0, "value", "pointProgressMax", "value"])
                    current_points = self._find_value_in_json(dashboard_data, ["userStatus", "value", "counters", "value", "pcSearch", "value", 0, "value", "pointProgress", "value"])

                    if complete_status is True or (max_points is not None and current_points is not None and max_points == current_points):
                        remaining_searches = 0
                    elif max_points is not None and current_points is not None:
                        remaining_points = max_points - current_points
                        remaining_searches = remaining_points // 3
                    else:
                        remaining_searches = None
                        logger.warning(f"Could not determine PC search progress for rewards.bing.com for {self.email}.", extra={'email': self.email})
                    extracted_data["remaining-pc-searches"] = remaining_searches
                    logger.debug(f"Extracted remaining-pc-searches: {remaining_searches}", extra={'email': self.email})
                else:
                    logger.warning(f"Unknown data key requested: {key}", extra={'email': self.email})
                    extracted_data[key] = None

        elif current_url.startswith("https://www.bing.com"):
            logger.info(f"Current URL is bing.com. Extracting user info via flyout for {self.email}.", extra={'email': self.email})
            user_info_data = await self._extract_bing_com_user_info()

            if not user_info_data:
                logger.warning(f"User info not available from bing.com flyout for {self.email}. Cannot retrieve specific data.", extra={'email': self.email})
                return None
            
            logger.debug(f"Raw user info data received for parsing: {user_info_data}", extra={'email': self.email})
            for key in data_keys:
                logger.debug(f"Processing data key: {key}", extra={'email': self.email})
                if key == "available_points":
                    value = self._find_value_in_json(user_info_data, ["userInfo", "balance"])
                    extracted_data["available_points"] = value
                    logger.debug(f"Extracted available_points: {value}", extra={'email': self.email})
                elif key == "level":
                    level_str = self._find_value_in_json(user_info_data, ["userInfo", "profile", "attributes", "level"])
                    level_num = int("".join(filter(str.isdigit, level_str))) if level_str else None
                    extracted_data["level"] = level_num
                    logger.debug(f"Extracted level: {level_num}", extra={'email': self.email})
                elif key == "today_points":
                    value = self._find_value_in_json(user_info_data, ["flyoutResult", "userStatus", "counters", "DailyPoint", 0, "pointProgress"])
                    extracted_data["today_points"] = value
                    logger.debug(f"Extracted today_points: {value}", extra={'email': self.email})
                elif key == "remaining-pc-searches":
                    pc_search_promo = self._find_promotion_by_tag(user_info_data.get("userInfo", {}).get("promotions", []), "Classification.Tag", "PCSearch")
                    if pc_search_promo:
                        complete_status_str = self._find_value_in_json(pc_search_promo, ["attributes", "complete"])
                        max_points_str = self._find_value_in_json(pc_search_promo, ["attributes", "max"])
                        current_points_str = self._find_value_in_json(pc_search_promo, ["attributes", "progress"])

                        complete_status = complete_status_str.lower() == 'true' if isinstance(complete_status_str, str) else False
                        max_points = int(max_points_str) if max_points_str is not None else 0
                        current_points = int(current_points_str) if current_points_str is not None else 0

                        if complete_status or max_points == current_points:
                            remaining_searches = 0
                        else:
                            remaining_points = max_points - current_points
                            remaining_searches = remaining_points // 3
                        extracted_data["remaining-pc-searches"] = remaining_searches
                        logger.debug(f"Extracted remaining-pc-searches: {remaining_searches}", extra={'email': self.email})
                    else:
                        logger.warning(f"PC search promotion not found in bing.com user info for {self.email}.", extra={'email': self.email})
                        extracted_data["remaining-pc-searches"] = None
                else:
                    logger.warning(f"Unknown data key requested: {key}", extra={'email': self.email})
                    extracted_data[key] = None
        else:
            logger.warning(f"Unsupported URL for data extraction: {current_url} for {self.email}. Returning None.", extra={'email': self.email})
            return None

        logger.debug(f"Finished extracting data. Result: {extracted_data}", extra={'email': self.email})
        return extracted_data
    
    async def _extract_bing_com_user_info(self):
        """
        Handles the specific logic for extracting user info when on bing.com.
        Clicks the rewards medallion, waits for points to show, and extracts
        userInfo from the script tag.
        """
        logger.debug(f"Starting _extract_bing_com_user_info for {self.email}", extra={'email': self.email})

        rewards_medallion_selector = 'a#id_rh_w .b_clickarea' # Updated selector to target b_clickarea

        # 1. Initial click (double click for the very first attempt)
        logger.info(f"Performing initial click(s) on Microsoft Rewards medallion's click area: {rewards_medallion_selector}", extra={'email': self.email})
        
        # Perform initial click
        initial_click_success = await self.browser_actions.click(rewards_medallion_selector, timeout=10)
        if not initial_click_success:
            logger.error(f"Failed initial click on Microsoft Rewards medallion's click area '{rewards_medallion_selector}' for {self.email}.", extra={'email': self.email})
            return None
        
        await asyncio.sleep(1.5) # Wait for 1.5 seconds before first attempt

        # 2. Loop to extract userInfo from script tag with 5-second delay until 30 seconds
        start_time = asyncio.get_event_loop().time()
        user_info_data = None
        while asyncio.get_event_loop().time() - start_time < 30: # Max 30 seconds total
            logger.info(f"Attempting to extract userInfo from script tag for {self.email} (retry loop).", extra={'email': self.email})
            user_info_data = await self._extract_user_info_from_script() # Call the modified method
            
            if user_info_data:
                logger.debug(f"Successfully extracted user info from bing.com flyout for {self.email}", extra={'email': self.email})
                return user_info_data # Data found, exit loop and return
            else:
                logger.debug(f"User info not yet available in script. Waiting 5 seconds before retrying...", extra={'email': self.email})
                await asyncio.sleep(5) # Wait for 5 seconds before next attempt

        logger.error(f"Failed to extract user info from script tag after 30 seconds for {self.email}.", extra={'email': self.email})
        return None

    async def _extract_user_info_from_script(self):
        """
        Executes JavaScript to get the value of window.flyoutViewModel.userInfo from a script tag within an iframe.
        Uses the same logic as the working console script that directly accesses iframe content.
        """
        logger.debug(f"Attempting to extract 'window.flyoutViewModel.userInfo' from iframe for {self.email} using direct iframe access.", extra={'email': self.email})

        try:
            # JavaScript that replicates the working console code logic
            script_to_evaluate = """
                (function() {
                    console.log("=== Starting flyoutViewModel extraction ===");

                    // Method 1: Try to access iframes directly from main page
                    const iframes = document.querySelectorAll('iframe');
                    console.log(`Found ${iframes.length} iframe(s)`);

                    for (let i = 0; i < iframes.length; i++) {
                        const iframe = iframes[i];
                        console.log(`Checking iframe ${i + 1}: ${iframe.src}`);

                        try {
                            // Try to access iframe content
                            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                            const iframeWindow = iframe.contentWindow;

                            if (iframeWindow && iframeWindow.flyoutViewModel) {
                                console.log("✅ Found flyoutViewModel in iframe window!");
                                if (iframeWindow.flyoutViewModel) {
                                    return JSON.stringify(iframeWindow.flyoutViewModel);
                                }
                            }

                            // Search script tags within iframe
                            if (iframeDoc) {
                                const scriptTags = iframeDoc.querySelectorAll('script');
                                console.log(`- Found ${scriptTags.length} script tags in iframe`);
                                
                                for (let script of scriptTags) {
                                    const scriptContent = script.textContent || script.innerHTML;
                                    
                                    if (scriptContent.includes('flyoutViewModel')) {
                                        console.log("✅ Found script with flyoutViewModel in iframe!");
                                        
                                        // Try to extract the complete flyoutViewModel object
                                        const match = scriptContent.match(/window\\.flyoutViewModel\\s*=\\s*({[\\s\\S]*?)(?=\\s*<\\/script>|\\s*$)/);
                                        if (match) {
                                            try {
                                                // Clean up the JSON string
                                                let jsonStr = match[1].trim();
                                                
                                                // Handle cases where the object might be incomplete due to truncation
                                                if (!jsonStr.endsWith('}')) {
                                                    console.log("⚠️ JSON appears truncated, attempting to extract what we can");
                                                }
                                                
                                                // Try to parse as-is first
                                                let flyoutData;
                                                try {
                                                    flyoutData = JSON.parse(jsonStr);
                                                } catch (e) {
                                                    // If that fails, try to extract just the userInfo part
                                                    const userInfoMatch = jsonStr.match(/"userInfo":\\s*({[^}]*})/);
                                                    if (userInfoMatch) {
                                                        console.log("Extracted userInfo portion:", userInfoMatch[1]);
                                                        flyoutData = { userInfo: JSON.parse(userInfoMatch[1]) };
                                                    } else {
                                                        throw e;
                                                    }
                                                }
                                                
                                                if (flyoutData) {
                                                    console.log("✅ Successfully extracted flyoutViewModel from script parsing");
                                                    return JSON.stringify(flyoutData);
                                                }
                                                
                                            } catch (parseError) {
                                                console.log("❌ Error parsing JSON:", parseError);
                                            }
                                        }
                                    }
                                }
                            }
                            
                        } catch (e) {
                            console.log(`❌ Cannot access iframe ${i + 1} content:`, e.message);
                        }
                    }
                    
                    // Method 2: Try to find specific rewards panel iframe
                    console.log("=== Looking for rewards panel specifically ===");
                    const rewardsIframe = document.querySelector('iframe[src*="rewards/panelflyout"]');
                    if (rewardsIframe) {
                        console.log("Found rewards panel iframe:", rewardsIframe.src);
                        try {
                            const iframeWindow = rewardsIframe.contentWindow;
                            if (iframeWindow && iframeWindow.flyoutViewModel) {
                                console.log("✅ Found flyoutViewModel in rewards iframe!");
                                return JSON.stringify(iframeWindow.flyoutViewModel);
                            }
                        } catch (e) {
                            console.log("❌ Cannot access rewards iframe:", e.message);
                        }
                    }
                    
                    // Method 3: Check if we're currently inside the iframe
                    console.log("=== Checking if we're inside the iframe ===");
                    if (window.location.href.includes('rewards/panelflyout')) {
                        console.log("We are inside the rewards iframe!");
                        
                        // Check global variable
                        if (window.flyoutViewModel) {
                            console.log("✅ Found flyoutViewModel in current window:");
                            return JSON.stringify(window.flyoutViewModel);
                        }
                        
                        // Search script tags in current context
                        const scriptTags = document.querySelectorAll('script');
                        console.log(`Found ${scriptTags.length} script tags in current context`);
                        
                        for (let i = 0; i < scriptTags.length; i++) {
                            const script = scriptTags[i];
                            const content = script.textContent || script.innerHTML;
                            
                            if (content.includes('flyoutViewModel')) {
                                console.log(`✅ Found flyoutViewModel in script ${i + 1}`);
                                
                                // Try to execute the script to populate the variable
                                try {
                                    eval(content);
                                    if (window.flyoutViewModel) {
                                        console.log("✅ Successfully executed script, flyoutViewModel now available");
                                        return JSON.stringify(window.flyoutViewModel);
                                    }
                                } catch (e) {
                                    console.log("❌ Error executing script:", e);
                                }
                                
                                // Try direct parsing if execution failed
                                const match = content.match(/window\\.flyoutViewModel\\s*=\\s*({[\\s\\S]*?)(?=\\s*<\\/script>|\\s*$)/);
                                if (match) {
                                    try {
                                        let jsonStr = match[1].trim();
                                        let flyoutData;
                                        try {
                                            flyoutData = JSON.parse(jsonStr);
                                        } catch (e) {
                                            const userInfoMatch = jsonStr.match(/"userInfo":\\s*({[^}]*})/);
                                            if (userInfoMatch) {
                                                flyoutData = { userInfo: JSON.parse(userInfoMatch[1]) };
                                            } else {
                                                throw e;
                                            }
                                        }
                                        if (flyoutData) {
                                            return JSON.stringify(flyoutData);
                                        }
                                    } catch (parseError) {
                                        console.log("Error parsing flyoutViewModel JSON:", parseError);
                                    }
                                }
                            }
                        }
                    }
                    
                    console.log("❌ Could not find flyoutViewModel.userInfo data");
                    return null;
                })();
            """
            
            # Execute the script in the main page context
            logger.debug(f"Executing JavaScript to extract userInfo for {self.email}", extra={'email': self.email})
            
            # Use the tab's evaluate method directly instead of CDP
            result = await self.tab.evaluate(script_to_evaluate)
            
            if result:
                try:
                    user_info = json.loads(result)
                    logger.debug(f"Successfully extracted userInfo for {self.email}: {user_info}", extra={'email': self.email})
                    return user_info
                except json.JSONDecodeError as e:
                    logger.warning(f"Failed to decode userInfo JSON for {self.email}: {e}. Raw data: {result}", extra={'email': self.email})
                    return None
            else:
                logger.debug(f"No userInfo data found for {self.email}", extra={'email': self.email})
                return None
                
        except Exception as e:
            logger.error(f"Error extracting userInfo for {self.email}: {e}", extra={'email': self.email})
            return None
    
        def _find_promotion_by_tag(self, promotions_list, tag_name, tag_value):
            """
            Finds a promotion dictionary within a list of promotions based on a tag.
            """
            if not promotions_list:
                return None
            for promo in promotions_list:
                if isinstance(promo, dict) and "attributes" in promo and tag_name in promo["attributes"]:
                    if promo["attributes"][tag_name] == tag_value:
                        return promo
            return None
    
    def _find_value_in_json(self, data, path):
        """Recursively finds a value in a nested JSON structure based on a path."""
        if not data or not path:
            # If data is empty or path is exhausted, return None or the data itself if it's the final value
            if not path and data is not None: # If path is empty, and we have data, that's our value
                logger.debug(f"Path exhausted, returning final data: {data}", extra={'email': self.email})
                return data
            return None
        logger.debug(f"Searching for path: {path} in data type: {type(data)}", extra={'email': self.email})

        current_key = path[0]
        remaining_path = path[1:]

        if isinstance(data, list):
            logger.debug(f"Current data is a list. Looking for key/index: {current_key}", extra={'email': self.email})
            if isinstance(current_key, int): # Check if the key is an integer (index)
                if 0 <= current_key < len(data):
                    logger.debug(f"Found index '{current_key}'. Remaining path: {remaining_path}", extra={'email': self.email})
                    return self._find_value_in_json(data[current_key], remaining_path)
                else:
                    logger.debug(f"Index '{current_key}' out of bounds for list.", extra={'email': self.email})
                    return None
            else: # Original logic for key-value pairs in a list
                for item in data:
                    if isinstance(item, list) and len(item) > 0 and item[0] == current_key:
                        logger.debug(f"Found item with key '{current_key}'. Remaining path: {remaining_path}", extra={'email': self.email})
                        return self._find_value_in_json(item[1], remaining_path)
        elif isinstance(data, dict):
            logger.debug(f"Current data is a dict. Looking for key: {current_key}", extra={'email': self.email})
            if current_key in data:
                logger.debug(f"Found key '{current_key}'. Remaining path: {remaining_path}", extra={'email': self.email})
                return self._find_value_in_json(data[current_key], remaining_path)

        return None
