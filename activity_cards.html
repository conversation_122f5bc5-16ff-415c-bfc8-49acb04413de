<completed_activity>
    <mee-card ng-if="$ctrl.cardItems.length &gt; 4" display-type="single" ng-disabled="$ctrl.disabled" role="listitem"
        aria-posinset="5" aria-setsize="9"
        class="ng-scope ng-isolate-scope c-card f-single"><!-- ngIf: $ctrl.hasHeader() -->
        <div class="c-card-content" ng-transclude="content"><card-content class="ng-scope">
                <mee-rewards-more-activities-card-item item="$ctrl.cardItems[4]" cms-data="$ctrl.cmsData" index="4"
                    class="ng-isolate-scope">
                    <div class="text-align-center rewards-card-container" mee-rewardable=""
                        data-bi-id="ENstar_Rewards_DailyGlobalOffer_Evergreen_Monday"
                        data-m="{&quot;cN&quot;:&quot;Complete this puzzle&quot;,&quot;id&quot;:&quot;enstar_rewards_dailyglobaloffer_evergreen_monday&quot;,&quot;sN&quot;:1,&quot;aN&quot;:&quot;Rewards.Dashboard.MoreActivities&quot;,&quot;cT&quot;:&quot;Promotion&quot;}"
                        ng-class="{'locked-card' : item.exclusiveLockedFeatureCardStatus == 'locked'}">

                        <!-- ngIf: item.sameTabNoRefresh -->

                        <!-- ngIf: !item.sameTabNoRefresh --><a class="ds-card-sec ng-scope"
                            ng-if="!item.sameTabNoRefresh"
                            ng-href="https://www.bing.com/spotlight/imagepuzzle?form=ML2BF0&amp;OCID=ML2BF0&amp;PUBL=RewardsDO&amp;PROGRAMNAME=BingDailyOfferIN&amp;CREA=ML2BF0"
                            ng-click="$ctrl.onCardClick($event)" target="true" aria-disabled="false"
                            aria-labelledby="card-title- card-desc- card-action-"
                            aria-label="Complete this puzzle   Arrange the tiles to reveal the image   5 Points   Activities section 5"
                            href="https://www.bing.com/spotlight/imagepuzzle?form=ML2BF0&amp;OCID=ML2BF0&amp;PUBL=RewardsDO&amp;PROGRAMNAME=BingDailyOfferIN&amp;CREA=ML2BF0">

                            <!-- ngIf: item.displayAnidHexaCode -->

                            <mee-rewards-points complete="item.complete" is-in-progress="item.isInProgress"
                                points="item.points" cms-data="$ctrl.cmsData" show-progress="$ctrl.item.isWot"
                                point-progress="$ctrl.item.pointProgress"
                                is-exclusive-locked-item="item.exclusiveLockedFeatureCardStatus == 'locked'"
                                ng-mouseover="$ctrl.onHoverPoints($event)" ng-class="{inprogress : item.isInProgress}"
                                class="ng-isolate-scope">
                                <div class="points clearfix">
                                    <!-- ngIf: !$ctrl.locked && !$ctrl.isExclusiveLockedItem -->
                                    <div ng-if="!$ctrl.locked &amp;&amp; !$ctrl.isExclusiveLockedItem" class="ng-scope">
                                        <!-- ngIf: $ctrl.iconImage -->
                                        <!-- ngIf: !$ctrl.iconImage && $ctrl.iconName --><span
                                            class="mee-icon mee-icon-SkypeCircleCheck"
                                            ng-if="!$ctrl.iconImage &amp;&amp; $ctrl.iconName"
                                            aria-label="Points you have earned"
                                            role="img"></span><!-- end ngIf: !$ctrl.iconImage && $ctrl.iconName -->
                                        <!-- ngIf: $ctrl.pointsString --><span mee-heading="heading"
                                            ng-if="$ctrl.pointsString"
                                            class="c-heading pointsString ng-binding ng-scope">5</span><!-- end ngIf: $ctrl.pointsString -->
                                    </div><!-- end ngIf: !$ctrl.locked && !$ctrl.isExclusiveLockedItem -->
                                    <!-- ngIf: !$ctrl.locked && $ctrl.isExclusiveLockedItem -->
                                    <!-- ngIf: $ctrl.locked -->
                                </div>
                            </mee-rewards-points>

                            <!-- ngIf: $ctrl.isActivityInProgress -->

                            <div class="imageContainer"
                                ng-class="{'x-hidden-vp1' : item.displayType === 'single' &amp;&amp; !$ctrl.areaConfig.isMobileViewModeEnabled}">
                                <!-- ngIf: item.icon.source --><mee-rewards-animation data="item.icon"
                                    ng-if="item.icon.source"
                                    is-exclusive-locked-item="item.exclusiveLockedFeatureCardStatus == 'locked'"
                                    class="ng-scope ng-isolate-scope">
                                    <picture class="rewards-animation-container">
                                        <!-- ngIf: !$ctrl.isAnimation -->
                                        <!-- ngIf: $ctrl.isAnimation -->
                                        <div ng-if="$ctrl.isAnimation" class="rewardsBodymovin ng-scope"><svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 300 300"
                                                class="rewards-svg-renderer" preserveAspectRatio="xMidYMid meet"
                                                aria-hidden="true" focusable="false">
                                                <defs>
                                                    <clipPath id="__lottie_element_650">
                                                        <rect width="300" height="300" x="0" y="0"></rect>
                                                    </clipPath>
                                                </defs>

                                            </svg></div><!-- end ngIf: $ctrl.isAnimation -->
                                    </picture>
                                </mee-rewards-animation><!-- end ngIf: item.icon.source -->
                            </div>
                            <div class="contentContainer">
                                <h3 id="card-title-" mee-heading="heading" class="c-heading ng-binding">Complete this
                                    puzzle
                                </h3>
                                <p mee-paragraph="para4"
                                    ng-class="{'compact' : $ctrl.item.isWot &amp;&amp; ($ctrl.item.pointProgress &gt; 0 || $ctrl.item.complete)}"
                                    class="ng-binding c-paragraph-4">Arrange the tiles to reveal the image</p>
                                <!-- ngIf: $ctrl.item.isWot && $ctrl.item.pointProgress > 0 && !$ctrl.item.complete -->
                                <!-- ngIf: $ctrl.item.isWot && $ctrl.item.complete -->
                                <!-- ngIf: $ctrl.areaConfig.isMobileViewModeEnabled && item.exclusiveLockedFeatureCardStatus != 'locked' -->
                                <div class="actionLink x-hidden-vp2 x-hidden-vp3 x-hidden-vp4 x-hidden-vp5 ng-scope"
                                    ng-if="$ctrl.areaConfig.isMobileViewModeEnabled &amp;&amp; item.exclusiveLockedFeatureCardStatus != 'locked'">
                                    <span id="card-action-" class="pointLink ng-binding">
                                        5 Points &gt;
                                    </span>
                                </div>
                                <!-- end ngIf: $ctrl.areaConfig.isMobileViewModeEnabled && item.exclusiveLockedFeatureCardStatus != 'locked' -->
                                <!-- ngIf: $ctrl.isDenseDashboard() && item.exclusiveLockedFeatureCardStatus != 'locked' -->
                                <!-- ngIf: $ctrl.areaConfig.isMobileViewModeEnabled && item.exclusiveLockedFeatureCardStatus == 'locked' -->
                                <!-- ngIf: $ctrl.isDenseDashboard() && item.exclusiveLockedFeatureStatus == 'locked' -->
                            </div>
                            <!-- ngIf: item.exclusiveLockedFeatureCardStatus != 'locked' -->
                            <div id="ma-card-link" class="actionLink ng-scope x-hidden-vp1"
                                ng-class="{'x-hidden-vp1' : $ctrl.areaConfig.isMobileViewModeEnabled}"
                                ng-hide="$ctrl.isDenseDashboard()"
                                ng-if="item.exclusiveLockedFeatureCardStatus != 'locked'">
                                <span id="card-action-" class="pointLink ng-binding">
                                    5 Points &gt;
                                </span>
                            </div><!-- end ngIf: item.exclusiveLockedFeatureCardStatus != 'locked' -->
                            <!-- ngIf: item.exclusiveLockedFeatureCardStatus == 'locked' -->
                            <mee-rewards-debug-info item="$ctrl.item"
                                class="ng-isolate-scope"><!-- ngIf: $ctrl.showDebugInfo -->
                            </mee-rewards-debug-info>
                        </a><!-- end ngIf: !item.sameTabNoRefresh -->

                        <!-- ngIf: item.exclusiveLockedFeatureCardStatus != 'notsupported' && item.secondaryIcon != null -->

                        <!-- ngIf: item.exclusiveLockedFeatureCardStatus == 'unlocked' -->


                        <!-- ngIf: item.exclusiveLockedFeatureCardStatus == 'locked' -->

                    </div>


                </mee-rewards-more-activities-card-item>
            </card-content></div>
    </mee-card>
</completed_activity>





<not_completed_activity>
    <mee-card ng-if="$ctrl.cardItems.length &gt; 2" display-type="single" ng-disabled="$ctrl.disabled" role="listitem"
        aria-posinset="3" aria-setsize="9"
        class="ng-scope ng-isolate-scope c-card f-single"><!-- ngIf: $ctrl.hasHeader() -->
        <div class="c-card-content" ng-transclude="content"><card-content class="ng-scope">
                <mee-rewards-more-activities-card-item item="$ctrl.cardItems[2]" cms-data="$ctrl.cmsData" index="2"
                    class="ng-isolate-scope">
                    <div class="text-align-center rewards-card-container" mee-rewardable=""
                        data-bi-id="ENstar_Rewards_DailyGlobalOffer_Evergreen_Friday"
                        data-m="{&quot;cN&quot;:&quot;Take today's news quiz&quot;,&quot;id&quot;:&quot;enstar_rewards_dailyglobaloffer_evergreen_friday&quot;,&quot;sN&quot;:1,&quot;aN&quot;:&quot;Rewards.Dashboard.MoreActivities&quot;,&quot;cT&quot;:&quot;Promotion&quot;}"
                        ng-class="{'locked-card' : item.exclusiveLockedFeatureCardStatus == 'locked'}">

                        <!-- ngIf: item.sameTabNoRefresh -->

                        <!-- ngIf: !item.sameTabNoRefresh --><a class="ds-card-sec ng-scope"
                            ng-if="!item.sameTabNoRefresh"
                            ng-href="https://www.bing.com/search?q=Bing%20news%20quiz&amp;form=ML2BF3&amp;OCID=ML2BF3&amp;PUBL=RewardsDO&amp;PROGRAMNAME=BingDailyOfferIN&amp;CREA=ML2BF3"
                            ng-click="$ctrl.onCardClick($event)" target="true" aria-disabled="false"
                            aria-labelledby="card-title- card-desc- card-action-"
                            aria-label="Take today's news quiz   See how you score on these questions   5 points   Activities section 3"
                            href="https://www.bing.com/search?q=Bing%20news%20quiz&amp;form=ML2BF3&amp;OCID=ML2BF3&amp;PUBL=RewardsDO&amp;PROGRAMNAME=BingDailyOfferIN&amp;CREA=ML2BF3">

                            <!-- ngIf: item.displayAnidHexaCode -->

                            <mee-rewards-points complete="item.complete" is-in-progress="item.isInProgress"
                                points="item.points" cms-data="$ctrl.cmsData" show-progress="$ctrl.item.isWot"
                                point-progress="$ctrl.item.pointProgress"
                                is-exclusive-locked-item="item.exclusiveLockedFeatureCardStatus == 'locked'"
                                ng-mouseover="$ctrl.onHoverPoints($event)" ng-class="{inprogress : item.isInProgress}"
                                class="ng-isolate-scope">
                                <div class="points clearfix">
                                    <!-- ngIf: !$ctrl.locked && !$ctrl.isExclusiveLockedItem -->
                                    <div ng-if="!$ctrl.locked &amp;&amp; !$ctrl.isExclusiveLockedItem" class="ng-scope">
                                        <!-- ngIf: $ctrl.iconImage -->
                                        <!-- ngIf: !$ctrl.iconImage && $ctrl.iconName --><span
                                            class="mee-icon mee-icon-AddMedium"
                                            ng-if="!$ctrl.iconImage &amp;&amp; $ctrl.iconName"
                                            aria-label="Points you will earn"
                                            role="img"></span><!-- end ngIf: !$ctrl.iconImage && $ctrl.iconName -->
                                        <!-- ngIf: $ctrl.pointsString --><span mee-heading="heading"
                                            ng-if="$ctrl.pointsString"
                                            class="c-heading pointsString ng-binding ng-scope">5</span><!-- end ngIf: $ctrl.pointsString -->
                                    </div><!-- end ngIf: !$ctrl.locked && !$ctrl.isExclusiveLockedItem -->
                                    <!-- ngIf: !$ctrl.locked && $ctrl.isExclusiveLockedItem -->
                                    <!-- ngIf: $ctrl.locked -->
                                </div>
                            </mee-rewards-points>

                            <!-- ngIf: $ctrl.isActivityInProgress -->

                            <div class="imageContainer"
                                ng-class="{'x-hidden-vp1' : item.displayType === 'single' &amp;&amp; !$ctrl.areaConfig.isMobileViewModeEnabled}">
                                <!-- ngIf: item.icon.source --><mee-rewards-animation data="item.icon"
                                    ng-if="item.icon.source"
                                    is-exclusive-locked-item="item.exclusiveLockedFeatureCardStatus == 'locked'"
                                    class="ng-scope ng-isolate-scope">
                                    <picture class="rewards-animation-container">
                                        <!-- ngIf: !$ctrl.isAnimation -->
                                        <!-- ngIf: $ctrl.isAnimation -->
                                        <div ng-if="$ctrl.isAnimation" class="rewardsBodymovin ng-scope"><svg
                                                xmlns="http://www.w3.org/2000/svg"
                                                xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 125 129"
                                                class="rewards-svg-renderer" preserveAspectRatio="xMidYMid meet"
                                                aria-hidden="true" focusable="false">
                                                <defs>
                                                    <clipPath id="__lottie_element_809">
                                                        <rect width="125" height="129" x="0" y="0"></rect>
                                                    </clipPath>
                                                </defs>
                                            </svg></div><!-- end ngIf: $ctrl.isAnimation -->
                                    </picture>
                                </mee-rewards-animation><!-- end ngIf: item.icon.source -->
                            </div>
                            <div class="contentContainer">
                                <h3 id="card-title-" mee-heading="heading" class="c-heading ng-binding">Take today's
                                    news quiz</h3>
                                <p mee-paragraph="para4"
                                    ng-class="{'compact' : $ctrl.item.isWot &amp;&amp; ($ctrl.item.pointProgress &gt; 0 || $ctrl.item.complete)}"
                                    class="ng-binding c-paragraph-4">See how you score on these questions</p>
                                <!-- ngIf: $ctrl.item.isWot && $ctrl.item.pointProgress > 0 && !$ctrl.item.complete -->
                                <!-- ngIf: $ctrl.item.isWot && $ctrl.item.complete -->
                                <!-- ngIf: $ctrl.areaConfig.isMobileViewModeEnabled && item.exclusiveLockedFeatureCardStatus != 'locked' -->
                                <div class="actionLink x-hidden-vp2 x-hidden-vp3 x-hidden-vp4 x-hidden-vp5 ng-scope"
                                    ng-if="$ctrl.areaConfig.isMobileViewModeEnabled &amp;&amp; item.exclusiveLockedFeatureCardStatus != 'locked'">
                                    <span id="card-action-" class="pointLink ng-binding">
                                        5 points &gt;
                                    </span>
                                </div>
                                <!-- end ngIf: $ctrl.areaConfig.isMobileViewModeEnabled && item.exclusiveLockedFeatureCardStatus != 'locked' -->
                                <!-- ngIf: $ctrl.isDenseDashboard() && item.exclusiveLockedFeatureCardStatus != 'locked' -->
                                <!-- ngIf: $ctrl.areaConfig.isMobileViewModeEnabled && item.exclusiveLockedFeatureCardStatus == 'locked' -->
                                <!-- ngIf: $ctrl.isDenseDashboard() && item.exclusiveLockedFeatureStatus == 'locked' -->
                            </div>
                            <!-- ngIf: item.exclusiveLockedFeatureCardStatus != 'locked' -->
                            <div id="ma-card-link" class="actionLink ng-scope x-hidden-vp1"
                                ng-class="{'x-hidden-vp1' : $ctrl.areaConfig.isMobileViewModeEnabled}"
                                ng-hide="$ctrl.isDenseDashboard()"
                                ng-if="item.exclusiveLockedFeatureCardStatus != 'locked'">
                                <span id="card-action-" class="pointLink ng-binding">
                                    5 points &gt;
                                </span>
                            </div><!-- end ngIf: item.exclusiveLockedFeatureCardStatus != 'locked' -->
                            <!-- ngIf: item.exclusiveLockedFeatureCardStatus == 'locked' -->
                            <mee-rewards-debug-info item="$ctrl.item"
                                class="ng-isolate-scope"><!-- ngIf: $ctrl.showDebugInfo -->
                            </mee-rewards-debug-info>
                        </a><!-- end ngIf: !item.sameTabNoRefresh -->

                        <!-- ngIf: item.exclusiveLockedFeatureCardStatus != 'notsupported' && item.secondaryIcon != null -->

                        <!-- ngIf: item.exclusiveLockedFeatureCardStatus == 'unlocked' -->


                        <!-- ngIf: item.exclusiveLockedFeatureCardStatus == 'locked' -->

                    </div>


                </mee-rewards-more-activities-card-item>
            </card-content></div>
    </mee-card>
</not_completed_activity>
























<script type="text/javascript" nonce="">
    MeePortal.Rewards.Clarity.clarityInit(window, document, "clarity", "script", "76hfh21kdi");
    var dashboard = { "userStatus": {} };
    if (dashboard && dashboard.dashboardFlights && dashboard.dashboardFlights.dashboardbannernav) {
        window.clarity("set", "flight", "dashboardbannernav." + dashboard.dashboardFlights.dashboardbannernav);
    }

    if (dashboard && dashboard.welcomeTour && dashboard.welcomeTour.promotion && dashboard.welcomeTour.promotion.offerId) {
        window.clarity("set", "flight", "dashboardbannernav." + dashboard.welcomeTour.promotion.offerId);
    }

    var catalog = { "catalogItems": [{}] };
    if (catalog && catalog.redeemPageFlights && catalog.redeemPageFlights.showRedeemBenevityOrganizations) {
        window.clarity("set", "flight", "showRedeemBenevityOrganizations." + catalog.redeemPageFlights.showRedeemBenevityOrganizations);
    }

    if (areaConfig && areaConfig.flights && areaConfig.flights.isDenseDashboardLayout) {
        window.clarity("set", "flight", "isDenseDashboardLayout");
    }

    if (dashboard && dashboard.mbingFlight && dashboard.mbingFlight) {
        window.clarity("set", "flight", "bingAppPromotionBanner." + dashboard.mbingFlight.name);
    }

    if (areaConfig && areaConfig.flights && areaConfig.flights.isSuggestedSearchesEnabled) {
        window.clarity("set", "flight", "isSuggestedSearchesEnabled");
    }

    if (areaConfig && areaConfig.flights && areaConfig.flights['dailySetLLM-trt']) {
        window.clarity("set", "flight", "dailySetLLM");
    }

    if (areaConfig && areaConfig.flights) {
        ["explbingsection", "explbingstatus", "explbingsearch", "explbingindsrch"].forEach(function (eobFlight) {
            if (areaConfig.flights[eobFlight]) {
                window.clarity("set", "flight", eobFlight);
            }
        });
    }

    if (areaConfig && areaConfig.flights) {
        ["isShoppingSuggestionEnabled", "isGenericShoppingSuggestion", "isPersonalizedShoppingSuggestion", "isPersonalizedShoppingSuggestionUS", "isPersonalizedShoppingSuggestionWithShoppingDestination"].forEach(function (flight) {
            if (areaConfig.flights[flight]) {
                window.clarity("set", "flight", flight);
            }
        });
    }

    window.clarity('consent');
</script>






















<script type="text/javascript" nonce="">
    (function () {
        window.flyoutViewModel = {"userInfo": "i removed the content as it is too big"};
        window.isRightToLeft = false;
    })(window);

    // enable clarity for go Big flyout
    (function (c, l, a, r, i, t, y) {
        let trustedScriptURL = "https://www.clarity.ms/tag/" + i;

        if (window.trustedTypes && window.trustedTypes.createPolicy) {
            const policy = window.trustedTypes.createPolicy('rwflyoutDefault', {
                createScriptURL: function (url) {
                    return url;
                }
            });

            trustedScriptURL = policy.createScriptURL(trustedScriptURL);
        }

        c[a] = c[a] || function () { (c[a].q = c[a].q || []).push(arguments) };
        t = l.createElement(r); 
        t.async = 1; 
        t.src = trustedScriptURL;
        y = l.getElementsByTagName(r)[0]; 
        y.parentNode.insertBefore(t, y);
        c[a]("set", "flight", window.flyoutViewModel.FeatureNames);
    })(window, document, "clarity", "script", "j95dtajyim");
</script>