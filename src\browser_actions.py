import asyncio
import random
import nodriver as uc
from nodriver import cdp
import os
import hashlib
from .utils import log_error
from .logger import logger
import string
import math
import json
from ai_model.human_interaction_ai import HumanInteractionAI, InteractionConfig
from ai_model.interaction_generator import HumanInteractionGenerator

class Mouse:
    def __init__(self, tab):
        self.tab = tab
        self._x = 0.0
        self._y = 0.0
        self._button = cdp.input_.MouseButton.NONE

    async def move(self, x: float, y: float, steps: int = 1):
        """Move mouse cursor with optional intermediate steps"""
        from_x = self._x
        from_y = self._y
        self._x = x
        self._y = y

        for i in range(1, steps + 1):
            current_x = round(from_x + (self._x - from_x) * (i / steps))
            current_y = round(from_y + (self._y - from_y) * (i / steps))

            await self.tab.send(cdp.input_.dispatch_mouse_event(
                type_='mouseMoved',
                x=current_x,
                y=current_y,
                button=self._button
            ))

    async def click(self, x: float, y: float, button: str = 'left', delay: float = 0):
        """Click at specific coordinates"""
        await self.move(x, y)
        await self.down(button=button)
        if delay:
            await asyncio.sleep(delay / 1000)
        await self.up(button=button)

    async def down(self, button: str = 'left'):
        """Press mouse button down"""
        button_map = {
            'left': cdp.input_.MouseButton.LEFT,
            'right': cdp.input_.MouseButton.RIGHT,
            'middle': cdp.input_.MouseButton.MIDDLE,
            'none': cdp.input_.MouseButton.NONE,
        }
        self._button = button_map.get(button.lower(), cdp.input_.MouseButton.LEFT)
        await self.tab.send(cdp.input_.dispatch_mouse_event(
            type_='mousePressed',
            button=self._button,
            x=self._x,
            y=self._y,
            click_count=1
        ))

    async def up(self, button: str = 'left'):
        """Release mouse button"""
        button_map = {
            'left': cdp.input_.MouseButton.LEFT,
            'right': cdp.input_.MouseButton.RIGHT,
            'middle': cdp.input_.MouseButton.MIDDLE,
            'none': cdp.input_.MouseButton.NONE,
        }
        button_to_release = button_map.get(button.lower(), cdp.input_.MouseButton.LEFT)
        await self.tab.send(cdp.input_.dispatch_mouse_event(
            type_='mouseReleased',
            button=button_to_release,
            x=self._x,
            y=self._y,
            click_count=1
        ))
        self._button = cdp.input_.MouseButton.NONE


class BrowserActions:
    def __init__(self, tab, email):
        self.tab = tab
        self.email = email
        self.session_dir = "sessions"
        self.mouse = Mouse(self.tab)
        self.current_mouse_x = 450 
        self.current_mouse_y = 300

        # Initialize and load the human behavior model
        try:
            config = InteractionConfig()
            self.ai_model = HumanInteractionAI(config)
            
            # Try to load best models first, fallback to saved_models directory
            try:
                # Load best models from ai_model root directory
                import torch
                self.ai_model.mouse_model.load_state_dict(torch.load('ai_model/mouse_model_best.pth', map_location=self.ai_model.device))
                self.ai_model.typing_model.load_state_dict(torch.load('ai_model/typing_model_best.pth', map_location=self.ai_model.device))
                self.ai_model.scrolling_model.load_state_dict(torch.load('ai_model/scrolling_model_best.pth', map_location=self.ai_model.device))
                logger.info("Best trained models loaded successfully.", extra={'email': self.email})
            except FileNotFoundError:
                # Fallback to saved_models directory
                self.ai_model.load_models(directory='ai_model/saved_models/')
                logger.info("Fallback models loaded from saved_models directory.", extra={'email': self.email})
            
            self.human_interaction_generator = HumanInteractionGenerator(self.ai_model)
            logger.info("Human behavior model loaded successfully.", extra={'email': self.email})
        except Exception as e:
            logger.error(f"An unexpected error occurred during human behavior model loading: {e}. Human behavior model will not be used.", extra={'email': self.email})
            self.ai_model = None
            self.human_interaction_generator = None

    async def human_like_mouse_move(self, target_x, target_y):
        """
        Simulates smooth, human-like mouse movement using the trained model.
        """
        if not self.human_interaction_generator:
            logger.warning("Human behavior model not available for mouse movement. Using default random path.", extra={'email': self.email})
            await self._default_human_like_mouse_move(target_x, target_y)
            return

        start_x, start_y = self.current_mouse_x, self.current_mouse_y
        logger.debug(f"Moving mouse from ({start_x}, {start_y}) to ({target_x}, {target_y}) using trained model...", extra={'email': self.email})

        mouse_path = self.human_interaction_generator.generate_mouse_path((start_x, start_y), (target_x, target_y))

        for i, point in enumerate(mouse_path.points):
            # Update mouse position for accurate tracking
            await self.mouse.move(int(point[0]), int(point[1]))
            if i < len(mouse_path.timings) -1:
                await asyncio.sleep(mouse_path.timings[i+1] - mouse_path.timings[i])

        self.current_mouse_x, self.current_mouse_y = int(target_x), int(target_y)
        logger.debug(f"Mouse moved to final position: ({self.current_mouse_x}, {self.current_mouse_y})", extra={'email': self.email})

    async def _default_human_like_mouse_move(self, target_x, target_y):
        """Default mouse move if model is not available."""
        start_x, start_y = self.current_mouse_x, self.current_mouse_y
        steps = random.randint(10, 20)
        await self.mouse.move(target_x, target_y, steps=steps)
        self.current_mouse_x, self.current_mouse_y = int(target_x), int(target_y)

    async def send_keys(self, selector, text, timeout=30):
        """
        Waits for an element to appear, adds a human-like delay, and then sends keys to it.
        Uses trained model for delays and durations.
        Returns the element if found and keys sent, False otherwise.
        """
        element = await self.wait_for_element(selector, timeout=timeout)
        if element:
            await self.click(selector, timeout=timeout)
            if self.human_interaction_generator:
                typing_sequence = self.human_interaction_generator.generate_typing_sequence(text)
                for i, key in enumerate(typing_sequence.keys):
                    if key == 'Backspace':
                        await self.tab.send(uc.cdp.input_.dispatch_key_event("rawKeyDown", windows_virtual_key_code=8, key='Backspace'))
                        await self.tab.send(uc.cdp.input_.dispatch_key_event("keyUp", windows_virtual_key_code=8, key='Backspace'))
                    else:
                        await self.tab.send(uc.cdp.input_.dispatch_key_event("keyDown", text=key))
                        await self.tab.send(uc.cdp.input_.dispatch_key_event("keyUp", text=key))
                    if i < len(typing_sequence.timings) - 1:
                        await asyncio.sleep(typing_sequence.timings[i+1] - typing_sequence.timings[i])
            else:
                logger.warning("Human behavior model not available for typing. Using default typing.", extra={'email': self.email})
                for char in text:
                    await self.tab.send(uc.cdp.input_.dispatch_key_event("keyDown", text=char))
                    await asyncio.sleep(random.uniform(0.05, 0.15))
                    await self.tab.send(uc.cdp.input_.dispatch_key_event("keyUp", text=char))
            return element
        return False

    async def click(self, selector, timeout=30):
        """
        Waits for an element to appear, adds a human-like delay, and then clicks it.
        Returns the element if found and clicked, False otherwise.
        """
        element = await self.wait_for_element(selector, timeout=timeout)
        if element:
            # Get element position and scroll information in one call for accuracy
            try:
                element_info = await self.tab.evaluate("""
                    (element) => {
                        const rect = element.getBoundingClientRect();
                        const scrollX = window.pageXOffset || document.documentElement.scrollLeft || 0;
                        const scrollY = window.pageYOffset || document.documentElement.scrollTop || 0;
                        
                        return {
                            // Viewport coordinates (what get_position() returns)
                            viewportX: rect.left,
                            viewportY: rect.top,
                            width: rect.width,
                            height: rect.height,
                            // Absolute page coordinates (viewport + scroll)
                            absoluteX: rect.left + scrollX,
                            absoluteY: rect.top + scrollY,
                            scrollX: scrollX,
                            scrollY: scrollY
                        };
                    }
                """, element)
                
                if element_info and isinstance(element_info, dict):
                    # Use viewport coordinates (no scroll adjustment needed for mouse movement)
                    target_x = random.uniform(
                        element_info['viewportX'] + 5, 
                        element_info['viewportX'] + element_info['width'] - 5
                    )
                    target_y = random.uniform(
                        element_info['viewportY'] + 5, 
                        element_info['viewportY'] + element_info['height'] - 5
                    )
                    
                    logger.debug(f"Element position - Viewport: ({element_info['viewportX']:.1f}, {element_info['viewportY']:.1f}), "
                               f"Absolute: ({element_info['absoluteX']:.1f}, {element_info['absoluteY']:.1f}), "
                               f"Scroll: ({element_info['scrollX']:.1f}, {element_info['scrollY']:.1f}), "
                               f"Target: ({target_x:.1f}, {target_y:.1f})", extra={'email': self.email})
                else:
                    # Fallback to old method if JavaScript fails
                    pos = await element.get_position()
                    if pos:
                        target_x = random.uniform(pos.x + 5, pos.x + pos.width - 5)
                        target_y = random.uniform(pos.y + 5, pos.y + pos.height - 5)
                        logger.warning("Using fallback positioning method", extra={'email': self.email})
                    else:
                        logger.error("Could not get element position", extra={'email': self.email})
                        await element.click()
                        return element
                        
            except Exception as e:
                logger.warning(f"Failed to get accurate element position: {e}. Using fallback.", extra={'email': self.email})
                # Fallback to old method
                pos = await element.get_position()
                if pos:
                    target_x = random.uniform(pos.x + 5, pos.x + pos.width - 5)
                    target_y = random.uniform(pos.y + 5, pos.y + pos.height - 5)
                else:
                    await element.click()
                    return element
            # Ensure coordinates are within reasonable bounds
            target_x = max(0, min(1920, target_x))  # Increased bounds for larger screens
            target_y = max(0, min(1080, target_y))

            await self.human_like_mouse_move(target_x, target_y)
            
            await self.mouse.click(int(target_x), int(target_y))
            return element
        return False

    async def wait_for_element(self, selector, timeout=30):
        """
        Waits for an element to appear using a selector.
        Returns the element if found, False otherwise.
        """
        logger.debug(f"Waiting for element with selector '{selector}' for up to {timeout} seconds...", extra={'email': self.email})
        start_time = asyncio.get_event_loop().time()
        while asyncio.get_event_loop().time() - start_time < timeout:
            try:
                remaining_timeout = timeout - (asyncio.get_event_loop().time() - start_time)
                if remaining_timeout <= 0:
                    logger.warning(f"Timeout: No remaining time to find element '{selector}'.", extra={'email': self.email})
                    return False
                element = await self.tab.select(selector, timeout=remaining_timeout)
                if element:
                    is_visible_js = """
                    (element) => {
                        if (!element) return false;
                        if (element.offsetParent === null) return false;
                        if (element.offsetWidth === 0 && element.offsetHeight === 0) return false;
                        if (element.getClientRects().length === 0) return false;

                        const style = window.getComputedStyle(element);
                        if (style.visibility === 'hidden') return false;
                        if (style.opacity === '0') return false;

                        const rect = element.getBoundingClientRect();
                        const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
                        const viewportHeight = window.innerHeight || document.documentElement.clientHeight;

                        const isVisible = (
                            rect.top < viewportHeight &&
                            rect.left < viewportWidth &&
                            rect.bottom > 0 &&
                            rect.right > 0
                        );

                        return isVisible;
                    }
                    """
                    visible = await element.apply(is_visible_js)
                    if visible:
                        logger.debug(f"Element '{selector}' found and is visible.", extra={'email': self.email})
                        return element
                    else:
                        logger.debug(f"Element '{selector}' found in DOM but not visible. Retrying...", extra={'email': self.email})
                        await asyncio.sleep(1)
            except asyncio.TimeoutError:
                logger.debug(f"Element '{selector}' not found yet. Retrying...", extra={'email': self.email})
                await asyncio.sleep(1)
            except Exception as e:
                page_html = await self.tab.get_content()
                await log_error(self.email, f"Error waiting for element '{selector}': {e}", page_html)
                logger.error(f"Error during wait for element '{selector}': {e}", extra={'email': self.email})
                return False
        logger.debug(f"Timeout: Element with selector '{selector}' not found within {timeout} seconds.", extra={'email': self.email})
        return False

    async def wait_for_url_change_or_target(self, initial_url_part, target_url_part, timeout=60):
        """Waits for the URL to change from initial_url_part or reach target_url_part."""
        start_time = asyncio.get_event_loop().time()
        while asyncio.get_event_loop().time() - start_time < timeout:
            current_url = self.tab.url
            if target_url_part in current_url:
                logger.debug(f"Reached target URL: {current_url}", extra={'email': self.email})
                return True
            if initial_url_part not in current_url:
                logger.debug(f"Navigated away from initial URL. Current: {current_url}", extra={'email': self.email})
                return True
            await asyncio.sleep(1)
        await log_error(self.email, f"Timeout waiting for URL change from {initial_url_part} or to {target_url_part}. Current URL: {self.tab.url}")
        return False

    async def get_text_content(self, selector, timeout=10):
        """
        Waits for an element and returns its text content.
        Returns the text content if found, None otherwise.
        """
        element = await self.wait_for_element(selector, timeout=timeout)
        if element:
            text = element.text_all # Corrected: element.text_all is a property, not an awaitable.
            logger.debug(f"Text content for '{selector}': '{text}'", extra={'email': self.email})
            return text.strip()
        logger.warning(f"Could not get text content for element with selector '{selector}'. Element not found.", extra={'email': self.email})
        return None

    async def get_text_content_from_element(self, element):
        """
        Returns the text content of a given element.
        Returns the text content if found, None otherwise.
        """
        if element:
            try:
                text = element.text_all
                logger.debug(f"Text content from element: '{text}'", extra={'email': self.email})
                return text.strip()
            except Exception as e:
                logger.warning(f"Error getting text content from element: {e}", extra={'email': self.email})
                return None
        logger.warning("Could not get text content from element: element is None.", extra={'email': self.email})
        return None

    async def get_text_content_by_xpath(self, xpath_selector, timeout=10):
        """
        Waits for an element found by XPath and returns its text content.
        Returns the text content if found, None otherwise.
        """
        element = await self.wait_for_element_by_xpath(xpath_selector, timeout=timeout)
        if element:
            text = element.text_all
            logger.debug(f"Text content for XPath '{xpath_selector}': '{text}'", extra={'email': self.email})
            return text.strip()
        logger.warning(f"Could not get text content for element with XPath '{xpath_selector}'. Element not found.", extra={'email': self.email})
        return None

    async def get_attribute(self, selector, attribute_name, timeout=10):
        """
        Waits for an element and returns the value of a specified attribute.
        Returns the attribute value if found, None otherwise.
        """
        element = await self.wait_for_element(selector, timeout=timeout)
        if element:
            attrs = element.attrs
            attr_value = attrs.get(attribute_name)
            logger.debug(f"Attribute '{attribute_name}' for '{selector}': '{attr_value}'", extra={'email': self.email})
            return attr_value
        logger.warning(f"Could not get attribute '{attribute_name}' for element with selector '{selector}'. Element not found.", extra={'email': self.email})
        return None

    async def get_attribute_by_xpath(self, xpath_selector, attribute_name, timeout=10):
        """
        Waits for an element found by XPath and returns the value of a specified attribute.
        Returns the attribute value if found, None otherwise.
        """
        element = await self.wait_for_element_by_xpath(xpath_selector, timeout=timeout)
        if element:
            attrs = element.attrs
            attr_value = attrs.get(attribute_name)
            logger.debug(f"Attribute '{attribute_name}' for XPath '{xpath_selector}': '{attr_value}'", extra={'email': self.email})
            return attr_value
        logger.warning(f"Could not get attribute '{attribute_name}' for element with XPath '{xpath_selector}'. Element not found.", extra={'email': self.email})
        return None

    async def get_elements_by_selector(self, selector, timeout=10):
        """
        Waits for and returns a list of elements matching a CSS selector.
        Returns a list of elements, or an empty list if none found within timeout.
        """
        logger.debug(f"Waiting for elements with selector '{selector}' for up to {timeout} seconds...", extra={'email': self.email})
        start_time = asyncio.get_event_loop().time()
        elements = []
        while asyncio.get_event_loop().time() - start_time < timeout:
            try:
                elements = await self.tab.select_all(selector, timeout=1) # Use a short internal timeout
                if elements:
                    logger.debug(f"Found {len(elements)} elements with selector '{selector}'.", extra={'email': self.email})
                    return elements
            except asyncio.TimeoutError:
                pass # Continue waiting
            await asyncio.sleep(0.5) # Wait before retrying
        logger.warning(f"No elements found with selector '{selector}' within {timeout} seconds.", extra={'email': self.email})
        return []

    async def get_elements_by_xpath(self, xpath_selector, timeout=10):
        """
        Waits for and returns a list of elements matching an XPath selector.
        Returns a list of elements, or an empty list if none found within timeout.
        """
        logger.debug(f"Waiting for elements with XPath '{xpath_selector}' for up to {timeout} seconds...", extra={'email': self.email})
        start_time = asyncio.get_event_loop().time()
        elements = []
        while asyncio.get_event_loop().time() - start_time < timeout:
            try:
                elements = await self.tab.xpath(xpath_selector, timeout=1) # Use a short internal timeout
                if elements:
                    logger.debug(f"Found {len(elements)} elements with XPath '{xpath_selector}'.", extra={'email': self.email})
                    return elements
            except asyncio.TimeoutError:
                pass # Continue waiting
            await asyncio.sleep(0.5) # Wait before retrying
        logger.warning(f"No elements found with XPath '{xpath_selector}' within {timeout} seconds.", extra={'email': self.email})
        return []

    async def wait_for_element_by_xpath(self, xpath_selector, timeout=30):
        """
        Waits for an element to appear using an XPath selector.
        Returns the element if found, False otherwise.
        """
        logger.debug(f"Waiting for element with XPath '{xpath_selector}' for up to {timeout} seconds...", extra={'email': self.email})
        start_time = asyncio.get_event_loop().time()
        while asyncio.get_event_loop().time() - start_time < timeout:
            try:
                remaining_timeout = timeout - (asyncio.get_event_loop().time() - start_time)
                if remaining_timeout <= 0:
                    logger.warning(f"Timeout: No remaining time to find element '{xpath_selector}'.", extra={'email': self.email})
                    return False
                element = await self.tab.xpath(xpath_selector, timeout=remaining_timeout)
                if element:
                    # XPath can return a list, we want the first element if it's a list
                    if isinstance(element, list):
                        if element:
                            element = element[0]
                        else:
                            element = None # No elements found by xpath
                    
                    if element: # Check if element is not None after potential list handling
                        is_visible_js = """
                        (element) => {
                            if (!element) return false;
                            if (element.offsetParent === null) return false;
                            if (element.offsetWidth === 0 && element.offsetHeight === 0) return false;
                            if (element.getClientRects().length === 0) return false;

                            const style = window.getComputedStyle(element);
                            if (style.visibility === 'hidden') return false;
                            if (style.opacity === '0') return false;

                            const rect = element.getBoundingClientRect();
                            const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
                            const viewportHeight = window.innerHeight || document.documentElement.clientHeight;

                            const isVisible = (
                                rect.top < viewportHeight &&
                                rect.left < viewportWidth &&
                                rect.bottom > 0 &&
                                rect.right > 0
                            );

                            return isVisible;
                        }
                        """
                        visible = await element.apply(is_visible_js)
                        if visible:
                            logger.debug(f"Element '{xpath_selector}' found and is visible.", extra={'email': self.email})
                            return element
                        else:
                            logger.debug(f"Element '{xpath_selector}' found in DOM but not visible. Retrying...", extra={'email': self.email})
                            await asyncio.sleep(1)
                    else:
                        logger.debug(f"Element '{xpath_selector}' not found by XPath. Retrying...", extra={'email': self.email})
                        await asyncio.sleep(1)
            except asyncio.TimeoutError:
                logger.debug(f"Element '{xpath_selector}' not found yet. Retrying...", extra={'email': self.email})
                await asyncio.sleep(1)
            except Exception as e:
                page_html = await self.tab.get_content()
                await log_error(self.email, f"Error waiting for element '{xpath_selector}': {e}", page_html)
                logger.error(f"Error during wait for element '{xpath_selector}': {e}", extra={'email': self.email})
                return False
        logger.warning(f"Timeout: Element with XPath '{xpath_selector}' not found within {timeout} seconds.", extra={'email': self.email})
        return False

    async def wait_for_child_element(self, parent_element, selector, timeout=30):
        """
        Waits for a child element to appear within a parent element using a CSS selector.
        Returns the child element if found, False otherwise.
        """
        if not parent_element:
            logger.warning("Parent element is None. Cannot wait for child element.", extra={'email': self.email})
            return False

        logger.debug(f"Waiting for child element with selector '{selector}' within parent for up to {timeout} seconds...", extra={'email': self.email})
        start_time = asyncio.get_event_loop().time()
        while asyncio.get_event_loop().time() - start_time < timeout:
            try:
                remaining_timeout = timeout - (asyncio.get_event_loop().time() - start_time)
                if remaining_timeout <= 0:
                    logger.warning(f"Timeout: No remaining time to find child element '{selector}'.", extra={'email': self.email})
                    return False
                
                # Use query_selector on the parent element
                child_element = await parent_element.query_selector(selector)
                
                if child_element:
                    # Re-use the visibility check from wait_for_element
                    is_visible_js = """
                    (element) => {
                        if (!element) return false;
                        if (element.offsetParent === null) return false;
                        if (element.offsetWidth === 0 && element.offsetHeight === 0) return false;
                        if (element.getClientRects().length === 0) return false;

                        const style = window.getComputedStyle(element);
                        if (style.visibility === 'hidden') return false;
                        if (style.opacity === '0') return false;

                        const rect = element.getBoundingClientRect();
                        const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
                        const viewportHeight = window.innerHeight || document.documentElement.clientHeight;

                        const isVisible = (
                            rect.top < viewportHeight &&
                            rect.left < viewportWidth &&
                            rect.bottom > 0 &&
                            rect.right > 0
                        );

                        return isVisible;
                    }
                    """
                    visible = await child_element.apply(is_visible_js)
                    if visible:
                        logger.debug(f"Child element '{selector}' found and is visible within parent.", extra={'email': self.email})
                        return child_element
                    else:
                        logger.debug(f"Child element '{selector}' found in DOM but not visible within parent. Retrying...", extra={'email': self.email})
                        await asyncio.sleep(1)
                else:
                    logger.debug(f"Child element '{selector}' not found within parent yet. Retrying...", extra={'email': self.email})
                    await asyncio.sleep(1) # Wait if element is not found
            except Exception as e:
                # Log error but don't return False immediately, keep retrying until timeout
                logger.error(f"Error during wait for child element '{selector}' within parent: {e}", extra={'email': self.email})
                await asyncio.sleep(1)
        logger.warning(f"Timeout: Child element with selector '{selector}' not found within parent within {timeout} seconds.", extra={'email': self.email})
        return False

    async def wait_for_element_class_change(self, element_selector, expected_class, timeout=10, interval=0.5):
        """
        Waits for an element's class to change to the expected_class.
        Returns True if the class changes, False otherwise.
        """
        logger.debug(f"Waiting for element '{element_selector}' to have class '{expected_class}' for up to {timeout} seconds...", extra={'email': self.email})
        start_time = asyncio.get_event_loop().time()
        while asyncio.get_event_loop().time() - start_time < timeout:
            try:
                element = await self.tab.select(element_selector, timeout=interval) # Short timeout for each check
                if element:
                    current_class = element.attrs.get('class')
                    if current_class is not None and expected_class in current_class:
                        logger.info(f"Element '{element_selector}' now has class '{expected_class}'.", extra={'email': self.email})
                        return True
                await asyncio.sleep(interval)
            except asyncio.TimeoutError:
                await asyncio.sleep(interval) # Element not found yet, keep waiting
            except Exception as e:
                logger.error(f"Error waiting for class change on '{element_selector}': {e}", extra={'email': self.email})
                return False
        logger.warning(f"Timeout: Element '{element_selector}' did not get class '{expected_class}' within {timeout} seconds.", extra={'email': self.email})
        return False

    async def simulate_human_behavior(self):
        """Simulate realistic human browsing patterns (currently no-op as per user request)"""
        pass

    async def scroll_to_element(self, element, block='center', timeout=10):
        """
        Scrolls the page to bring a specific element into view.
        """
        if not element:
            return False

        is_visible_js = """
        (element) => {
            if (!element) return false;
            const rect = element.getBoundingClientRect();
            const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
            return rect.top >= 0 && rect.bottom <= viewportHeight;
        }
        """
        try:
            if await element.apply(is_visible_js):
                return True
        except Exception:
            pass

        if self.human_interaction_generator:
            scroll_sequence = self.human_interaction_generator.generate_scroll_sequence(element.top, self.tab.top)
            for i, delta in enumerate(scroll_sequence.scroll_deltas):
                await self.tab.mouse.wheel(delta_y=delta)
                if i < len(scroll_sequence.timings) - 1:
                    await asyncio.sleep(scroll_sequence.timings[i+1] - scroll_sequence.timings[i])
            return True
        else:
            logger.warning("Human behavior model not available for scrolling. Using default scrolling.", extra={'email': self.email})
            await element.scroll_into_view(block=block)
            return True

    async def human_like_scroll_to_random_target(self):
        print("[TRACE] Entered human_like_scroll_to_random_target")
        try:
            print("[TRACE] Step 1: Waiting for #scrollSection")
            scroll_section_element = await self.wait_for_element("#scrollSection", timeout=5)
            print("[TRACE] Step 2: Got scroll_section_element", scroll_section_element)
            if not scroll_section_element:
                print("[TRACE] Step 2a: scroll_section_element not found")
                logger.warning("Could not find scroll section element", extra={'email': self.email})
                return False
            print("[TRACE] Step 3: Getting position of scroll section")
            position_js = """
            (() => {
                const element = document.getElementById('scrollSection');
                if (!element) return null;
                const rect = element.getBoundingClientRect();
                const scrollX = window.pageXOffset || document.documentElement.scrollLeft;
                const scrollY = window.pageYOffset || document.documentElement.scrollTop;
                return {
                    x: rect.left + scrollX,
                    y: rect.top + scrollY,
                    width: rect.width,
                    height: rect.height
                };
            })()
            """
            pos = await self.tab.evaluate(position_js)
            print("[TRACE] Step 4: Got pos", pos)
            if isinstance(pos, list):
                # Convert list of pairs to dict, and extract .value if present
                pos = {k: (v['value'] if isinstance(v, dict) and 'value' in v else v) for k, v in pos}
                print("[TRACE] Step 4b: Converted pos to dict", pos)
            if not pos:
                print("[TRACE] Step 4a: pos not found")
                logger.warning("Could not get position of scroll section", extra={'email': self.email})
                return False
            print("[TRACE] Step 5: Calculating target_x, target_y")
            target_x = pos['x'] + pos['width'] / 2
            target_y = pos['y'] + pos['height'] / 2
            print(f"[TRACE] Step 6: Moving mouse to center ({target_x}, {target_y})")
            await self.human_like_mouse_move(target_x, target_y)
            print("[TRACE] Step 7: Getting scroll_info from JS")
            scroll_info_js = """
            (() => {
                const scrollSection = document.getElementById('scrollSection');
                const targetElement = document.getElementById('targetPosition');
                const scrollContent = document.querySelector('.scroll-content');
                const scrollTarget = document.getElementById('scrollTarget');
                if (!scrollSection || !scrollContent) return {
                    currentPosition: 0,
                    targetPosition: 0,
                    maxScroll: 0
                };
                let targetPosition = 0;
                if (targetElement && targetElement.textContent) {
                    targetPosition = parseInt(targetElement.textContent.trim(), 10);
                } else if (scrollTarget) {
                    const targetRect = scrollTarget.getBoundingClientRect();
                    const sectionRect = scrollSection.getBoundingClientRect();
                    targetPosition = targetRect.top - sectionRect.top + scrollSection.scrollTop;
                }
                return {
                    currentPosition: scrollSection.scrollTop,
                    targetPosition: targetPosition,
                    maxScroll: scrollContent.offsetHeight - scrollSection.offsetHeight
                };
            })()
            """
            print("[TRACE] Step 8: Evaluating scroll_info_js")
            scroll_info = await self.tab.evaluate(scroll_info_js)
            print(f"[TRACE] Step 9: Got scroll_info: {scroll_info} (type: {type(scroll_info)})")
            if isinstance(scroll_info, list):
                # Convert list of pairs to dict, and extract .value if present
                scroll_info = {k: (v['value'] if isinstance(v, dict) and 'value' in v else v) for k, v in scroll_info}
                print("[TRACE] Step 9b: Converted scroll_info to dict", scroll_info)
            logger.debug(f"Raw scroll_info value: {scroll_info} (type: {type(scroll_info)})", extra={'email': self.email})
            print("[TRACE] Step 10: Checking scroll_info type and keys")
            if not isinstance(scroll_info, dict) or \
               not all(k in scroll_info for k in ('currentPosition', 'targetPosition', 'maxScroll')):
                print(f"[TRACE] Step 10a: Unexpected scroll_info value: {scroll_info} (type: {type(scroll_info)})")
                logger.error(f"Unexpected scroll_info value: {scroll_info} (type: {type(scroll_info)})", extra={'email': self.email})
                import traceback
                print(traceback.format_exc())
                logger.error(f"Stack trace: {traceback.format_exc()}", extra={'email': self.email})
                return False
            print("[TRACE] Step 11: Accessing scroll_info keys")
            current_position = scroll_info['currentPosition']
            target_position = scroll_info['targetPosition']
            max_scroll = scroll_info['maxScroll']
            print(f"[TRACE] Step 12: current_position={current_position}, target_position={target_position}, max_scroll={max_scroll}")
            if max_scroll <= 0:
                print("[TRACE] Step 13: max_scroll <= 0")
                logger.warning("Scroll section is not scrollable.", extra={'email': self.email})
                return False
            if target_position <= 0:
                print("[TRACE] Step 14: target_position <= 0, generating random target_position")
                target_position = random.uniform(max_scroll * 0.2, max_scroll * 0.8)
            print(f"[TRACE] Step 15: Logging scroll from {current_position} to {target_position} (max: {max_scroll})")
            logger.info(f"Scrolling from {current_position:.0f} to {target_position:.0f} (max: {max_scroll:.0f})", extra={'email': self.email})
            print("[TRACE] Step 16: Focusing scroll section with click")
            await self.mouse.click(target_x, target_y)
            await asyncio.sleep(0.2)
            print("[TRACE] Step 17: Checking for human_interaction_generator")
            if self.human_interaction_generator:
                print("[TRACE] Step 18: Using AI model for human-like scrolling")
                try:
                    target_pos = float(target_position)
                    current_pos = float(current_position)
                    target_pos = int(float(target_position))
                    current_pos = int(float(current_position))
                    print(f"[TRACE] Step 19: target_pos={target_pos}, current_pos={current_pos}")
                    if target_pos == current_pos:
                        print("[TRACE] Step 20: Already at target position")
                        logger.info("Already at target position", extra={'email': self.email})
                        return True
                    scroll_sequence = self.human_interaction_generator.generate_scroll_sequence(
                        target_position=target_pos,
                        current_position=current_pos
                    )
                    print(f"[TRACE] Step 21: Generated scroll_sequence: {scroll_sequence}")
                except (ValueError, TypeError) as e:
                    print(f"[TRACE] Step 22: Error converting scroll positions: {e}")
                    logger.error(f"Error converting scroll positions to float: {e}", extra={'email': self.email})
                    return False
                logger.debug(f"Generated scroll sequence with {len(scroll_sequence.scroll_deltas)} scroll events", extra={'email': self.email})
                print("[TRACE] Step 23: Validating scroll_sequence")
                if not hasattr(scroll_sequence, 'scroll_deltas') or not scroll_sequence.scroll_deltas:
                    print("[TRACE] Step 24: Invalid scroll sequence generated")
                    logger.error("Invalid scroll sequence generated", extra={'email': self.email})
                    return False
                print("[TRACE] Step 25: Executing scroll sequence")
                for i, delta in enumerate(scroll_sequence.scroll_deltas):
                    try:
                        delta_y = int(float(delta))
                        if abs(delta_y) > 1000:
                            print(f"[TRACE] Step 26: Large scroll delta detected: {delta_y}")
                            logger.warning(f"Large scroll delta detected ({delta_y}), clamping value", extra={'email': self.email})
                            delta_y = min(max(delta_y, -1000), 1000)
                        # Use JS to increment scrollTop of the scroll section
                        scroll_js = f"""
                        (() => {{
                            const scrollSection = document.getElementById('scrollSection');
                            if (scrollSection) {{
                                scrollSection.scrollTop += {delta_y};
                                const newPos = scrollSection.scrollTop;
                                return {{ position: newPos, success: true }};
                            }}
                            return {{ position: null, success: false }};
                        }})()
                        """
                        result = await self.tab.evaluate(scroll_js)
                        print(f"[TRACE] Step 28: Scroll result: {result}")
                        if isinstance(result, list):
                            result = {k: (v['value'] if isinstance(v, dict) and 'value' in v else v) for k, v in result}
                            print("[TRACE] Step 28b: Converted result to dict", result)
                        if not result.get('success'):
                            print("[TRACE] Step 29: Failed to update scroll position")
                            logger.error("Failed to update scroll position", extra={'email': self.email})
                        current_pos = result.get('position')
                        if current_pos is not None:
                            print(f"[TRACE] Step 30: Scrolled to position: {current_pos}")
                            logger.debug(f"Scrolled to position: {current_pos}", extra={'email': self.email})
                    except (ValueError, TypeError) as e:
                        print(f"[TRACE] Step 27: Invalid scroll delta value: {e}")
                        logger.error(f"Invalid scroll delta value: {e}", extra={'email': self.email})
                        continue
                    if i < len(scroll_sequence.timings) - 1:
                        sleep_time = scroll_sequence.timings[i+1] - scroll_sequence.timings[i]
                        print(f"[TRACE] Step 31: Sleeping for {sleep_time}s")
                        await asyncio.sleep(sleep_time)
                    try:
                        if hasattr(scroll_sequence, 'pauses') and scroll_sequence.pauses and i < len(scroll_sequence.pauses):
                            pause_time = scroll_sequence.pauses[i]
                            if pause_time > 0:
                                print(f"[TRACE] Step 32: Pausing for {pause_time}s")
                                await asyncio.sleep(pause_time)
                    except (IndexError, AttributeError, TypeError) as e:
                        print(f"[TRACE] Step 33: Could not apply pause at index {i}: {e}")
                        logger.debug(f"Note: Could not apply pause at index {i}: {e}", extra={'email': self.email})
                final_position_js = "document.getElementById('scrollSection').scrollTop"
                final_position = await self.tab.evaluate(final_position_js)
                print(f"[TRACE] Step 34: Scroll completed. Final position: {final_position}")
                logger.info(f"Scroll completed. Final position: {final_position:.0f}", extra={'email': self.email})
                distance_to_target = abs(final_position - target_position)
                if distance_to_target < 100:
                    print(f"[TRACE] Step 35: Successfully scrolled near target (distance: {distance_to_target})")
                    logger.info(f"Successfully scrolled near target (distance: {distance_to_target:.0f}px)", extra={'email': self.email})
                else:
                    print(f"[TRACE] Step 36: Scroll completed but target not reached precisely (distance: {distance_to_target})")
                    logger.info(f"Scroll completed but target not reached precisely (distance: {distance_to_target:.0f}px)", extra={'email': self.email})
                return True
            else:
                print("[TRACE] Step 37: Fallback to basic scrolling")
                logger.warning("Human behavior model not available for scrolling. Using basic scrolling.", extra={'email': self.email})
                scroll_distance = target_position - current_position
                scroll_direction = 1 if scroll_distance > 0 else -1
                remaining_distance = abs(scroll_distance)
                while remaining_distance > 10:
                    chunk_size = min(remaining_distance * 0.3, 120) * scroll_direction
                    print(f"[TRACE] Step 38: Scrolling chunk_size: {chunk_size}")
                    await self.tab.mouse.wheel(delta_y=chunk_size)
                    remaining_distance -= abs(chunk_size)
                    print(f"[TRACE] Step 39: Remaining distance: {remaining_distance}")
                    await asyncio.sleep(random.uniform(0.05, 0.2))
                return True
        except Exception as e:
            print(f"[TRACE] Exception in human_like_scroll_to_random_target: {e}")
            import traceback
            print(traceback.format_exc())
            logger.error(f"Exception in human_like_scroll_to_random_target: {e}", extra={'email': self.email})
            logger.error(f"Stack trace: {traceback.format_exc()}", extra={'email': self.email})
            return False

    async def handle_code_entry_form(self):
        """
        Checks if the 'Enter your code' form is present and clicks 'Use your password' link if it is.
        Returns True if the form was handled, False otherwise.
        """
        try:
            code_title = await self.tab.find("Enter your code", timeout=5)
            if code_title:
                logger.info("Detected 'Enter your code' form. Attempting to click 'Use your password' link.", extra={'email': self.email})
                use_password_link = await self.click("Use your password", timeout=5)
                if use_password_link:
                    logger.info("Clicked 'Use your password' link.", extra={'email': self.email})
                    return True
                else:
                    logger.warning("Could not find 'Use your password' link on 'Enter your code' form.", extra={'email': self.email})
                    await log_error(self.email, "Could not find 'Use your password' link on 'Enter your code' form.")
                    return False
            return False
        except asyncio.TimeoutError:
            logger.warning("Timeout: 'Enter your code' form not detected within the given time.", extra={'email': self.email})
            return False
        except Exception as e:
            await log_error(self.email, f"Error handling code entry form: {e}")
            return False

    async def wait_for_points_breakdown_button(self, timeout=30):
        """
        Waits for the 'Points breakdown' button or similar element to appear,
        indicating a successful login to the rewards page.
        """
        logger.debug(f"Waiting for 'Points breakdown' button for up to {timeout} seconds...", extra={'email': self.email})
        points_button = await self.wait_for_element("Points breakdown", timeout=timeout)
        if points_button:
            logger.debug("Found 'Points breakdown' button.", extra={'email': self.email})
            return True
        
        points_button_alt = await self.wait_for_element("button[data-testid*='points-breakdown'], [aria-label*='points breakdown']", timeout=timeout)
        if points_button_alt:
            logger.debug("Found 'Points breakdown' button using alternative selector.", extra={'email': self.email})
            return True

        return False

    def _get_session_file_path(self, email):
        """Generates a unique session file path for an email."""
        email_hash = hashlib.sha256(email.encode('utf-8')).hexdigest()
        os.makedirs(self.session_dir, exist_ok=True)
        return os.path.join(self.session_dir, f"{email_hash}.session.dat")

    async def load_session_cookies(self, browser, email):
        """Loads cookies for a specific email from a session file."""
        if not browser:
            logger.warning("Browser not initialized. Cannot load session cookies.", extra={'email': self.email})
            return False

        session_file_path = self._get_session_file_path(email)
        try:
            await browser.cookies.load(session_file_path)
            return True
        except FileNotFoundError:
            logger.info(f"No existing session file found for {email}.", extra={'email': self.email})
            return False
        except Exception as e:
            await log_error(email, f"Error loading session cookies from {session_file_path}: {e}")
            return False

    async def save_session_cookies(self, browser, email):
        """Saves current browser cookies to a session file for a specific email."""
        if not browser:
            logger.warning("Browser not initialized. Cannot save session cookies.", extra={'email': self.email})
            return False
        session_file_path = self._get_session_file_path(email)
        await browser.cookies.save(session_file_path)
        logger.info(f"Session cookies saved for {email} to {session_file_path}", extra={'email': self.email})

    async def move_to_target(self, selector="#draggableTarget", timeout=30):
        """
        Move mouse to a target element for testing purposes.
        Returns True if successful, False otherwise.
        """
        element = await self.wait_for_element(selector, timeout=timeout)
        if element:
            # Get element position and scroll information in one call for accuracy
            try:
                element_info = await self.tab.evaluate("""
                    (element) => {
                        const rect = element.getBoundingClientRect();
                        const scrollX = window.pageXOffset || document.documentElement.scrollLeft || 0;
                        const scrollY = window.pageYOffset || document.documentElement.scrollTop || 0;
                        
                        return {
                            // Viewport coordinates (what get_position() returns)
                            viewportX: rect.left,
                            viewportY: rect.top,
                            width: rect.width,
                            height: rect.height,
                            // Absolute page coordinates (viewport + scroll)
                            absoluteX: rect.left + scrollX,
                            absoluteY: rect.top + scrollY,
                            scrollX: scrollX,
                            scrollY: scrollY
                        };
                    }
                """, element)
                
                if element_info and isinstance(element_info, dict):
                    # Use viewport coordinates for mouse movement
                    target_x = element_info['viewportX'] + element_info['width'] / 2
                    target_y = element_info['viewportY'] + element_info['height'] / 2
                    
                    logger.info(f"Moving to target - Viewport: ({element_info['viewportX']:.1f}, {element_info['viewportY']:.1f}), "
                               f"Absolute: ({element_info['absoluteX']:.1f}, {element_info['absoluteY']:.1f}), "
                               f"Scroll: ({element_info['scrollX']:.1f}, {element_info['scrollY']:.1f}), "
                               f"Target: ({target_x:.1f}, {target_y:.1f})", extra={'email': self.email})
                    
                    # Ensure coordinates are within reasonable bounds
                    target_x = max(0, min(1920, target_x))
                    target_y = max(0, min(1080, target_y))

                    await self.human_like_mouse_move(target_x, target_y)
                    return True
                else:
                    # Fallback to old method if JavaScript fails
                    pos = await element.get_position()
                    if pos:
                        target_x = pos.x + pos.width / 2
                        target_y = pos.y + pos.height / 2
                        await self.human_like_mouse_move(target_x, target_y)
                        logger.warning("Using fallback positioning method for move_to_target", extra={'email': self.email})
                        return True
                        
            except Exception as e:
                logger.warning(f"Failed to move to target: {e}. Using fallback.", extra={'email': self.email})
                # Fallback to old method
                pos = await element.get_position()
                if pos:
                    target_x = pos.x + pos.width / 2
                    target_y = pos.y + pos.height / 2
                    await self.human_like_mouse_move(target_x, target_y)
                    return True
        
        logger.error(f"Could not find target element with selector '{selector}'", extra={'email': self.email})
        return False