<a class="ds-card-sec ng-scope" ng-if="!item.sameTabNoRefresh"
    ng-href="https://www.bing.com/search?q=Bing%20news%20quiz&form=ML2BF3&OCID=ML2BF3&PUBL=RewardsDO&PROGRAMNAME=BingDailyOfferIN&CREA=ML2BF3"
    ng-click="$ctrl.onCardClick($event)" target="true" aria-disabled="false"
    aria-labelledby="card-title- card-desc- card-action-"
    aria-label="Take today's news quiz   See how you score on these questions   5 points   Activities section 4"
    href="https://www.bing.com/search?q=Bing%20news%20quiz&form=ML2BF3&OCID=ML2BF3&PUBL=RewardsDO&PROGRAMNAME=BingDailyOfferIN&CREA=ML2BF3">
    <#comment></#comment><mee-rewards-points complete="item.complete" is-in-progress="item.isInProgress"
        points="item.points" cms-data="$ctrl.cmsData" show-progress="$ctrl.item.isWot"
        point-progress="$ctrl.item.pointProgress"
        is-exclusive-locked-item="item.exclusiveLockedFeatureCardStatus == 'locked'"
        ng-mouseover="$ctrl.onHoverPoints($event)" ng-class="{inprogress : item.isInProgress}" class="ng-isolate-scope">
        <div class="points clearfix">
            <#comment></#comment>
            <div ng-if="!$ctrl.locked && !$ctrl.isExclusiveLockedItem" class="ng-scope">
                <#comment></#comment>
                <#comment></#comment><span class="mee-icon mee-icon-AddMedium"
                    ng-if="!$ctrl.iconImage && $ctrl.iconName" aria-label="Points you will earn" role="img"></span>
                <#comment></#comment>
                <#comment></#comment><span mee-heading="heading" ng-if="$ctrl.pointsString"
                    class="c-heading pointsString ng-binding ng-scope">5</span>
                <#comment></#comment>
            </div>
            <#comment></#comment>
            <#comment></#comment>
            <#comment></#comment>
        </div>
    </mee-rewards-points>
    <#comment></#comment>
    <div class="imageContainer"
        ng-class="{'x-hidden-vp1' : item.displayType === 'single' && !$ctrl.areaConfig.isMobileViewModeEnabled}">
        <#comment></#comment><mee-rewards-animation data="item.icon" ng-if="item.icon.source"
            is-exclusive-locked-item="item.exclusiveLockedFeatureCardStatus == 'locked'"
            class="ng-scope ng-isolate-scope">
            <picture class="rewards-animation-container">
                <#comment></#comment>
                <#comment></#comment>
                <div ng-if="$ctrl.isAnimation" class="rewardsBodymovin ng-scope"><svg xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 125 129" class="rewards-svg-renderer"
                        preserveAspectRatio="xMidYMid meet" aria-hidden="true" focusable="false">
                        <defs>
                            <clippath id="__lottie_element_1227">
                                <rect width="125" height="129" x="0" y="0"></rect>
                            </clippath>
                        </defs>
                        <g clip-path="url(#__lottie_element_1227)">
                            <g transform="matrix(0.****************,0,0,0.****************,18.205001831054688,15.***************)"
                                opacity="1" style="display: block;">
                            </g>
                            <g transform="matrix(0.6789399981498718,0,0,0.6578900218009949,12.616317749023438,34.04131317138672)"
                                opacity="1" style="display: block;">
                                <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                                    <path fill="rgb(184,144,105)" fill-opacity="1" d="M0 0"></path>
                                    <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                        stroke-miterlimit="4" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="0"
                                        d="M0 0"></path>
                                </g>
                            </g>
                            <g transform="matrix(1,0,0,1,160,64)" opacity="1" style="display: block;">
                                <g opacity="1" transform="matrix(1,0,0,1,-50.375,9.5)">
                                    <g opacity="1" transform="matrix(1,0,0,1,-96.875,-1)">
                                        <path fill="rgb(185,144,105)" fill-opacity="1"
                                            d=" M0,-7.25 C3.6563374996185303,-7.25 6.625,-4.001275062561035 6.625,0 C6.625,4.001275062561035 3.6563374996185303,7.25 0,7.25 C-3.6563374996185303,7.25 -6.625,4.001275062561035 -6.625,0 C-6.625,-4.001275062561035 -3.6563374996185303,-7.25 0,-7.25z">
                                        </path>
                                        <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                            stroke-miterlimit="4" stroke="rgb(0,120,215)" stroke-opacity="1"
                                            stroke-width="0"
                                            d=" M0,-7.25 C3.6563374996185303,-7.25 6.625,-4.001275062561035 6.625,0 C6.625,4.001275062561035 3.6563374996185303,7.25 0,7.25 C-3.6563374996185303,7.25 -6.625,4.001275062561035 -6.625,0 C-6.625,-4.001275062561035 -3.6563374996185303,-7.25 0,-7.25z">
                                        </path>
                                    </g>
                                    <path fill="rgb(185,144,105)" fill-opacity="1"
                                        d=" M0,-7.25 C3.6563374996185303,-7.25 6.625,-4.001275062561035 6.625,0 C6.625,4.001275062561035 3.6563374996185303,7.25 0,7.25 C-3.6563374996185303,7.25 -6.625,4.001275062561035 -6.625,0 C-6.625,-4.001275062561035 -3.6563374996185303,-7.25 0,-7.25z">
                                    </path>
                                    <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                        stroke-miterlimit="4" stroke="rgb(0,120,215)" stroke-opacity="1"
                                        stroke-width="0"
                                        d=" M0,-7.25 C3.6563374996185303,-7.25 6.625,-4.001275062561035 6.625,0 C6.625,4.001275062561035 3.6563374996185303,7.25 0,7.25 C-3.6563374996185303,7.25 -6.625,4.001275062561035 -6.625,0 C-6.625,-4.001275062561035 -3.6563374996185303,-7.25 0,-7.25z">
                                    </path>
                                </g>
                                <g opacity="1" transform="matrix(1,0,0,1,-147.5,0.5619999766349792)">
                                    <path fill="rgb(185,144,105)" fill-opacity="1"
                                        d=" M0,-3.0625 C1.****************,-3.0625 2.75,-1.690193772315979 2.75,0 C2.75,1.690193772315979 1.****************,3.0625 0,3.0625 C-1.****************,3.0625 -2.75,1.690193772315979 -2.75,0 C-2.75,-1.690193772315979 -1.****************,-3.0625 0,-3.0625z">
                                    </path>
                                    <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                        stroke-miterlimit="4" stroke="rgb(0,120,215)" stroke-opacity="1"
                                        stroke-width="0"
                                        d=" M0,-3.0625 C1.****************,-3.0625 2.75,-1.690193772315979 2.75,0 C2.75,1.690193772315979 1.****************,3.0625 0,3.0625 C-1.****************,3.0625 -2.75,1.690193772315979 -2.75,0 C-2.75,-1.690193772315979 -1.****************,-3.0625 0,-3.0625z">
                                    </path>
                                </g>
                                <g opacity="1" transform="matrix(1,0,0,1,-49,2.375)">
                                    <path fill="rgb(185,144,105)" fill-opacity="1"
                                        d=" M0,-2.75 C1.****************,-2.75 2.75,-1.**************** 2.75,0 C2.75,1.**************** 1.****************,2.75 0,2.75 C-1.****************,2.75 -2.75,1.**************** -2.75,0 C-2.75,-1.**************** -1.****************,-2.75 0,-2.75z">
                                    </path>
                                    <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                        stroke-miterlimit="4" stroke="rgb(0,120,215)" stroke-opacity="1"
                                        stroke-width="0"
                                        d=" M0,-2.75 C1.****************,-2.75 2.75,-1.**************** 2.75,0 C2.75,1.**************** 1.****************,2.75 0,2.75 C-1.****************,2.75 -2.75,1.**************** -2.75,0 C-2.75,-1.**************** -1.****************,-2.75 0,-2.75z">
                                    </path>
                                </g>
                            </g>
                        </g>
                    </svg></div>
                <#comment></#comment>
            </picture>
        </mee-rewards-animation>
        <#comment></#comment>
    </div>
    <div class="contentContainer">
        <h3 id="card-title-" mee-heading="heading" class="c-heading ng-binding">Take today's news quiz</h3>
        <p mee-paragraph="para4"
            ng-class="{'compact' : $ctrl.item.isWot && ($ctrl.item.pointProgress > 0 || $ctrl.item.complete)}"
            class="ng-binding c-paragraph-4">See how you score on these questions</p>
        <#comment></#comment>
        <#comment></#comment>
        <#comment></#comment>
        <div class="actionLink x-hidden-vp2 x-hidden-vp3 x-hidden-vp4 x-hidden-vp5 ng-scope"
            ng-if="$ctrl.areaConfig.isMobileViewModeEnabled && item.exclusiveLockedFeatureCardStatus != 'locked'"><span
                id="card-action-" class="pointLink ng-binding">
                5 points >
            </span></div>
        <#comment></#comment>
        <#comment></#comment>
        <#comment></#comment>
        <#comment></#comment>
    </div>
    <#comment></#comment>
    <div id="ma-card-link" class="actionLink ng-scope x-hidden-vp1"
        ng-class="{'x-hidden-vp1' : $ctrl.areaConfig.isMobileViewModeEnabled}" ng-hide="$ctrl.isDenseDashboard()"
        ng-if="item.exclusiveLockedFeatureCardStatus != 'locked'"><span id="card-action-" class="pointLink ng-binding">
            5 points >
        </span></div>
    <#comment></#comment>
    <#comment></#comment><mee-rewards-debug-info item="$ctrl.item" class="ng-isolate-scope">
        <#comment></#comment>
    </mee-rewards-debug-info>
</a>' for up to 30 seconds...
2025-06-27 12:02:43,757 - RewardsBotLogger - ERROR - [<EMAIL>] - Email: <EMAIL> - Error:
Error waiting for element '<a class="ds-card-sec ng-scope" ng-if="!item.sameTabNoRefresh"
    ng-href="https://www.bing.com/search?q=Bing%20news%20quiz&form=ML2BF3&OCID=ML2BF3&PUBL=RewardsDO&PROGRAMNAME=BingDailyOfferIN&CREA=ML2BF3"
    ng-click="$ctrl.onCardClick($event)" target="true" aria-disabled="false"
    aria-labelledby="card-title- card-desc- card-action-"
    aria-label="Take today's news quiz   See how you score on these questions   5 points   Activities section 4"
    href="https://www.bing.com/search?q=Bing%20news%20quiz&form=ML2BF3&OCID=ML2BF3&PUBL=RewardsDO&PROGRAMNAME=BingDailyOfferIN&CREA=ML2BF3">
    <#comment></#comment><mee-rewards-points complete="item.complete" is-in-progress="item.isInProgress"
        points="item.points" cms-data="$ctrl.cmsData" show-progress="$ctrl.item.isWot"
        point-progress="$ctrl.item.pointProgress"
        is-exclusive-locked-item="item.exclusiveLockedFeatureCardStatus == 'locked'"
        ng-mouseover="$ctrl.onHoverPoints($event)" ng-class="{inprogress : item.isInProgress}" class="ng-isolate-scope">
        <div class="points clearfix">
            <#comment></#comment>
            <div ng-if="!$ctrl.locked && !$ctrl.isExclusiveLockedItem" class="ng-scope">
                <#comment></#comment>
                <#comment></#comment><span class="mee-icon mee-icon-AddMedium"
                    ng-if="!$ctrl.iconImage && $ctrl.iconName" aria-label="Points you will earn" role="img"></span>
                <#comment></#comment>
                <#comment></#comment><span mee-heading="heading" ng-if="$ctrl.pointsString"
                    class="c-heading pointsString ng-binding ng-scope">5</span>
                <#comment></#comment>
            </div>
            <#comment></#comment>
            <#comment></#comment>
            <#comment></#comment>
        </div>
    </mee-rewards-points>
    <#comment></#comment>
    <div class="imageContainer"
        ng-class="{'x-hidden-vp1' : item.displayType === 'single' && !$ctrl.areaConfig.isMobileViewModeEnabled}">
        <#comment></#comment><mee-rewards-animation data="item.icon" ng-if="item.icon.source"
            is-exclusive-locked-item="item.exclusiveLockedFeatureCardStatus == 'locked'"
            class="ng-scope ng-isolate-scope">
            <picture class="rewards-animation-container">
                <#comment></#comment>
                <#comment></#comment>
                <div ng-if="$ctrl.isAnimation" class="rewardsBodymovin ng-scope"><svg xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 125 129" class="rewards-svg-renderer"
                        preserveAspectRatio="xMidYMid meet" aria-hidden="true" focusable="false">
                        <defs>
                            <clippath id="__lottie_element_1227">
                                <rect width="125" height="129" x="0" y="0"></rect>
                            </clippath>
                        </defs>
                        <g clip-path="url(#__lottie_element_1227)">
                            <g transform="matrix(0.****************,0,0,0.****************,18.205001831054688,15.***************)"
                                opacity="1" style="display: block;">
                            </g>
                            <g transform="matrix(0.6789399981498718,0,0,0.6578900218009949,12.616317749023438,34.04131317138672)"
                                opacity="1" style="display: block;">
                                <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                                    <path fill="rgb(184,144,105)" fill-opacity="1" d="M0 0"></path>
                                    <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                        stroke-miterlimit="4" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="0"
                                        d="M0 0"></path>
                                </g>
                            </g>
                            <g transform="matrix(1,0,0,1,160,64)" opacity="1" style="display: block;">
                                <g opacity="1" transform="matrix(1,0,0,1,-50.375,9.5)">
                                    <g opacity="1" transform="matrix(1,0,0,1,-96.875,-1)">
                                        <path fill="rgb(185,144,105)" fill-opacity="1"
                                            d=" M0,-7.25 C3.6563374996185303,-7.25 6.625,-4.001275062561035 6.625,0 C6.625,4.001275062561035 3.6563374996185303,7.25 0,7.25 C-3.6563374996185303,7.25 -6.625,4.001275062561035 -6.625,0 C-6.625,-4.001275062561035 -3.6563374996185303,-7.25 0,-7.25z">
                                        </path>
                                        <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                            stroke-miterlimit="4" stroke="rgb(0,120,215)" stroke-opacity="1"
                                            stroke-width="0"
                                            d=" M0,-7.25 C3.6563374996185303,-7.25 6.625,-4.001275062561035 6.625,0 C6.625,4.001275062561035 3.6563374996185303,7.25 0,7.25 C-3.6563374996185303,7.25 -6.625,4.001275062561035 -6.625,0 C-6.625,-4.001275062561035 -3.6563374996185303,-7.25 0,-7.25z">
                                        </path>
                                    </g>
                                    <path fill="rgb(185,144,105)" fill-opacity="1"
                                        d=" M0,-7.25 C3.6563374996185303,-7.25 6.625,-4.001275062561035 6.625,0 C6.625,4.001275062561035 3.6563374996185303,7.25 0,7.25 C-3.6563374996185303,7.25 -6.625,4.001275062561035 -6.625,0 C-6.625,-4.001275062561035 -3.6563374996185303,-7.25 0,-7.25z">
                                    </path>
                                    <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                        stroke-miterlimit="4" stroke="rgb(0,120,215)" stroke-opacity="1"
                                        stroke-width="0"
                                        d=" M0,-7.25 C3.6563374996185303,-7.25 6.625,-4.001275062561035 6.625,0 C6.625,4.001275062561035 3.6563374996185303,7.25 0,7.25 C-3.6563374996185303,7.25 -6.625,4.001275062561035 -6.625,0 C-6.625,-4.001275062561035 -3.6563374996185303,-7.25 0,-7.25z">
                                    </path>
                                </g>
                                <g opacity="1" transform="matrix(1,0,0,1,-147.5,0.5619999766349792)">
                                    <path fill="rgb(185,144,105)" fill-opacity="1"
                                        d=" M0,-3.0625 C1.****************,-3.0625 2.75,-1.690193772315979 2.75,0 C2.75,1.690193772315979 1.****************,3.0625 0,3.0625 C-1.****************,3.0625 -2.75,1.690193772315979 -2.75,0 C-2.75,-1.690193772315979 -1.****************,-3.0625 0,-3.0625z">
                                    </path>
                                    <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                        stroke-miterlimit="4" stroke="rgb(0,120,215)" stroke-opacity="1"
                                        stroke-width="0"
                                        d=" M0,-3.0625 C1.****************,-3.0625 2.75,-1.690193772315979 2.75,0 C2.75,1.690193772315979 1.****************,3.0625 0,3.0625 C-1.****************,3.0625 -2.75,1.690193772315979 -2.75,0 C-2.75,-1.690193772315979 -1.****************,-3.0625 0,-3.0625z">
                                    </path>
                                </g>
                                <g opacity="1" transform="matrix(1,0,0,1,-49,2.375)">
                                    <path fill="rgb(185,144,105)" fill-opacity="1"
                                        d=" M0,-2.75 C1.****************,-2.75 2.75,-1.**************** 2.75,0 C2.75,1.**************** 1.****************,2.75 0,2.75 C-1.****************,2.75 -2.75,1.**************** -2.75,0 C-2.75,-1.**************** -1.****************,-2.75 0,-2.75z">
                                    </path>
                                    <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                        stroke-miterlimit="4" stroke="rgb(0,120,215)" stroke-opacity="1"
                                        stroke-width="0"
                                        d=" M0,-2.75 C1.****************,-2.75 2.75,-1.**************** 2.75,0 C2.75,1.**************** 1.****************,2.75 0,2.75 C-1.****************,2.75 -2.75,1.**************** -2.75,0 C-2.75,-1.**************** -1.****************,-2.75 0,-2.75z">
                                    </path>
                                </g>
                            </g>
                        </g>
                    </svg></div>
                <#comment></#comment>
            </picture>
        </mee-rewards-animation>
        <#comment></#comment>
    </div>
    <div class="contentContainer">
        <h3 id="card-title-" mee-heading="heading" class="c-heading ng-binding">Take today's news quiz</h3>
        <p mee-paragraph="para4"
            ng-class="{'compact' : $ctrl.item.isWot && ($ctrl.item.pointProgress > 0 || $ctrl.item.complete)}"
            class="ng-binding c-paragraph-4">See how you score on these questions</p>
        <#comment></#comment>
        <#comment></#comment>
        <#comment></#comment>
        <div class="actionLink x-hidden-vp2 x-hidden-vp3 x-hidden-vp4 x-hidden-vp5 ng-scope"
            ng-if="$ctrl.areaConfig.isMobileViewModeEnabled && item.exclusiveLockedFeatureCardStatus != 'locked'"><span
                id="card-action-" class="pointLink ng-binding">
                5 points >
            </span></div>
        <#comment></#comment>
        <#comment></#comment>
        <#comment></#comment>
        <#comment></#comment>
    </div>
    <#comment></#comment>
    <div id="ma-card-link" class="actionLink ng-scope x-hidden-vp1"
        ng-class="{'x-hidden-vp1' : $ctrl.areaConfig.isMobileViewModeEnabled}" ng-hide="$ctrl.isDenseDashboard()"
        ng-if="item.exclusiveLockedFeatureCardStatus != 'locked'"><span id="card-action-" class="pointLink ng-binding">
            5 points >
        </span></div>
    <#comment></#comment>
    <#comment></#comment><mee-rewards-debug-info item="$ctrl.item" class="ng-isolate-scope">
        <#comment></#comment>
    </mee-rewards-debug-info>
</a>': 'NoneType' object is not callable
2025-06-27 12:02:43,791 - RewardsBotLogger - INFO - [<EMAIL>] - Error HTML saved to:
errors_html\error_page_nexbytehacks@gmail.com_2025-06-27_12-02-43.html
2025-06-27 12:02:43,816 - RewardsBotLogger - ERROR - [<EMAIL>] - Error during wait for element '<a
    class="ds-card-sec ng-scope" ng-if="!item.sameTabNoRefresh"
    ng-href="https://www.bing.com/search?q=Bing%20news%20quiz&form=ML2BF3&OCID=ML2BF3&PUBL=RewardsDO&PROGRAMNAME=BingDailyOfferIN&CREA=ML2BF3"
    ng-click="$ctrl.onCardClick($event)" target="true" aria-disabled="false"
    aria-labelledby="card-title- card-desc- card-action-"
    aria-label="Take today's news quiz   See how you score on these questions   5 points   Activities section 4"
    href="https://www.bing.com/search?q=Bing%20news%20quiz&form=ML2BF3&OCID=ML2BF3&PUBL=RewardsDO&PROGRAMNAME=BingDailyOfferIN&CREA=ML2BF3">
    <#comment></#comment><mee-rewards-points complete="item.complete" is-in-progress="item.isInProgress"
        points="item.points" cms-data="$ctrl.cmsData" show-progress="$ctrl.item.isWot"
        point-progress="$ctrl.item.pointProgress"
        is-exclusive-locked-item="item.exclusiveLockedFeatureCardStatus == 'locked'"
        ng-mouseover="$ctrl.onHoverPoints($event)" ng-class="{inprogress : item.isInProgress}" class="ng-isolate-scope">
        <div class="points clearfix">
            <#comment></#comment>
            <div ng-if="!$ctrl.locked && !$ctrl.isExclusiveLockedItem" class="ng-scope">
                <#comment></#comment>
                <#comment></#comment><span class="mee-icon mee-icon-AddMedium"
                    ng-if="!$ctrl.iconImage && $ctrl.iconName" aria-label="Points you will earn" role="img"></span>
                <#comment></#comment>
                <#comment></#comment><span mee-heading="heading" ng-if="$ctrl.pointsString"
                    class="c-heading pointsString ng-binding ng-scope">5</span>
                <#comment></#comment>
            </div>
            <#comment></#comment>
            <#comment></#comment>
            <#comment></#comment>
        </div>
    </mee-rewards-points>
    <#comment></#comment>
    <div class="imageContainer"
        ng-class="{'x-hidden-vp1' : item.displayType === 'single' && !$ctrl.areaConfig.isMobileViewModeEnabled}">
        <#comment></#comment><mee-rewards-animation data="item.icon" ng-if="item.icon.source"
            is-exclusive-locked-item="item.exclusiveLockedFeatureCardStatus == 'locked'"
            class="ng-scope ng-isolate-scope">
            <picture class="rewards-animation-container">
                <#comment></#comment>
                <#comment></#comment>
                <div ng-if="$ctrl.isAnimation" class="rewardsBodymovin ng-scope"><svg xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 125 129" class="rewards-svg-renderer"
                        preserveAspectRatio="xMidYMid meet" aria-hidden="true" focusable="false">
                        <defs>
                            <clippath id="__lottie_element_1227">
                                <rect width="125" height="129" x="0" y="0"></rect>
                            </clippath>
                        </defs>
                        <g clip-path="url(#__lottie_element_1227)">
                            <g transform="matrix(0.****************,0,0,0.****************,18.205001831054688,15.***************)"
                                opacity="1" style="display: block;">
                            </g>
                            <g transform="matrix(0.6789399981498718,0,0,0.6578900218009949,12.616317749023438,34.04131317138672)"
                                opacity="1" style="display: block;">
                                <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                                    <path fill="rgb(184,144,105)" fill-opacity="1" d="M0 0"></path>
                                    <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                        stroke-miterlimit="4" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="0"
                                        d="M0 0"></path>
                                </g>
                            </g>
                            <g transform="matrix(1,0,0,1,160,64)" opacity="1" style="display: block;">
                                <g opacity="1" transform="matrix(1,0,0,1,-50.375,9.5)">
                                    <g opacity="1" transform="matrix(1,0,0,1,-96.875,-1)">
                                        <path fill="rgb(185,144,105)" fill-opacity="1"
                                            d=" M0,-7.25 C3.6563374996185303,-7.25 6.625,-4.001275062561035 6.625,0 C6.625,4.001275062561035 3.6563374996185303,7.25 0,7.25 C-3.6563374996185303,7.25 -6.625,4.001275062561035 -6.625,0 C-6.625,-4.001275062561035 -3.6563374996185303,-7.25 0,-7.25z">
                                        </path>
                                        <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                            stroke-miterlimit="4" stroke="rgb(0,120,215)" stroke-opacity="1"
                                            stroke-width="0"
                                            d=" M0,-7.25 C3.6563374996185303,-7.25 6.625,-4.001275062561035 6.625,0 C6.625,4.001275062561035 3.6563374996185303,7.25 0,7.25 C-3.6563374996185303,7.25 -6.625,4.001275062561035 -6.625,0 C-6.625,-4.001275062561035 -3.6563374996185303,-7.25 0,-7.25z">
                                        </path>
                                    </g>
                                    <path fill="rgb(185,144,105)" fill-opacity="1"
                                        d=" M0,-7.25 C3.6563374996185303,-7.25 6.625,-4.001275062561035 6.625,0 C6.625,4.001275062561035 3.6563374996185303,7.25 0,7.25 C-3.6563374996185303,7.25 -6.625,4.001275062561035 -6.625,0 C-6.625,-4.001275062561035 -3.6563374996185303,-7.25 0,-7.25z">
                                    </path>
                                    <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                        stroke-miterlimit="4" stroke="rgb(0,120,215)" stroke-opacity="1"
                                        stroke-width="0"
                                        d=" M0,-7.25 C3.6563374996185303,-7.25 6.625,-4.001275062561035 6.625,0 C6.625,4.001275062561035 3.6563374996185303,7.25 0,7.25 C-3.6563374996185303,7.25 -6.625,4.001275062561035 -6.625,0 C-6.625,-4.001275062561035 -3.6563374996185303,-7.25 0,-7.25z">
                                    </path>
                                </g>
                                <g opacity="1" transform="matrix(1,0,0,1,-147.5,0.5619999766349792)">
                                    <path fill="rgb(185,144,105)" fill-opacity="1"
                                        d=" M0,-3.0625 C1.****************,-3.0625 2.75,-1.690193772315979 2.75,0 C2.75,1.690193772315979 1.****************,3.0625 0,3.0625 C-1.****************,3.0625 -2.75,1.690193772315979 -2.75,0 C-2.75,-1.690193772315979 -1.****************,-3.0625 0,-3.0625z">
                                    </path>
                                    <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                        stroke-miterlimit="4" stroke="rgb(0,120,215)" stroke-opacity="1"
                                        stroke-width="0"
                                        d=" M0,-3.0625 C1.****************,-3.0625 2.75,-1.690193772315979 2.75,0 C2.75,1.690193772315979 1.****************,3.0625 0,3.0625 C-1.****************,3.0625 -2.75,1.690193772315979 -2.75,0 C-2.75,-1.690193772315979 -1.****************,-3.0625 0,-3.0625z">
                                    </path>
                                </g>
                                <g opacity="1" transform="matrix(1,0,0,1,-49,2.375)">
                                    <path fill="rgb(185,144,105)" fill-opacity="1"
                                        d=" M0,-2.75 C1.****************,-2.75 2.75,-1.**************** 2.75,0 C2.75,1.**************** 1.****************,2.75 0,2.75 C-1.****************,2.75 -2.75,1.**************** -2.75,0 C-2.75,-1.**************** -1.****************,-2.75 0,-2.75z">
                                    </path>
                                    <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                        stroke-miterlimit="4" stroke="rgb(0,120,215)" stroke-opacity="1"
                                        stroke-width="0"
                                        d=" M0,-2.75 C1.****************,-2.75 2.75,-1.**************** 2.75,0 C2.75,1.**************** 1.****************,2.75 0,2.75 C-1.****************,2.75 -2.75,1.**************** -2.75,0 C-2.75,-1.**************** -1.****************,-2.75 0,-2.75z">
                                    </path>
                                </g>
                            </g>
                        </g>
                    </svg></div>
                <#comment></#comment>
            </picture>
        </mee-rewards-animation>
        <#comment></#comment>
    </div>
    <div class="contentContainer">
        <h3 id="card-title-" mee-heading="heading" class="c-heading ng-binding">Take today's news quiz</h3>
        <p mee-paragraph="para4"
            ng-class="{'compact' : $ctrl.item.isWot && ($ctrl.item.pointProgress > 0 || $ctrl.item.complete)}"
            class="ng-binding c-paragraph-4">See how you score on these questions</p>
        <#comment></#comment>
        <#comment></#comment>
        <#comment></#comment>
        <div class="actionLink x-hidden-vp2 x-hidden-vp3 x-hidden-vp4 x-hidden-vp5 ng-scope"
            ng-if="$ctrl.areaConfig.isMobileViewModeEnabled && item.exclusiveLockedFeatureCardStatus != 'locked'"><span
                id="card-action-" class="pointLink ng-binding">
                5 points >
            </span></div>
        <#comment></#comment>
        <#comment></#comment>
        <#comment></#comment>
        <#comment></#comment>
    </div>
    <#comment></#comment>
    <div id="ma-card-link" class="actionLink ng-scope x-hidden-vp1"
        ng-class="{'x-hidden-vp1' : $ctrl.areaConfig.isMobileViewModeEnabled}" ng-hide="$ctrl.isDenseDashboard()"
        ng-if="item.exclusiveLockedFeatureCardStatus != 'locked'"><span id="card-action-" class="pointLink ng-binding">
            5 points >
        </span></div>
    <#comment></#comment>
    <#comment></#comment><mee-rewards-debug-info item="$ctrl.item" class="ng-isolate-scope">
        <#comment></#comment>
    </mee-rewards-debug-info>
</a>': 'NoneType' object is not callable
2025-06-27 12:02:43,878 - RewardsBotLogger - WARNING - [<EMAIL>] - Element with selector '<a
    class="ds-card-sec ng-scope" ng-if="!item.sameTabNoRefresh"
    ng-href="https://www.bing.com/search?q=Bing%20news%20quiz&form=ML2BF3&OCID=ML2BF3&PUBL=RewardsDO&PROGRAMNAME=BingDailyOfferIN&CREA=ML2BF3"
    ng-click="$ctrl.onCardClick($event)" target="true" aria-disabled="false"
    aria-labelledby="card-title- card-desc- card-action-"
    aria-label="Take today's news quiz   See how you score on these questions   5 points   Activities section 4"
    href="https://www.bing.com/search?q=Bing%20news%20quiz&form=ML2BF3&OCID=ML2BF3&PUBL=RewardsDO&PROGRAMNAME=BingDailyOfferIN&CREA=ML2BF3">
    <#comment></#comment><mee-rewards-points complete="item.complete" is-in-progress="item.isInProgress"
        points="item.points" cms-data="$ctrl.cmsData" show-progress="$ctrl.item.isWot"
        point-progress="$ctrl.item.pointProgress"
        is-exclusive-locked-item="item.exclusiveLockedFeatureCardStatus == 'locked'"
        ng-mouseover="$ctrl.onHoverPoints($event)" ng-class="{inprogress : item.isInProgress}" class="ng-isolate-scope">
        <div class="points clearfix">
            <#comment></#comment>
            <div ng-if="!$ctrl.locked && !$ctrl.isExclusiveLockedItem" class="ng-scope">
                <#comment></#comment>
                <#comment></#comment><span class="mee-icon mee-icon-AddMedium"
                    ng-if="!$ctrl.iconImage && $ctrl.iconName" aria-label="Points you will earn" role="img"></span>
                <#comment></#comment>
                <#comment></#comment><span mee-heading="heading" ng-if="$ctrl.pointsString"
                    class="c-heading pointsString ng-binding ng-scope">5</span>
                <#comment></#comment>
            </div>
            <#comment></#comment>
            <#comment></#comment>
            <#comment></#comment>
        </div>
    </mee-rewards-points>
    <#comment></#comment>
    <div class="imageContainer"
        ng-class="{'x-hidden-vp1' : item.displayType === 'single' && !$ctrl.areaConfig.isMobileViewModeEnabled}">
        <#comment></#comment><mee-rewards-animation data="item.icon" ng-if="item.icon.source"
            is-exclusive-locked-item="item.exclusiveLockedFeatureCardStatus == 'locked'"
            class="ng-scope ng-isolate-scope">
            <picture class="rewards-animation-container">
                <#comment></#comment>
                <#comment></#comment>
                <div ng-if="$ctrl.isAnimation" class="rewardsBodymovin ng-scope"><svg xmlns="http://www.w3.org/2000/svg"
                        xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 125 129" class="rewards-svg-renderer"
                        preserveAspectRatio="xMidYMid meet" aria-hidden="true" focusable="false">
                        <defs>
                            <clippath id="__lottie_element_1227">
                                <rect width="125" height="129" x="0" y="0"></rect>
                            </clippath>
                        </defs>
                        <g clip-path="url(#__lottie_element_1227)">
                            <g transform="matrix(0.****************,0,0,0.****************,18.205001831054688,15.***************)"
                                opacity="1" style="display: block;">
                            </g>
                            <g transform="matrix(0.6789399981498718,0,0,0.6578900218009949,12.616317749023438,34.04131317138672)"
                                opacity="1" style="display: block;">
                                <g opacity="1" transform="matrix(1,0,0,1,0,0)">
                                    <path fill="rgb(184,144,105)" fill-opacity="1" d="M0 0"></path>
                                    <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                        stroke-miterlimit="4" stroke="rgb(0,0,0)" stroke-opacity="1" stroke-width="0"
                                        d="M0 0"></path>
                                </g>
                            </g>
                            <g transform="matrix(1,0,0,1,160,64)" opacity="1" style="display: block;">
                                <g opacity="1" transform="matrix(1,0,0,1,-50.375,9.5)">
                                    <g opacity="1" transform="matrix(1,0,0,1,-96.875,-1)">
                                        <path fill="rgb(185,144,105)" fill-opacity="1"
                                            d=" M0,-7.25 C3.6563374996185303,-7.25 6.625,-4.001275062561035 6.625,0 C6.625,4.001275062561035 3.6563374996185303,7.25 0,7.25 C-3.6563374996185303,7.25 -6.625,4.001275062561035 -6.625,0 C-6.625,-4.001275062561035 -3.6563374996185303,-7.25 0,-7.25z">
                                        </path>
                                        <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                            stroke-miterlimit="4" stroke="rgb(0,120,215)" stroke-opacity="1"
                                            stroke-width="0"
                                            d=" M0,-7.25 C3.6563374996185303,-7.25 6.625,-4.001275062561035 6.625,0 C6.625,4.001275062561035 3.6563374996185303,7.25 0,7.25 C-3.6563374996185303,7.25 -6.625,4.001275062561035 -6.625,0 C-6.625,-4.001275062561035 -3.6563374996185303,-7.25 0,-7.25z">
                                        </path>
                                    </g>
                                    <path fill="rgb(185,144,105)" fill-opacity="1"
                                        d=" M0,-7.25 C3.6563374996185303,-7.25 6.625,-4.001275062561035 6.625,0 C6.625,4.001275062561035 3.6563374996185303,7.25 0,7.25 C-3.6563374996185303,7.25 -6.625,4.001275062561035 -6.625,0 C-6.625,-4.001275062561035 -3.6563374996185303,-7.25 0,-7.25z">
                                    </path>
                                    <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                        stroke-miterlimit="4" stroke="rgb(0,120,215)" stroke-opacity="1"
                                        stroke-width="0"
                                        d=" M0,-7.25 C3.6563374996185303,-7.25 6.625,-4.001275062561035 6.625,0 C6.625,4.001275062561035 3.6563374996185303,7.25 0,7.25 C-3.6563374996185303,7.25 -6.625,4.001275062561035 -6.625,0 C-6.625,-4.001275062561035 -3.6563374996185303,-7.25 0,-7.25z">
                                    </path>
                                </g>
                                <g opacity="1" transform="matrix(1,0,0,1,-147.5,0.5619999766349792)">
                                    <path fill="rgb(185,144,105)" fill-opacity="1"
                                        d=" M0,-3.0625 C1.****************,-3.0625 2.75,-1.690193772315979 2.75,0 C2.75,1.690193772315979 1.****************,3.0625 0,3.0625 C-1.****************,3.0625 -2.75,1.690193772315979 -2.75,0 C-2.75,-1.690193772315979 -1.****************,-3.0625 0,-3.0625z">
                                    </path>
                                    <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                        stroke-miterlimit="4" stroke="rgb(0,120,215)" stroke-opacity="1"
                                        stroke-width="0"
                                        d=" M0,-3.0625 C1.****************,-3.0625 2.75,-1.690193772315979 2.75,0 C2.75,1.690193772315979 1.****************,3.0625 0,3.0625 C-1.****************,3.0625 -2.75,1.690193772315979 -2.75,0 C-2.75,-1.690193772315979 -1.****************,-3.0625 0,-3.0625z">
                                    </path>
                                </g>
                                <g opacity="1" transform="matrix(1,0,0,1,-49,2.375)">
                                    <path fill="rgb(185,144,105)" fill-opacity="1"
                                        d=" M0,-2.75 C1.****************,-2.75 2.75,-1.**************** 2.75,0 C2.75,1.**************** 1.****************,2.75 0,2.75 C-1.****************,2.75 -2.75,1.**************** -2.75,0 C-2.75,-1.**************** -1.****************,-2.75 0,-2.75z">
                                    </path>
                                    <path stroke-linecap="butt" stroke-linejoin="miter" fill-opacity="0"
                                        stroke-miterlimit="4" stroke="rgb(0,120,215)" stroke-opacity="1"
                                        stroke-width="0"
                                        d=" M0,-2.75 C1.****************,-2.75 2.75,-1.**************** 2.75,0 C2.75,1.**************** 1.****************,2.75 0,2.75 C-1.****************,2.75 -2.75,1.**************** -2.75,0 C-2.75,-1.**************** -1.****************,-2.75 0,-2.75z">
                                    </path>
                                </g>
                            </g>
                        </g>
                    </svg></div>
                <#comment></#comment>
            </picture>
        </mee-rewards-animation>
        <#comment></#comment>
    </div>
    <div class="contentContainer">
        <h3 id="card-title-" mee-heading="heading" class="c-heading ng-binding">Take today's news quiz</h3>
        <p mee-paragraph="para4"
            ng-class="{'compact' : $ctrl.item.isWot && ($ctrl.item.pointProgress > 0 || $ctrl.item.complete)}"
            class="ng-binding c-paragraph-4">See how you score on these questions</p>
        <#comment></#comment>
        <#comment></#comment>
        <#comment></#comment>
        <div class="actionLink x-hidden-vp2 x-hidden-vp3 x-hidden-vp4 x-hidden-vp5 ng-scope"
            ng-if="$ctrl.areaConfig.isMobileViewModeEnabled && item.exclusiveLockedFeatureCardStatus != 'locked'"><span
                id="card-action-" class="pointLink ng-binding">
                5 points >
            </span></div>
        <#comment></#comment>
        <#comment></#comment>
        <#comment></#comment>
        <#comment></#comment>
    </div>
    <#comment></#comment>
    <div id="ma-card-link" class="actionLink ng-scope x-hidden-vp1"
        ng-class="{'x-hidden-vp1' : $ctrl.areaConfig.isMobileViewModeEnabled}" ng-hide="$ctrl.isDenseDashboard()"
        ng-if="item.exclusiveLockedFeatureCardStatus != 'locked'"><span id="card-action-" class="pointLink ng-binding">
            5 points >
        </span></div>
    <#comment></#comment>
    <#comment></#comment><mee-rewards-debug-info item="$ctrl.item" class="ng-isolate-scope">
        <#comment></#comment>
    </mee-rewards-debug-info>
</a>