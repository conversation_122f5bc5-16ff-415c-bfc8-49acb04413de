<!DOCTYPE html><html dir="ltr" lang="en-US" data-role-name="RewardsPortal" class="ltr rewards-dashboard rewards js picture eventlistener"><head>
    <title>Microsoft Rewards </title>

    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="format-detection" content="telephone=no">
        <meta name="description" content="Reward Page description!">
        <meta name="og:description" content="Reward Page description!">
        <meta name="og:title" content="Microsoft Rewards ">
        <meta name="pageid" content="rewards-dashboard">


    <link rel="canonical" href="https://rewards.bing.com/">
    <link href="/rewards.png" rel="shortcut icon" type="image/png" sizes="any">

    
    <meta name="noAutoPageView" content="1">

    <link rel="stylesheet" href="https://msrewardspme.azureedge.net/rewardsuxassets/amcassets/css/amc-mwf-bundle-1-5a76310edb-v1.57.8.css" nonce="">
    <link rel="stylesheet" href="https://msrewardspme.azureedge.net/rewardsuxassets/amcassets/css/site_oneui-b86f67fbd4-v1.css" nonce="">
    <link rel="stylesheet" href="https://msrewardspme.azureedge.net/rewardsuxassets/amcassets/css/rewardscommon-6fcc8e61c2-v1.css" nonce="">
    <link rel="stylesheet" href="https://msrewardspme.azureedge.net/rewardsuxassets/amcassets/css/rewards_oneui-cb18b7c061-v1.css" nonce="">

    


    <script crossorigin="anonymous" type="text/javascript" src="https://msrewardspme.azureedge.net/rewardsuxassets/amcassets/js/jquery-8101d596b2-v1.js" nonce=""></script>

    <script nonce="">
        var jqueryInstance = jQuery || $;
        jqueryInstance && jqueryInstance.ajaxPrefilter(function (s) {
        if (s.crossDomain) {
        s.contents.script = false;
        }
        });
    </script>

    
<script type="text/javascript" nonce="">

    //TBD: Add Nightwatch for JS Error
    window.onerror = function javaScriptErrorWatchdog(errorMsg, url, lineNumber) {
        var errorMeta = document.createElement("meta");
        errorMeta.name = "JSError";
        errorMeta.content = "" + url + "; " + lineNumber + "; " + errorMsg;
        document.getElementsByTagName("head")[0].appendChild(errorMeta);

        return false;
    };


</script>


    <script type="text/javascript" nonce="">
        if (navigator.userAgent.match(/IEMobile\/10\.0/)) {
        var msViewportStyle = document.createElement("style");
        var mq = "@-ms-viewport{width:auto!important}";
        msViewportStyle.appendChild(document.createTextNode(mq));
        document.getElementsByTagName("head")[0].appendChild(msViewportStyle);
        }
    </script>

    <script id="site-layout-config" type="text/javascript" nonce="">
        var MeePortal = MeePortal || {};
        MeePortal.Utilities = MeePortal.Utilities || {};

        MeePortal.g_userFeatures =[];
        MeePortal.g_userFlights = MeePortal.g_userFeatures.map(feature => feature.split(":")[0]);

        //MeePortal.Utilities.EmailRegex = //;
    </script>


    
    


        <base href="/">
</head>
<body ng-csp="">
    <div id="page-wrapper">
        <div id="site-header">
            

<header>
    <div id="rewards-header" class="" aria-label="Rewards Header">
        <div class="l_header_container ">
            <div class="l_header_left">
                <a href="/createuser" class="skip_main_content_btn">Skip to main content</a>

                <a href="/" id="reward_header_rewards_logo">
                    <img class="bing_logo" src="//rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/Ms_Logo_48px.png" alt="Rewards">
                </a>
                <a class="l_header_title" href="/" aria-label="Rewards" id="reward_header_rewards">
                    Microsoft &nbsp; | &nbsp; Rewards
                </a>

                            <a id="give_header_link" class="redirect_link other_sites" href="https://rewards.bing.com/redeem/donate?publ=&amp;crea=&amp;pn=" aria-label="Donation catalog">
                                Donate
                            </a>
                            <a id="chat_header_link" class="redirect_link other_sites" href="https://www.bing.com/chat?form=ML2L0N" aria-label="Link to Microsoft Copilot">
                                Copilot
                            </a>

                    <div class="redirect_link more_options">
                        <button id="link_dropdown" type="button" title="More" class="more_btn" data-is-focusable="true">
                            <span>
                                ···
                            </span>
                        </button>
                        <div class="more_btn_popup" id="morelinks_popup">
                            <div class="ms-Callout-beak">
                            </div>
                            <div class="ms-Callout-beakCurtain">
                                <div class="ms-Callout-main">
                                    <ul class="ms-ContextualMenu-list">
                                            <li role="presentation" class="ms-ContextualMenu-item">
                                                <div>
                                                </div>
                                            </li>
                                            <li role="presentation" class="ms-ContextualMenu-item">
                                                <div>
                                                        <a class="redirect_link other_sites more_option" href="https://rewards.bing.com/redeem/donate?publ=&amp;crea=&amp;pn=" aria-label="Donation catalog">
                                                            Donate
                                                        </a>
                                                </div>
                                            </li>
                                            <li role="presentation" class="ms-ContextualMenu-item">
                                                <div>
                                                </div>
                                            </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

            </div>
            <div class="l_header_right">
                        <a class="redirect_link amc_link" href="https://account.microsoft.com" aria-label="Microsoft account" id="reward_header_amc">
                            Microsoft account
                        </a>
                    <span class="redirect_link additional_info" id="divider"> | </span>
                    <a id="redirect_info_link" tabindex="0" class="redirect_link additional_info" data-clarity-mask="True" title="Next Byte">
                        Next Byte
                    </a>
                    <div id="profile_img_sec" class="image_sec" title="Next Byte">
                        <img id="img_sec" class="profile_img" src="https://storage.live.com/users/0x000000000000000026d03b6a22c5b618/myprofile/expressionprofile/profilephoto:Win8Static,UserTileMedium,UserTileStatic/MeControlXXLUserTile?ck=1&amp;ex=24">
                        <div class="displayPictureLinkedAccount">
                            <img id="img_sec_default" tabindex="0" class="profile_img" src="//rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/profilePicBackground.svg" alt="Default profile image">
                        </div>
                    </div>
                    <div id="mectrl_popup" class="mectrl_body">
                        <div class="mectrl_currentAccount">
                            <div class="mectrl_company">
                                    <div class="glyph_msft">
                                        <div class="glyph_text">Microsoft</div>
                                    </div>
                                <div id="mectrl_body_signOut" class="mectrl_signout">
<a href="/Signout">Sign Out</a>                                </div>
                            </div>
                            <div class="mectrl_accountInfo">
                                <div>
                                    <a id="mectrl_currentAccount_picture" tabindex="0" class="mectrl_resetStyle" aria-label="My&nbsp;Microsoft&nbsp;account" target="_blank">
                                        <img id="currentAccount_picture" class="mectrl_profilepic mectrl_glyph" src="https://storage.live.com/users/0x000000000000000026d03b6a22c5b618/myprofile/expressionprofile/profilephoto:Win8Static,UserTileMedium,UserTileStatic/MeControlXXLUserTile?ck=1&amp;ex=24">
                                        <img id="currentAccount_picture_default" class="mectrl_profilepic mectrl_glyph" src="//rewards.bing.com/rewardscdn/images/rewards/membercenter/missions/profilePicBackground.svg" alt="Default profile image">

                                    </a>
                                </div>
                                <div class="mectrl_accountDetails">
                                    <div id="mectrl_currentAccount_primary" class="mectrl_name mectrl_truncate" data-clarity-mask="True">
                                        Next Byte
                                    </div>
                                    <div id="mectrl_currentAccount_secondary" class="mectrl_truncate">
                                        <EMAIL>
                                    </div>
                                    <div>
                                            <a id="mectrl_viewAccount" tabindex="0" class="mectrl_resetStyle mectrl_link mectrl_truncate" aria-describedby="mectrl_viewAccount_desc" aria-label="My&nbsp;Microsoft&nbsp;account" target="_blank">My&nbsp;Microsoft&nbsp;account</a>
                                    </div>
                                    <div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
            </div>
        </div>
    </div>
</header>

<script type="text/javascript" nonce="">
    function normalImgHandling(normalId, defaultId) {
        document.getElementById(defaultId).style["display"] = "none";
        document.getElementById(normalId).style["display"] = "inline-block";
    }

    document.getElementById("give_header_link")?.addEventListener('click', function () {
        return MeePortal.Rewards.Header.redirectTo('');
    });
    document.getElementById("chat_header_link")?.addEventListener('click', function () {
        return MeePortal.Rewards.Header.redirectTo('');
    });
    document.getElementById("rebates_header_link")?.addEventListener('click', function () {
        return MeePortal.Rewards.Header.redirectTo('');
    });

    document.getElementById("link_dropdown")?.addEventListener('click', function () {
        MeePortal.Rewards.Header.switchDropDown('#morelinks_popup');
    });
    document.getElementById("img_sec")?.addEventListener('click', function () {
        MeePortal.Rewards.Header.switchDropDown('#mectrl_popup');
    });

    document.getElementById("img_sec_default")?.addEventListener('click', function () {
        MeePortal.Rewards.Header.switchDropDown('#mectrl_popup');
    });
    document.getElementById("img_sec_default")?.addEventListener('keydown', function (e) {
        if (e.key === "Enter") {
            MeePortal.Rewards.Header.switchDropDown('#mectrl_popup');
        }
    });
    document.getElementById("redirect_info_link")?.addEventListener('click', function () {
        MeePortal.Rewards.Header.switchDropDown('#mectrl_popup');
    });
    document.getElementById("redirect_info_link")?.addEventListener('keydown', function (e) {
        if (e.key === "Enter") {
            MeePortal.Rewards.Header.switchDropDown('#mectrl_popup');
        }
    });

    document.getElementById("mectrl_viewAccount")?.addEventListener('click', function () {
        MeePortal.Rewards.Header.openManageAccountLink('https://account.microsoft.com/?username=', '<EMAIL>');
    });
    document.getElementById("mectrl_currentAccount_picture")?.addEventListener('click', function () {
        MeePortal.Rewards.Header.openManageAccountLink('https://account.microsoft.com/?username=', '<EMAIL>');
    });

    document.getElementById("mectrl_viewAccount")?.addEventListener('keydown', function (e) {
        if (e.key === "Enter") {
            MeePortal.Rewards.Header.openManageAccountLink('https://account.microsoft.com/?username=', '<EMAIL>');
        }
    });
    document.getElementById("mectrl_currentAccount_picture")?.addEventListener('keydown', function (e) {
        if (e.key === "Enter") {
            MeePortal.Rewards.Header.openManageAccountLink('https://account.microsoft.com/?username=', '<EMAIL>');
        }
    });

    document.getElementById("aad_submit_link")?.addEventListener("click", function () {
        document.getElementById("signoutPostLink")?.submit();
    });

    document.getElementById("img_sec")?.addEventListener('load', function () {
        normalImgHandling('img_sec', 'img_sec_default');
    });

    document.getElementById("currentAccount_picture")?.addEventListener('load', function () {
        normalImgHandling('currentAccount_picture', 'currentAccount_picture_default');
    });
    
    // Add ESC key support to close the profile popup
    document.addEventListener('keydown', function(event) {
        // Check if ESC key is pressed (key code 27)
        if (event.key === "Escape" || event.key === "Esc" || event.keyCode === 27) {
            // Get the profile popup element
            var popup = document.getElementById("mectrl_popup");
            // If popup exists and has the expand class (meaning it's visible), close it
            if (popup && popup.classList.contains("expand")) {
                MeePortal.Rewards.Header.switchDropDown('#mectrl_popup');
            }
        }
    });
</script>
            
    

        </div>
        <div tabindex="-1" id="main-content-landing" aria-label="pagearia" class="no-outline">
            <main id="rewards-dashboard" data-grid="container" class="content-container container-fluid context-amc rewards-container">
                












<input name="__RequestVerificationToken" type="hidden" value="CfDJ8OmjzpJHIp5HoToS1x8R-_oVL1il_zgGA46SP4LECk9_WiaeGIHty051_CZ8Em5Yp-18O8GJKPSoi-eG37AJBrXu17zuHX7ZiUib9omnGIF1XDprY6eXDLebT3rT0xhX3cwDRYZR8gQiTdy6xBOFYjkPbsdfCFPSINOL7wWW-5x9omBDE-1-edKDuctQsL_2lg">

<div id="app-host" ng-app="rewardsApp">
    <ui-view></ui-view>
    <mee-modal-dialog-host data-modal-id="modal-host" data-kind="flow close-button" mee-aria-close-button-label="Close" mee-aria-dialog-label="Rewards Dialog"></mee-modal-dialog-host>

</div>




            </main>
        </div>

        
    

    </div>

    <div id="site-footer">
        

<div id="footerArea" class="uhf">
    <div id="footerRegion">
            <div class="help-footer " data-grid="container pad-12x">
                <div>
                    <h2 class="c-heading-5">Help with Microsoft Rewards</h2>
                    <hr class="c-divider f-pad-vertical-3x">
                    <ul class="c-list f-bare">
                                <li>
                                    <a class="c-hyperlink" id="rewards-helplinks-microsoft-rewards-faq" href="https://support.microsoft.com/topic/c5ab735d-c6d9-4bb9-30ad-d828e954b6a9" aria-label="Find how to resolve common issues" target="_blank">
                                        Find how to resolve common issues
                                    </a>
                                </li>
                                <li>
                                    <a class="c-hyperlink" id="rewards-provide-feedback" tabindex="0" role="button" aria-label="Provide feedback" target="_self">
                                        Provide feedback
                                    </a>
                                </li>
                                <li>
                                    <a class="c-hyperlink" id="rewards-contact-support" href="/support" aria-label="Contact Microsoft Rewards support" target="_self">
                                        Contact Microsoft Rewards support
                                    </a>
                                </li>
                    </ul>
                </div>
            </div>

        <div id="footerUniversalFooter">
            <footer id="uhf-footer" class="c-uhff context-uhf">
                <div class="c-uhff-base">
                    <a id="locale-picker-link" aria-label="English (United States)" class="c-uhff-link c-uhff-lang-selector c-glyph glyph-world " href="https://www.bing.com/account/general?ru=https://www.bing.com/rewards&amp;id=language_section#language-section">English (United States)</a>
                    <nav>
                        <ul class="c-list f-bare ">
                                        <li>
                                            <a class="c-uhff-link" id="rewards-footer-privacy" href="https://go.microsoft.com/fwlink/?LinkID=521839" tabindex="0" role="link" aria-label="Privacy and Cookies" target="_blank">
                                                Privacy and Cookies
                                            </a>
                                        </li>
                                        <li>
                                            <a class="c-uhff-link" id="rewards-footer-managecookie" href="#" tabindex="0" role="link" aria-label="Manage Cookies">
                                                Manage Cookies
                                            </a>
                                        </li>
                                        <li>
                                            <a class="c-uhff-link" id="rewards-footer-tou" href="https://go.microsoft.com/fwlink/?LinkID=530144" tabindex="0" role="link" aria-label="Terms of use" target="_blank">
                                                Terms of use
                                            </a>
                                        </li>
                                        <li>
                                            <a class="c-uhff-link" id="rewards-footer-contact" href="https://support.microsoft.com/contactus" tabindex="0" role="link" aria-label="Contact us" target="_blank">
                                                Contact us
                                            </a>
                                        </li>

                            <li>© Microsoft 2025</li>
                        </ul>
                    </nav>
                    


<script crossorigin="anonymous" type="text/javascript" src="https://msrewardspme.azureedge.net/rewardsuxassets/amcassets/js/htmlsanitizer-aae1a5498e-v1.js" nonce=""></script>
<script crossorigin="anonymous" type="text/javascript" src="https://msrewardspme.azureedge.net/rewardsuxassets/amcassets/js/formsanitizing-ef4ae0f98c-v1.js" nonce=""></script>

<div id="feedback-widget" class="feedback_body">
    <div id="feedback-container">
        <div id="feedbackModalShield" class="modalShield hlig">
        </div>
        <div id="fbpgdg" role="dialog" aria-modal="true" aria-label="Feedback dialog" class="feedback-dialog hlig" data-fbsource="verbatim">
            <div id="fbpgdghd" class="hlig">
                <h2 class=" hlig step1">Feedback about Microsoft Rewards</h2>
                <h2 class=" hlig thanku" id="title-thanku">Thank You!</h2>
                <h2 class=" hlig thanku" id="title-sorry">Something went wrong</h2>
            </div>


            <p class="hlig step1">Please provide as many details as you can.​ Feedbacks will be used to improve the program and to help you find more information​</p>
            <div class="hlig step1 fbctgcnt" role="radiogroup" aria-label="Feedback type">
                <div class="hlig fbctgctl">
                    <input id="fbpgdgtp1" type="radio" value="1" name="feedbacktype" class="hlig" checked="">
                    <label for="fbpgdgtp1" class="hlig">
                        Suggest​
                    </label>
                </div>
                <div class="hlig fbctgctl">
                    <input id="fbpgdgtp2" type="radio" value="2" name="feedbacktype" class="hlig">
                    <label for="fbpgdgtp2" class="hlig">
                        Like​
                    </label>
                </div>
                <div class="hlig fbctgctl">
                    <input id="fbpgdgtp3" type="radio" value="3" name="feedbacktype" class="hlig">
                    <label for="fbpgdgtp3" class="hlig">
                        Dislike​
                    </label>
                </div>
            </div>

            <p class="hlig step1">Select an area:​</p>
            <div class="hlig step1 fbctgcnt" role="radiogroup" aria-label="Feedback area">
                <div class="hlig fbctgctl">
                    <input id="fbpgdgar1" type="radio" value="1" name="feedbackarea" class="hlig" checked="">
                    <label for="fbpgdgar1" class="hlig">
                        General Feedback​
                    </label>
                </div>
                <div class="hlig fbctgctl">
                    <input id="fbpgdgar2" type="radio" value="2" name="feedbackarea" class="hlig">
                    <label for="fbpgdgar2" class="hlig">
                        Points/Streak issue​
                    </label>
                </div>
                <div class="hlig fbctgctl">
                    <input id="fbpgdgar3" type="radio" value="3" name="feedbackarea" class="hlig">
                    <label for="fbpgdgar3" class="hlig">
                        Issues with my account​
                    </label>
                </div>
            </div>
            
            <div id="fbpgdgtxpnl">
                <textarea id="fbpgdgtx" aria-label="Textbox for your feedback. The maximum length is 400." maxlength="400" placeholder="Enter your feedback here. To help protect your privacy, don't include personal info, like your name or email address.​" data-err="Please leave a comment.​" class="hlig step1" required=""></textarea>
                <label for="fbpgdgtx" id="fbpgdgtx_error" class="error hlig b_hide">
                    Please leave a comment.​
                </label>
                <div tabindex="{0}" aria-label="Remaining number" role="contentinfo" id="fbpgdgtxct" class="hlig step1">
                    400
                </div>
            </div>
            <div id="fbpgdgpnl" class="step1">
                <div id="fbpgdgpnlss" class="inline hlig">
                    <input id="fbpgdgsschk" type="checkbox" checked="" name="screenshotAllowed" class="hlig" data-collect="IncludeUnchecked">
                    <label id="fbpgdgsschklbl" for="fbpgdgsschk" class="hlig">
                        Include a Screenshot​
                    </label>
                </div>
            </div>

            <input id="fbpgdgid" hidden="" value="3EA7D00F9809096A3EE688E6FFFFFFFF">
            <div id="fbpgdgpnlrp" class="hlig step1">
                <p>
                    <a id="fbpgdgrplk" class="hlig" target="_blank" href="http://go.microsoft.com/fwlink?LinkId=850876">
                        Report a concern​
                    </a>
                </p>
            </div>
            <div id="fbpgdgbtn" class="hlig controls step1">
                <button id="fbpgdgsbbt" role="button" class="inline button hlig">
                    Send
                </button>
                <button id="fbpgdgclbt1" role="button" class="inline button hlig">
                    Cancel
                </button>
            </div>
            <div id="fbpgdgsp" class="hlig">
                <div class="hlig">
                    <img class="hlig rms_img" alt="Sending..." src="https://www.bing.com/rp/YMAjFdAOhEC-uxszpfD7wcU3aJE.gif">
                    <br class="hlig">
                    <span class="hlig">Please wait ...</span>
                </div>
            </div>
            <div id="feedback-status-container" aria-live="assertive" aria-atomic="true">
                <p id="fbpgdgthnxmsg1" class="thanku" tabindex="-1">
                    Thank you for helping us making the Microsoft Rewards program better.​
                </p>
                <p id="fbpgdgthnxmsg2" class="thanku" tabindex="-1">
                    Thank you for helping us making the Microsoft Rewards program better.



If you need support to solve an issue related to points or streak, we suggest you to use this 
                    <a id="rewards-feedback-support-form" href="/support" aria-label="web form." target="_blank"> web form.</a>
                </p>
                <p id="fbpgdgthnxmsg3" class="thanku" tabindex="-1">
                    Thank you for helping us making the Microsoft Rewards program better.



If you need support to solve an issue related to your account, we suggest you to use this
                    <a id="rewards-feedback-support-form" href="/support" aria-label="web form." target="_blank"> web form.</a>
                </p>
                <p id="fbpgdgerrmsg1" class="thanku" tabindex="-1">
                    You've submitted too many feedback requests in a short time. Please try again later.
                </p>
                <p id="fbpgdgerrmsg2" class="thanku" tabindex="-1">
                    We're unable to process feedback requests at this time. Please try again later.
                </p>
            </div>
            <div class="controls step2">
                <button id="fbpgdgclbt" role="button" class="inline button hlig">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script nonce="">
    document.addEventListener('DOMContentLoaded', () => {
        document.getElementById('fbpgdgsbbt').addEventListener('click', () => {
            onSendFeedback();
        });
    });
</script>
                </div>
            </footer>
        </div>
    </div>
</div>

<script type="text/javascript" nonce="">
    document.addEventListener('DOMContentLoaded', () => {

                    document.getElementById('rewards-provide-feedback')?.addEventListener('click', function () {
                MeePortal.Rewards.FeedbackForm.onFeedbackClickHandler();
                    });
            

                        document.getElementById('rewards-footer-managecookie')?.addEventListener('click', function (evt) {
                            evt.preventDefault();
                            
                manageConsent();
                
                                });
                        });
</script>
        
    

    </div>

    
    

    
    <script crossorigin="anonymous" type="text/javascript" src="https://msrewardspme.azureedge.net/rewardsuxassets/amcassets/js/mwf-main-832892dd3a-v1.umd.js" nonce=""></script>
    <script crossorigin="anonymous" type="text/javascript" src="https://msrewardspme.azureedge.net/rewardsuxassets/amcassets/js/site-940d6445af-v1.js" nonce=""></script>
    <script crossorigin="anonymous" type="text/javascript" src="https://msrewardspme.azureedge.net/rewardsuxassets/amcassets/js/angular-0a734239b4-v1.js" nonce=""></script>
    <script crossorigin="anonymous" type="text/javascript" src="https://msrewardspme.azureedge.net/rewardsuxassets/amcassets/js/aria-webjs-sdk-a54a34c25f-v1.js" nonce=""></script>
    </body></html>