import asyncio
import random
import nodriver as uc
import os
import time
import json
from datetime import datetime
from src.browser_actions import BrowserActions

class EnhancedTestRunner:
    def __init__(self):
        self.browser = None
        self.tab = None
        self.browser_actions = None
        self.test_stats = {
            'commands_executed': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'start_time': None,
            'mouse_movements': 0,
            'keyboard_inputs': 0,
            'scroll_operations': 0
        }
        self.available_commands = {
            'move': 'Move mouse to a random location',
            'target': 'Move mouse to the draggable target element',
            'type': 'Type text into the input field (usage: type <text>)',
            'scroll': 'Scroll to a random point in the scrollable section',
            'click': 'Click on the draggable target',
            'clear': 'Clear the input field',
            'stats': 'Show test statistics',
            'help': 'Show this help message',
            'reset': 'Reset the page and reload',
            'exit': 'Close the browser and exit'
        }

    async def initialize_browser(self):
        """Initialize the browser and load the test page"""
        try:
            print("🚀 Launching Enhanced Browser Test Environment...")
            self.browser = await uc.start(headless=False)
            self.tab = self.browser.main_tab
            
            # Load the enhanced test HTML file
            html_file_path = f"file://{os.path.abspath('tmp_rovodev_enhanced_test_page.html')}"
            print(f"📄 Loading enhanced test page: {html_file_path}")
            await self.tab.get(html_file_path)
            
            # Wait for page to load
            await asyncio.sleep(2)
            
            # Initialize BrowserActions
            self.browser_actions = BrowserActions(self.tab, "<EMAIL>")
            
            # Initialize random target position
            await self.initialize_random_target()
            
            self.test_stats['start_time'] = datetime.now()
            print("✅ Browser initialized successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Error initializing browser: {e}")
            return False

    async def initialize_random_target(self):
        """Initialize the random target position"""
        try:
            init_target_js = """
            (() => {
                if (typeof setRandomTargetPosition === 'function') {
                    return setRandomTargetPosition();
                }
                return 0;
            })()
            """
            target_pos = await self.tab.evaluate(init_target_js)
            print(f"🎯 Random scroll target set at position: {target_pos}px")
            return target_pos
        except Exception as e:
            print(f"⚠️ Could not set random target position: {e}")
            return 0

    def print_welcome_message(self):
        """Print a welcome message with available commands"""
        print("\n" + "="*80)
        print("🎮 ENHANCED BROWSER ACTIONS TEST ENVIRONMENT")
        print("="*80)
        print("Welcome to the advanced testing environment for browser automation!")
        print("This tool allows you to test mouse movements, keyboard input, and scrolling.")
        print("\n📋 Available Commands:")
        print("-" * 40)
        
        for cmd, desc in self.available_commands.items():
            print(f"  {cmd:10} - {desc}")
        
        print("\n💡 Tips:")
        print("  • Use 'help' to see this message again")
        print("  • Use 'stats' to view performance statistics")
        print("  • Press Ctrl+C to exit at any time")
        print("="*80 + "\n")

    def print_stats(self):
        """Print current test statistics"""
        if self.test_stats['start_time']:
            runtime = datetime.now() - self.test_stats['start_time']
            runtime_str = str(runtime).split('.')[0]  # Remove microseconds
        else:
            runtime_str = "N/A"
        
        print("\n📊 TEST STATISTICS")
        print("-" * 30)
        print(f"Runtime: {runtime_str}")
        print(f"Commands executed: {self.test_stats['commands_executed']}")
        print(f"Successful operations: {self.test_stats['successful_operations']}")
        print(f"Failed operations: {self.test_stats['failed_operations']}")
        print(f"Mouse movements: {self.test_stats['mouse_movements']}")
        print(f"Keyboard inputs: {self.test_stats['keyboard_inputs']}")
        print(f"Scroll operations: {self.test_stats['scroll_operations']}")
        
        if self.test_stats['commands_executed'] > 0:
            success_rate = (self.test_stats['successful_operations'] / self.test_stats['commands_executed']) * 100
            print(f"Success rate: {success_rate:.1f}%")
        print("-" * 30 + "\n")

    async def execute_move_command(self):
        """Execute random mouse movement"""
        try:
            target_x = random.randint(100, 1000)
            target_y = random.randint(100, 600)
            print(f"🖱️ Moving mouse to random position: ({target_x}, {target_y})")
            
            await self.browser_actions.human_like_mouse_move(target_x, target_y)
            self.test_stats['mouse_movements'] += 1
            self.test_stats['successful_operations'] += 1
            print("✅ Mouse movement completed successfully!")
            
        except Exception as e:
            print(f"❌ Mouse movement failed: {e}")
            self.test_stats['failed_operations'] += 1

    async def execute_target_command(self):
        """Execute movement to draggable target"""
        try:
            print("🎯 Moving mouse to draggable target element...")
            success = await self.browser_actions.move_to_target("#draggableTarget")
            
            if success:
                print("✅ Successfully moved to target!")
                self.test_stats['mouse_movements'] += 1
                self.test_stats['successful_operations'] += 1
            else:
                print("❌ Failed to move to target.")
                self.test_stats['failed_operations'] += 1
                
        except Exception as e:
            print(f"❌ Target movement failed: {e}")
            self.test_stats['failed_operations'] += 1

    async def execute_type_command(self, text_to_type):
        """Execute typing command"""
        try:
            print(f"⌨️ Typing '{text_to_type}' into the text field...")
            success = await self.browser_actions.send_keys("#testInput", text_to_type)
            
            if success:
                print("✅ Text typed successfully!")
                self.test_stats['keyboard_inputs'] += len(text_to_type)
                self.test_stats['successful_operations'] += 1
            else:
                print("❌ Failed to type text.")
                self.test_stats['failed_operations'] += 1
                
        except Exception as e:
            print(f"❌ Typing failed: {e}")
            self.test_stats['failed_operations'] += 1

    async def execute_scroll_command(self):
        """Execute scrolling command"""
        try:
            print("📜 Performing human-like scroll to random target...")
            success = await self.browser_actions.human_like_scroll_to_random_target()
            
            if success:
                print("✅ Scroll operation completed successfully!")
                self.test_stats['scroll_operations'] += 1
                self.test_stats['successful_operations'] += 1
            else:
                print("❌ Scroll operation failed.")
                self.test_stats['failed_operations'] += 1
                
        except Exception as e:
            print(f"❌ Scroll operation failed: {e}")
            self.test_stats['failed_operations'] += 1

    async def execute_click_command(self):
        """Execute click on target"""
        try:
            print("🖱️ Clicking on the draggable target...")
            success = await self.browser_actions.click("#draggableTarget")
            
            if success:
                print("✅ Click operation completed successfully!")
                self.test_stats['successful_operations'] += 1
            else:
                print("❌ Click operation failed.")
                self.test_stats['failed_operations'] += 1
                
        except Exception as e:
            print(f"❌ Click operation failed: {e}")
            self.test_stats['failed_operations'] += 1

    async def execute_clear_command(self):
        """Clear the input field"""
        try:
            print("🧹 Clearing the input field...")
            # Select all text and delete it
            await self.browser_actions.click("#testInput")
            await asyncio.sleep(0.2)
            
            # Send Ctrl+A to select all, then Delete
            clear_js = """
            (() => {
                const input = document.getElementById('testInput');
                if (input) {
                    input.value = '';
                    input.dispatchEvent(new Event('input', { bubbles: true }));
                    return true;
                }
                return false;
            })()
            """
            success = await self.tab.evaluate(clear_js)
            
            if success:
                print("✅ Input field cleared successfully!")
                self.test_stats['successful_operations'] += 1
            else:
                print("❌ Failed to clear input field.")
                self.test_stats['failed_operations'] += 1
                
        except Exception as e:
            print(f"❌ Clear operation failed: {e}")
            self.test_stats['failed_operations'] += 1

    async def execute_reset_command(self):
        """Reset the page"""
        try:
            print("🔄 Resetting the page...")
            await self.tab.reload()
            await asyncio.sleep(2)
            await self.initialize_random_target()
            print("✅ Page reset successfully!")
            self.test_stats['successful_operations'] += 1
            
        except Exception as e:
            print(f"❌ Page reset failed: {e}")
            self.test_stats['failed_operations'] += 1

    async def run_interactive_session(self):
        """Run the interactive command session"""
        self.print_welcome_message()
        
        while True:
            try:
                # Get user input
                command = await asyncio.to_thread(input, "🎮 Enter command (or 'help' for options): ")
                command_parts = command.strip().split(' ', 1)
                action = command_parts[0].lower()
                
                if not action:
                    continue
                
                self.test_stats['commands_executed'] += 1
                start_time = time.time()
                
                # Execute commands
                if action == 'move':
                    await self.execute_move_command()
                    
                elif action == 'target':
                    await self.execute_target_command()
                    
                elif action == 'type':
                    if len(command_parts) > 1:
                        text_to_type = command_parts[1]
                        await self.execute_type_command(text_to_type)
                    else:
                        print("❌ Please provide text to type. Usage: 'type <text>'")
                        self.test_stats['failed_operations'] += 1
                        
                elif action == 'scroll':
                    await self.execute_scroll_command()
                    
                elif action == 'click':
                    await self.execute_click_command()
                    
                elif action == 'clear':
                    await self.execute_clear_command()
                    
                elif action == 'stats':
                    self.print_stats()
                    
                elif action == 'help':
                    self.print_welcome_message()
                    
                elif action == 'reset':
                    await self.execute_reset_command()
                    
                elif action == 'exit':
                    print("👋 Exiting test environment...")
                    break
                    
                else:
                    print(f"❌ Unknown command: '{action}'. Type 'help' for available commands.")
                    self.test_stats['failed_operations'] += 1
                
                # Show execution time for operations
                if action in ['move', 'target', 'type', 'scroll', 'click', 'clear', 'reset']:
                    execution_time = time.time() - start_time
                    print(f"⏱️ Operation completed in {execution_time:.2f} seconds\n")
                
            except KeyboardInterrupt:
                print("\n\n⚠️ Interrupted by user. Exiting...")
                break
            except Exception as e:
                print(f"❌ Unexpected error: {e}")
                self.test_stats['failed_operations'] += 1

    async def cleanup(self):
        """Clean up resources"""
        try:
            if self.browser:
                print("🧹 Cleaning up browser resources...")
                await self.browser.stop()
                print("✅ Browser closed successfully!")
                
            # Print final statistics
            print("\n📊 FINAL TEST STATISTICS")
            self.print_stats()
            
        except Exception as e:
            print(f"⚠️ Error during cleanup: {e}")

async def main():
    """Main function to run the enhanced test environment"""
    test_runner = EnhancedTestRunner()
    
    try:
        # Initialize browser
        if not await test_runner.initialize_browser():
            print("❌ Failed to initialize browser. Exiting...")
            return
        
        # Run interactive session
        await test_runner.run_interactive_session()
        
    except Exception as e:
        print(f"❌ Fatal error: {e}")
    finally:
        # Always cleanup
        await test_runner.cleanup()

if __name__ == "__main__":
    try:
        uc.loop().run_until_complete(main())
    except KeyboardInterrupt:
        print("\n👋 Test environment terminated by user.")
    except Exception as e:
        print(f"❌ Failed to start test environment: {e}")