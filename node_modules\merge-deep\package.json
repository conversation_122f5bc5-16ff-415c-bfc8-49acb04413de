{"name": "merge-deep", "description": "Recursively merge values in a javascript object.", "version": "3.0.3", "homepage": "https://github.com/jonschlinkert/merge-deep", "author": "<PERSON> (https://github.com/jonschlinkert)", "repository": "jonschlinkert/merge-deep", "bugs": {"url": "https://github.com/jonschlinkert/merge-deep/issues"}, "license": "MIT", "files": ["index.js"], "main": "index.js", "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "dependencies": {"arr-union": "^3.1.0", "clone-deep": "^0.2.4", "kind-of": "^3.0.2"}, "devDependencies": {"gulp-format-md": "^0.1.7", "mocha": "^2.4.5"}, "keywords": ["clone", "clone-deep", "copy", "deep", "deep-clone", "deep-merge", "extend", "key", "keys", "merge", "merge-deep", "object", "objects", "prop", "properties", "property", "props", "value", "values"], "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["assign-deep", "defaults-deep", "extend-shallow", "merge-deep", "mixin-deep", "omit-deep"]}, "reflinks": ["verb"], "lint": {"reflinks": true}}}