"""
Human Interaction AI Model Package

This package provides advanced AI models for generating human-like browser interactions
based on recorded human behavior data.

Main Components:
- HumanInteractionAI: Core AI model for learning from interaction data
- HumanInteractionGenerator: Generates realistic human-like interactions
- InteractionConfig: Configuration for interaction generation
- NoDriverIntegration: Integration with nodriver browser automation

Example Usage:
    from ai_model import HumanInteractionAI, InteractionConfig, HumanInteractionGenerator
    
    # Initialize and train model
    config = InteractionConfig()
    ai_model = HumanInteractionAI(config)
    ai_model.load_training_data('data/')
    ai_model.train_models()
    
    # Generate interactions
    generator = HumanInteractionGenerator(ai_model)
    mouse_path = generator.generate_mouse_path((0, 0), (500, 300))
    typing_seq = generator.generate_typing_sequence("Hello World!")
"""

from .human_interaction_ai import HumanInteractionAI, InteractionConfig, FeatureExtractor
from .interaction_generator import (
    HumanInteractionGenerator, 
    MousePath, 
    TypingSequence, 
    ScrollSequence,
    BezierCurve,
    NoDriverIntegration
)

__version__ = "1.0.0"
__author__ = "Human Interaction AI Team"
__email__ = "<EMAIL>"

__all__ = [
    # Core classes
    'HumanInteractionAI',
    'InteractionConfig',
    'FeatureExtractor',
    
    # Generation classes
    'HumanInteractionGenerator',
    'MousePath',
    'TypingSequence', 
    'ScrollSequence',
    'BezierCurve',
    
    # Integration
    'NoDriverIntegration',
]

# Package metadata
PACKAGE_INFO = {
    'name': 'human-interaction-ai',
    'version': __version__,
    'description': 'Advanced AI model for generating human-like browser interactions',
    'author': __author__,
    'email': __email__,
    'url': 'https://github.com/your-repo/human-interaction-ai',
    'license': 'MIT',
    'keywords': ['ai', 'automation', 'human-behavior', 'browser-automation', 'machine-learning'],
    'classifiers': [
        'Development Status :: 4 - Beta',
        'Intended Audience :: Developers',
        'License :: OSI Approved :: MIT License',
        'Programming Language :: Python :: 3',
        'Programming Language :: Python :: 3.8',
        'Programming Language :: Python :: 3.9',
        'Programming Language :: Python :: 3.10',
        'Programming Language :: Python :: 3.11',
        'Topic :: Scientific/Engineering :: Artificial Intelligence',
        'Topic :: Software Development :: Libraries :: Python Modules',
    ],
}