import asyncio
import nodriver as uc
from src.browser_actions import BrowserActions
from src.utils import log_error
from src.logger import logger # Import the new logger

async def get_remaining_desktop_searches(browser, email):
    """
    Navigates to the points breakdown page, extracts the remaining desktop searches,
    and closes the dialog.
    """
    browser_actions = BrowserActions(browser.main_tab, email)
    tab = browser.main_tab

    logger.info("Attempting to get remaining desktop searches...", extra={'email': email})

    try:
        # 1. Click Points Breakdown Link
        # The button has id="dailypointColumnCalltoAction"
        logger.info("Clicking 'Points breakdown' link...", extra={'email': email})
        points_breakdown_link =  await browser_actions.click('#dailypointColumnCalltoAction')
        if not points_breakdown_link:
            page_html = await tab.get_content()
            await log_error(email, "Points breakdown link not found or clickable.", page_html)
            logger.warning("Failed to find or click Points breakdown link.", extra={'email': email})
            return "N/A (Link not found)"

        # 2. Wait for Dialog
        # The dialog content is within a div with ui-view="modalContent" and contains id="userPointsBreakdown"
        # We can wait for the id="userPointsBreakdown" element to appear.
        dialog_element = await browser_actions.wait_for_element('#userPointsBreakdown', timeout=15)
        if not dialog_element:
            page_html = await tab.get_content()
            await log_error(email, "Points breakdown dialog did not appear within timeout.", page_html)
            logger.warning("Points breakdown dialog did not appear.", extra={'email': email})
            return "N/A (Dialog not found)"
        logger.info("Points breakdown dialog appeared.", extra={'email': email})

        # 3. Extract Search Progress
        # The relevant element is <p ng-bind-html="$ctrl.pointProgressText" class="pointsDetail c-subheading-3 ng-binding"><b>0</b> / 30</p>
        # It's inside a div with class="pointsBreakdownCard" that also contains "PC search"
        
        # Find all points breakdown cards
        points_cards = await tab.select_all('.pointsBreakdownCard', timeout=10)
        desktop_search_progress = "N/A"

        for card in points_cards:
            # Check if "PC search" text is present within the card
            card_html = await card.get_html()
            if "PC search" in card_html:
                # Found the PC search card, now extract the progress text
                progress_element = await card.query_selector('.pointsDetail.c-subheading-3.ng-binding')
                if progress_element:
                    desktop_search_progress = progress_element.text_all
                    logger.info(f"Extracted desktop search progress: {desktop_search_progress}", extra={'email': email})
                    break
        
        if desktop_search_progress == "N/A":
            logger.warning("Could not find desktop search progress in the dialog.", extra={'email': email})

        # 4. Close Dialog
        # The close button has class="c-glyph glyph-cancel" and aria-label="Close"
        
        logger.info("Clicking close button for the dialog...", extra={'email': email})
        close_button = await browser_actions.click('button.c-glyph.glyph-cancel[aria-label="Close"]')
        if not close_button:
            logger.warning("Could not find dialog close button.", extra={'email': email})

        return desktop_search_progress

    except asyncio.TimeoutError:
        logger.warning("Timeout while waiting for elements in get_remaining_desktop_searches.", extra={'email': email})
        return "N/A (Timeout)"
    except Exception as e:
        page_html = await tab.get_content()
        await log_error(email, f"An error occurred in get_remaining_desktop_searches: {e}", page_html)
        logger.error(f"An error occurred in get_remaining_desktop_searches: {e}", extra={'email': email})
        return f"N/A (Error: {e})"