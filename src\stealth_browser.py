import nodriver as uc
import asyncio
import random
import os
import hashlib
from .utils import log_error # Import log_error for session errors
from .browser_actions import BrowserActions # Import the new BrowserActions class
from .logger import logger # Import the new logger

class StealthBrowser:
    def __init__(self, session_dir="sessions"):
        self.browser = None
        self.tab = None
        self.session_dir = session_dir
        self.session_loaded = False # New attribute to track if a session was loaded
        self.browser_actions = None # Initialize BrowserActions here
    
    async def start_stealth_session(self):
        """Initialize browser with maximum stealth configuration"""
        # Advanced browser arguments for stealth
        browser_args = [
            '--no-first-run',
            '--no-service-autorun',
            '--no-default-browser-check',
            # '--disable-blink-features=AutomationControlled', # Removed as it causes an "unsupported command-line flag" warning. Stealth is handled by JS injection.
            '--disable-features=VizDisplayCompositor',
            '--disable-ipc-flooding-protection',
            '--disable-renderer-backgrounding',
            '--disable-backgrounding-occluded-windows',
            # '--disable-web-security', # Removed as it causes an "unsupported command-line flag" warning and is not essential for stealth.
            '--disable-features=TranslateUI',
            '--disable-default-apps',
            # '--no-sandbox', # Removed as it causes an "unsupported command-line flag" warning and might be detectable.
            # '--disable-dev-shm-usage', # Removed as it might force SwiftShader
            # '--disable-gpu', # Removed as it might force SwiftShader
            '--disable-extensions-file-access-check',
            '--disable-extensions-http-throttling',
            '--disable-extensions-except-background',
            '--start-maximized' # Open browser in full window
        ]
        
        # Start browser with stealth config
        self.browser = await uc.start(
            browser_args=browser_args,
            headless=False,  # Always use GUI mode for maximum stealth
            expert=False     # Enable built-in stealth optimizations
        )
        
        self.tab = self.browser.main_tab
        # Initialize BrowserActions with the tab and a placeholder email (will be updated by bot)
        self.browser_actions = BrowserActions(self.tab, "<EMAIL>")
        await self.setup_maximum_stealth()
        
        return self.browser, self.tab
    
    async def setup_maximum_stealth(self):
        """Apply comprehensive stealth modifications"""
        # Enable CDP domains
        await self.tab.send(uc.cdp.runtime.enable())
        await self.tab.send(uc.cdp.page.enable())
        await self.tab.send(uc.cdp.network.enable())
        
        # Inject comprehensive stealth script
        await self.inject_stealth_script()
        
        # Configure realistic viewport
        
        # Grant permissions (FLASH permission removed as it's deprecated)
        # The grant_all_permissions method seems to be causing issues with a deprecated FLASH permission.
        # We will remove this call and rely on browser arguments and JS injection for stealth.
        # If specific permissions are needed later, they can be granted individually via CDP.
        # Grant permissions (FLASH permission removed as it's deprecated)
        # The grant_all_permissions method seems to be causing issues with a deprecated FLASH permission.
        # We will remove this call and rely on browser arguments and JS injection for stealth.
        # If specific permissions are needed later, they can be granted individually via CDP.
        
        # Add adaptive stealth method
    async def adaptive_stealth(self):
        """Implement adaptive stealth based on website response"""
        
        # Monitor for detection indicators
        detection_script = """
        // Check for common detection methods
        const detectionChecks = {
            webdriverCheck: () => navigator.webdriver,
            pluginCheck: () => navigator.plugins.length === 0,
            languageCheck: () => navigator.languages.length === 0,
            hardwareCheck: () => navigator.hardwareConcurrency === 0
        };
        
        return Object.entries(detectionChecks).map(([name, check]) => ({
            name,
            detected: check()
        }));
        """
        
        try:
            detection_results = await self.tab.evaluate(detection_script)
            
            # Adapt based on detection results
            for result in detection_results:
                if result['detected']:
                    await self._apply_additional_stealth(result['name'])
        except TypeError as e:
            # This specific TypeError happens when 'ExceptionDetails' object is not iterable
            logger.error(f"TypeError in adaptive_stealth: {e}. Likely due to JavaScript evaluation failure returning ExceptionDetails.", extra={'email': self.browser_actions.email})
            return # Exit the function gracefully
        except Exception as e:
            # Catch any other unexpected exceptions
            logger.error(f"An unexpected error occurred in adaptive_stealth: {e}", extra={'email': self.browser_actions.email})
            return # Exit the function gracefully

    async def _apply_additional_stealth(self, detection_type):
        """Apply targeted stealth measures based on detection type"""
        stealth_patches = {
            'webdriverCheck': "Object.defineProperty(navigator, 'webdriver', {get: () => undefined});",
            'pluginCheck': "Object.defineProperty(navigator, 'plugins', {get: () => [1,2,3,4,5]});",
            'languageCheck': "Object.defineProperty(navigator, 'languages', {get: () => ['en-US', 'en']});",
            'hardwareCheck': "Object.defineProperty(navigator, 'hardwareConcurrency', {get: () => 8});"
        }
        
        if detection_type in stealth_patches:
            await self.tab.evaluate(stealth_patches[detection_type])
    
    async def inject_stealth_script(self):
        """Inject comprehensive anti-detection JavaScript"""
        stealth_script = """
        (() => {
            'use strict';
            
            // Remove automation indicators
            delete Object.getPrototypeOf(navigator).webdriver;
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
                configurable: true
            });
            
            // Spoof navigator properties
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });
            
            Object.defineProperty(navigator, 'hardwareConcurrency', {
                get: () => 8,
            });
            
            // Spoof permissions API
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
            
            // Chrome runtime spoofing
            if (!window.chrome) window.chrome = {};
            window.chrome.runtime = {
                onConnect: undefined,
                onMessage: undefined,
            };
            
            // Plugin spoofing
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // Override Function.toString for stealth
            const originalToString = Function.prototype.toString;
            Function.prototype.toString = function() {
                return originalToString.call(this);
            };

            // WebGL spoofing
            const getParameter = WebGLRenderingContext.prototype.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                // UNMASKED_VENDOR_WEBGL
                if (parameter === 37445) {
                    return 'AMD'; // Spoof vendor
                }
                // UNMASKED_RENDERER_WEBGL
                if (parameter === 37446) {
                    return 'AMD Radeon(TM) Graphics (0x00001638) Direct3D11 vs_5_0 ps_5_0'; // Spoof renderer
                }
                return getParameter.call(this, parameter);
            };
            
            console.log('Stealth script injected successfully');
        })();
        """
        
        await self.tab.evaluate(stealth_script)
    
    async def navigate_with_stealth(self, url):
        """Navigate to URL with human-like delays and basic page load detection."""
        # Random delay before navigation
        await asyncio.sleep(random.uniform(1, 3))
        
        # Navigate to URL
        await self.tab.get(url)
        
        # Wait for page load with timeout
        await asyncio.sleep(random.uniform(2, 5))
        
    async def _stop_browser_robustly(self, browser_instance):
        """
        Robustly stops the browser instance, handling cases where it might be None or already stopped.
        """
        try:
            if browser_instance is not None:
                if getattr(browser_instance, "stopped", False) is False:
                    print("Stopping browser...")
                    if asyncio.iscoroutinefunction(browser_instance.stop):
                        await browser_instance.stop()
                    else:
                        browser_instance.stop()  # Non-async fallback
                    print("Browser closed.")
                else:
                    print("Browser already stopped.")
            else:
                print("Browser was never initialized.")
        except Exception as e:
            print(f"Error during browser shutdown: {e}")

    async def close_session(self):
        """Clean up browser session."""
        await self._stop_browser_robustly(self.browser)
        self.browser = None # Clear reference after attempting to stop
        self.tab = None

    async def load_session_cookies(self, email):
        """Loads cookies for a specific email from a session file using BrowserActions."""
        self.session_loaded = await self.browser_actions.load_session_cookies(self.browser, email)
        return self.session_loaded

    async def save_session_cookies(self, email):
        """Saves current browser cookies to a session file for a specific email using BrowserActions."""
        await self.browser_actions.save_session_cookies(self.browser, email)