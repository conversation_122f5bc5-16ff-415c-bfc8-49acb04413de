<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Browser Actions Test Page</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 300vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .header h1 {
            color: #4a5568;
            margin-bottom: 10px;
        }

        .status-bar {
            display: flex;
            justify-content: space-around;
            background: rgba(0,0,0,0.1);
            padding: 10px;
            border-radius: 8px;
            margin-top: 15px;
        }

        .status-item {
            text-align: center;
            font-size: 14px;
        }

        .status-value {
            font-weight: bold;
            color: #2d3748;
        }

        .main-content {
            padding: 40px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .test-section {
            background: rgba(255, 255, 255, 0.9);
            margin: 20px 0;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .test-section h2 {
            color: #2d3748;
            margin-bottom: 20px;
            border-bottom: 2px solid #4299e1;
            padding-bottom: 10px;
        }

        .input-group {
            margin: 20px 0;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #4a5568;
        }

        #testInput {
            width: 100%;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        #testInput:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .draggable-target {
            position: fixed;
            top: 100px;
            right: 50px;
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: 3px solid #fff;
            border-radius: 12px;
            cursor: move;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
            user-select: none;
            z-index: 1000;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .scroll-section {
            height: 400px;
            overflow-y: auto;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            background: #f7fafc;
            position: relative;
        }

        .scroll-content {
            padding: 20px;
            min-height: 1200px;
        }

        .scroll-marker {
            height: 60px;
            margin: 15px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
            border-radius: 8px;
            position: relative;
        }

        .scroll-target {
            background: linear-gradient(45deg, #ff5722, #ff9800);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        .section-1 { background: linear-gradient(45deg, #2196F3, #21CBF3); }
        .section-2 { background: linear-gradient(45deg, #4CAF50, #8BC34A); }
        .section-3 { background: linear-gradient(45deg, #9C27B0, #E91E63); }
        .section-4 { background: linear-gradient(45deg, #FF9800, #FFC107); }
        .section-5 { background: linear-gradient(45deg, #795548, #A1887F); }
        .section-6 { background: linear-gradient(45deg, #607D8B, #90A4AE); }

        .mouse-indicator {
            position: fixed;
            width: 16px;
            height: 16px;
            background: radial-gradient(circle, #ff0080, #ff4081);
            border: 2px solid white;
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 10px rgba(255, 0, 128, 0.5);
        }

        .coordinates-display {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            z-index: 9998;
        }

        .scroll-info {
            display: flex;
            justify-content: space-between;
            margin: 15px 0;
            padding: 10px;
            background: rgba(66, 153, 225, 0.1);
            border-radius: 6px;
            font-size: 14px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .info-label {
            font-size: 12px;
            color: #718096;
            margin-bottom: 4px;
        }

        .info-value {
            font-weight: bold;
            color: #2d3748;
        }

        .command-help {
            background: #f0f4f8;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .command-help h3 {
            color: #2d3748;
            margin-bottom: 15px;
        }

        .command-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }

        .command-item {
            background: white;
            padding: 10px;
            border-radius: 6px;
            border-left: 4px solid #4299e1;
        }

        .command-item code {
            background: #edf2f7;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
        }

        .performance-metrics {
            position: fixed;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 6px;
            font-size: 12px;
            z-index: 9998;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Enhanced Browser Actions Test Page</h1>
        <p>Advanced testing environment for mouse movements, keyboard input, and scrolling behaviors</p>
        
        <div class="status-bar">
            <div class="status-item">
                <div class="status-value" id="mousePos">Mouse: (0, 0)</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="scrollPos">Scroll: 0px</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="targetPos">Target: 0px</div>
            </div>
            <div class="status-item">
                <div class="status-value" id="inputCount">Inputs: 0</div>
            </div>
        </div>
    </div>

    <div class="main-content">
        <div class="test-section">
            <h2>📝 Keyboard Input Testing</h2>
            <div class="input-group">
                <label for="testInput">Test Input Field (type here to test keyboard automation):</label>
                <input type="text" id="testInput" placeholder="Start typing to test human-like keyboard input...">
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 Mouse Movement Testing</h2>
            <p>The draggable target below can be used to test precise mouse movements and clicking.</p>
            <div id="draggableTarget" class="draggable-target">
                🎯<br>DRAG ME
            </div>
        </div>

        <div class="test-section">
            <h2>📜 Scrolling Testing</h2>
            <p>The scrollable area below contains a randomly positioned target for testing scroll automation.</p>
            
            <div class="scroll-info">
                <div class="info-item">
                    <div class="info-label">Current Position</div>
                    <div class="info-value" id="scrollPosition">0</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Target Position</div>
                    <div class="info-value" id="targetPosition">0</div>
                </div>
                <div class="info-item">
                    <div class="info-label">Max Scroll</div>
                    <div class="info-value" id="maxScroll">0</div>
                </div>
            </div>

            <div class="scroll-section" id="scrollSection">
                <div class="scroll-content">
                    <div class="scroll-marker section-1">📍 Section 1 - Start</div>
                    <div class="scroll-marker section-2">🔵 Section 2 - Navigation</div>
                    <div class="scroll-marker section-3">🟣 Section 3 - Content</div>
                    <div class="scroll-marker section-4">🟡 Section 4 - Features</div>
                    <div class="scroll-marker scroll-target" id="scrollTarget">🎯 RANDOM TARGET SECTION</div>
                    <div class="scroll-marker section-5">🟤 Section 5 - More Content</div>
                    <div class="scroll-marker section-6">🔘 Section 6 - End</div>
                </div>
            </div>
        </div>

        <div class="test-section command-help">
            <h3>🎮 Available Test Commands</h3>
            <div class="command-list">
                <div class="command-item">
                    <strong><code>move</code></strong><br>
                    Move mouse to a random location on the page
                </div>
                <div class="command-item">
                    <strong><code>target</code></strong><br>
                    Move mouse to the draggable target element
                </div>
                <div class="command-item">
                    <strong><code>type &lt;text&gt;</code></strong><br>
                    Type text into the input field with human-like timing
                </div>
                <div class="command-item">
                    <strong><code>scroll</code></strong><br>
                    Scroll to the random target in the scrollable section
                </div>
                <div class="command-item">
                    <strong><code>exit</code></strong><br>
                    Close the browser and exit the test
                </div>
            </div>
        </div>
    </div>

    <!-- Mouse indicator -->
    <div class="mouse-indicator" id="mouseIndicator"></div>
    
    <!-- Coordinates display -->
    <div class="coordinates-display" id="coordinatesDisplay">
        Mouse: (0, 0) | Viewport: 0x0
    </div>

    <!-- Performance metrics -->
    <div class="performance-metrics" id="performanceMetrics">
        FPS: 0 | Memory: 0MB
    </div>

    <script>
        // Enhanced JavaScript functionality
        let isDragging = false;
        let currentX = 0;
        let currentY = 0;
        let initialX = 0;
        let initialY = 0;
        let xOffset = 0;
        let yOffset = 0;
        let inputCount = 0;
        let lastFrameTime = 0;
        let frameCount = 0;
        let fps = 0;

        // Get DOM elements
        const dragElement = document.getElementById('draggableTarget');
        const mouseIndicator = document.getElementById('mouseIndicator');
        const coordinatesDisplay = document.getElementById('coordinatesDisplay');
        const scrollSection = document.getElementById('scrollSection');
        const scrollTarget = document.getElementById('scrollTarget');
        const testInput = document.getElementById('testInput');
        
        // Status elements
        const mousePosElement = document.getElementById('mousePos');
        const scrollPosElement = document.getElementById('scrollPos');
        const targetPosElement = document.getElementById('targetPos');
        const inputCountElement = document.getElementById('inputCount');
        const performanceElement = document.getElementById('performanceMetrics');

        // Dragging functionality
        dragElement.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', dragMove);
        document.addEventListener('mouseup', dragEnd);

        function dragStart(e) {
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;
            
            if (e.target === dragElement) {
                isDragging = true;
                dragElement.style.cursor = 'grabbing';
            }
        }

        function dragMove(e) {
            if (isDragging) {
                e.preventDefault();
                
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;
                
                xOffset = currentX;
                yOffset = currentY;
                
                setTranslate(currentX, currentY, dragElement);
            }
        }

        function dragEnd(e) {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
            dragElement.style.cursor = 'move';
            // Remove any transition that might have been added
            dragElement.style.transition = 'none';
        }

        function setTranslate(xPos, yPos, el) {
            el.style.transform = `translate3d(${xPos}px, ${yPos}px, 0)`;
            el.style.transition = 'none';
        }

        // Mouse tracking - instant response with no lag
        document.addEventListener('mousemove', (e) => {
            // Remove any transitions for instant movement
            mouseIndicator.style.transition = 'none';
            mouseIndicator.style.left = e.clientX + 'px';
            mouseIndicator.style.top = e.clientY + 'px';
            
            coordinatesDisplay.textContent = 
                `Mouse: (${e.clientX}, ${e.clientY}) | Viewport: ${window.innerWidth}x${window.innerHeight}`;
            
            mousePosElement.textContent = `Mouse: (${e.clientX}, ${e.clientY})`;
        });

        // Scroll tracking
        scrollSection.addEventListener('scroll', () => {
            const scrollTop = scrollSection.scrollTop;
            scrollPosElement.textContent = `Scroll: ${scrollTop}px`;
            document.getElementById('scrollPosition').textContent = scrollTop;
        });

        // Input tracking
        testInput.addEventListener('input', () => {
            inputCount++;
            inputCountElement.textContent = `Inputs: ${inputCount}`;
        });

        // Random target positioning
        function setRandomTargetPosition() {
            const scrollContent = document.querySelector('.scroll-content');
            const contentHeight = scrollContent.offsetHeight;
            const targetHeight = scrollTarget.offsetHeight;
            
            // Place target randomly between 20% and 80% of content height
            const minPos = Math.floor(contentHeight * 0.2);
            const maxPos = Math.floor(contentHeight * 0.8 - targetHeight);
            const randomPos = Math.floor(Math.random() * (maxPos - minPos)) + minPos;
            
            // Position the target
            scrollTarget.style.position = 'absolute';
            scrollTarget.style.top = randomPos + 'px';
            scrollTarget.style.left = '20px';
            scrollTarget.style.right = '20px';
            
            // Update displays
            targetPosElement.textContent = `Target: ${randomPos}px`;
            document.getElementById('targetPosition').textContent = randomPos;
            
            return randomPos;
        }

        // Performance monitoring
        function updatePerformance() {
            const now = performance.now();
            frameCount++;
            
            if (now - lastFrameTime >= 1000) {
                fps = Math.round(frameCount * 1000 / (now - lastFrameTime));
                frameCount = 0;
                lastFrameTime = now;
                
                const memory = performance.memory ? 
                    Math.round(performance.memory.usedJSHeapSize / 1048576) : 0;
                
                performanceElement.textContent = `FPS: ${fps} | Memory: ${memory}MB`;
            }
            
            requestAnimationFrame(updatePerformance);
        }

        // Initialize
        window.addEventListener('load', () => {
            setRandomTargetPosition();
            updatePerformance();
            
            // Update max scroll
            const maxScrollValue = scrollSection.scrollHeight - scrollSection.clientHeight;
            document.getElementById('maxScroll').textContent = maxScrollValue;
            
            console.log('Enhanced test page loaded successfully!');
        });

        // Reset target position every 30 seconds
        setInterval(() => {
            setRandomTargetPosition();
            console.log('Target position randomized');
        }, 30000);

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey) {
                switch(e.key) {
                    case 'r':
                        e.preventDefault();
                        setRandomTargetPosition();
                        console.log('Target position reset via keyboard shortcut');
                        break;
                    case 'm':
                        e.preventDefault();
                        mouseIndicator.style.display = 
                            mouseIndicator.style.display === 'none' ? 'block' : 'none';
                        break;
                }
            }
        });
    </script>
</body>
</html>