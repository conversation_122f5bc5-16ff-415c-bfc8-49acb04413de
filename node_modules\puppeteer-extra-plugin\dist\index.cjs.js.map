{"version": 3, "file": "index.cjs.js", "sources": ["../src/index.ts"], "sourcesContent": ["import debug, { Debugger } from 'debug'\nimport * as Puppeteer from './puppeteer'\n\n/** @private */\nconst merge = require('merge-deep')\n\nexport interface PluginOptions {\n  [key: string]: any\n}\nexport interface PluginData {\n  name: {\n    [key: string]: any\n  }\n  value: {\n    [key: string]: any\n  }\n}\n\nexport type PluginDependencies = Set<string>\nexport type PluginRequirements = Set<\n  'launch' | 'headful' | 'dataFromPlugins' | 'runLast'\n>\n\n/**\n * Base class for `puppeteer-extra` plugins.\n *\n * Provides convenience methods to avoid boilerplate.\n *\n * All common `puppeteer` browser events will be bound to\n * the plugin instance, if a respectively named class member is found.\n *\n * Please refer to the [puppeteer API documentation](https://github.com/GoogleChrome/puppeteer/blob/master/docs/api.md) as well.\n *\n * @example\n * // hello-world-plugin.js\n * const { PuppeteerExtraPlugin } = require('puppeteer-extra-plugin')\n *\n * class Plugin extends PuppeteerExtraPlugin {\n *   constructor (opts = { }) { super(opts) }\n *\n *   get name () { return 'hello-world' }\n *\n *   async onPageCreated (page) {\n *     this.debug('page created', page.url())\n *     const ua = await page.browser().userAgent()\n *     this.debug('user agent', ua)\n *   }\n * }\n *\n * module.exports = function (pluginConfig) { return new Plugin(pluginConfig) }\n *\n *\n * // foo.js\n * const puppeteer = require('puppeteer-extra')\n * puppeteer.use(require('./hello-world-plugin')())\n *\n * ;(async () => {\n *   const browser = await puppeteer.launch({headless: false})\n *   const page = await browser.newPage()\n *   await page.goto('http://example.com', {waitUntil: 'domcontentloaded'})\n *   await browser.close()\n * })()\n *\n */\nexport abstract class PuppeteerExtraPlugin {\n  /** @private */\n  private _debugBase: Debugger\n  /** @private */\n  private _opts: PluginOptions\n  /** @private */\n  private _childClassMembers: string[]\n\n  constructor(opts?: PluginOptions) {\n    this._debugBase = debug(`puppeteer-extra-plugin:base:${this.name}`)\n    this._childClassMembers = []\n\n    this._opts = merge(this.defaults, opts || {})\n\n    this._debugBase('Initialized.')\n  }\n\n  /**\n   * Plugin name (required).\n   *\n   * Convention:\n   * - Package: `puppeteer-extra-plugin-anonymize-ua`\n   * - Name: `anonymize-ua`\n   *\n   * @example\n   * get name () { return 'anonymize-ua' }\n   */\n  get name(): string {\n    throw new Error('Plugin must override \"name\"')\n  }\n\n  /**\n   * Plugin defaults (optional).\n   *\n   * If defined will be ([deep-](https://github.com/jonschlinkert/merge-deep))merged with the (optional) user supplied options (supplied during plugin instantiation).\n   *\n   * The result of merging defaults with user supplied options can be accessed through `this.opts`.\n   *\n   * @see [[opts]]\n   *\n   * @example\n   * get defaults () {\n   *   return {\n   *     stripHeadless: true,\n   *     makeWindows: true,\n   *     customFn: null\n   *   }\n   * }\n   *\n   * // Users can overwrite plugin defaults during instantiation:\n   * puppeteer.use(require('puppeteer-extra-plugin-foobar')({ makeWindows: false }))\n   */\n  get defaults(): PluginOptions {\n    return {}\n  }\n\n  /**\n   * Plugin requirements (optional).\n   *\n   * Signal certain plugin requirements to the base class and the user.\n   *\n   * Currently supported:\n   * - `launch`\n   *   - If the plugin only supports locally created browser instances (no `puppeteer.connect()`),\n   *     will output a warning to the user.\n   * - `headful`\n   *   - If the plugin doesn't work in `headless: true` mode,\n   *     will output a warning to the user.\n   * - `dataFromPlugins`\n   *   - In case the plugin requires data from other plugins.\n   *     will enable usage of `this.getDataFromPlugins()`.\n   * - `runLast`\n   *   - In case the plugin prefers to run after the others.\n   *     Useful when the plugin needs data from others.\n   *\n   * @example\n   * get requirements () {\n   *   return new Set(['runLast', 'dataFromPlugins'])\n   * }\n   */\n  get requirements(): PluginRequirements {\n    return new Set([])\n  }\n\n  /**\n   * Plugin dependencies (optional).\n   *\n   * Missing plugins will be required() by puppeteer-extra.\n   *\n   * @example\n   * get dependencies () {\n   *   return new Set(['user-preferences'])\n   * }\n   * // Will ensure the 'puppeteer-extra-plugin-user-preferences' plugin is loaded.\n   */\n  get dependencies(): PluginDependencies {\n    return new Set([])\n  }\n\n  /**\n   * Plugin data (optional).\n   *\n   * Plugins can expose data (an array of objects), which in turn can be consumed by other plugins,\n   * that list the `dataFromPlugins` requirement (by using `this.getDataFromPlugins()`).\n   *\n   * Convention: `[ {name: 'Any name', value: 'Any value'} ]`\n   *\n   * @see [[getDataFromPlugins]]\n   *\n   * @example\n   * // plugin1.js\n   * get data () {\n   *   return [\n   *     {\n   *       name: 'userPreferences',\n   *       value: { foo: 'bar' }\n   *     },\n   *     {\n   *       name: 'userPreferences',\n   *       value: { hello: 'world' }\n   *     }\n   *   ]\n   *\n   * // plugin2.js\n   * get requirements () { return new Set(['dataFromPlugins']) }\n   *\n   * async beforeLaunch () {\n   *   const prefs = this.getDataFromPlugins('userPreferences').map(d => d.value)\n   *   this.debug(prefs) // => [ { foo: 'bar' }, { hello: 'world' } ]\n   * }\n   */\n  get data(): PluginData[] {\n    return []\n  }\n\n  /**\n   * Access the plugin options (usually the `defaults` merged with user defined options)\n   *\n   * To skip the auto-merging of defaults with user supplied opts don't define a `defaults`\n   * property and set the `this._opts` Object in your plugin constructor directly.\n   *\n   * @see [[defaults]]\n   *\n   * @example\n   * get defaults () { return { foo: \"bar\" } }\n   *\n   * async onPageCreated (page) {\n   *   this.debug(this.opts.foo) // => bar\n   * }\n   */\n  get opts(): PluginOptions {\n    return this._opts\n  }\n\n  /**\n   *  Convenience debug logger based on the [debug] module.\n   *  Will automatically namespace the logging output to the plugin package name.\n   *  [debug]: https://www.npmjs.com/package/debug\n   *\n   *  ```bash\n   *  # toggle output using environment variables\n   *  DEBUG=puppeteer-extra-plugin:<plugin_name> node foo.js\n   *  # to debug all the things:\n   *  DEBUG=puppeteer-extra,puppeteer-extra-plugin:* node foo.js\n   *  ```\n   *\n   * @example\n   * this.debug('hello world')\n   * // will output e.g. 'puppeteer-extra-plugin:anonymize-ua hello world'\n   */\n  get debug(): Debugger {\n    return debug(`puppeteer-extra-plugin:${this.name}`)\n  }\n\n  /**\n   * Before a new browser instance is created/launched.\n   *\n   * Can be used to modify the puppeteer launch options by modifying or returning them.\n   *\n   * Plugins using this method will be called in sequence to each\n   * be able to update the launch options.\n   *\n   * @example\n   * async beforeLaunch (options) {\n   *   if (this.opts.flashPluginPath) {\n   *     options.args.push(`--ppapi-flash-path=${this.opts.flashPluginPath}`)\n   *   }\n   * }\n   *\n   * @param options - Puppeteer launch options\n   */\n  async beforeLaunch(options: any) {\n    // noop\n  }\n\n  /**\n   * After the browser has launched.\n   *\n   * Note: Don't assume that there will only be a single browser instance during the lifecycle of a plugin.\n   * It's possible that `pupeeteer.launch` will be  called multiple times and more than one browser created.\n   * In order to make the plugins as stateless as possible don't store a reference to the browser instance\n   * in the plugin but rather consider alternatives.\n   *\n   * E.g. when using `onPageCreated` you can get a browser reference by using `page.browser()`.\n   *\n   * Alternatively you could expose a class method that takes a browser instance as a parameter to work with:\n   *\n   * ```es6\n   * const fancyPlugin = require('puppeteer-extra-plugin-fancy')()\n   * puppeteer.use(fancyPlugin)\n   * const browser = await puppeteer.launch()\n   * await fancyPlugin.killBrowser(browser)\n   * ```\n   *\n   * @param  browser - The `puppeteer` browser instance.\n   * @param  opts.options - Puppeteer launch options used.\n   *\n   * @example\n   * async afterLaunch (browser, opts) {\n   *   this.debug('browser has been launched', opts.options)\n   * }\n   */\n  async afterLaunch(\n    browser: Puppeteer.Browser,\n    opts = { options: {} as Puppeteer.LaunchOptions }\n  ) {\n    // noop\n  }\n\n  /**\n   * Before connecting to an existing browser instance.\n   *\n   * Can be used to modify the puppeteer connect options by modifying or returning them.\n   *\n   * Plugins using this method will be called in sequence to each\n   * be able to update the launch options.\n   *\n   * @param  {Object} options - Puppeteer connect options\n   * @return {Object=}\n   */\n  async beforeConnect(options: Puppeteer.ConnectOptions) {\n    // noop\n  }\n\n  /**\n   * After connecting to an existing browser instance.\n   *\n   * > Note: Don't assume that there will only be a single browser instance during the lifecycle of a plugin.\n   *\n   * @param browser - The `puppeteer` browser instance.\n   * @param  {Object} opts\n   * @param  {Object} opts.options - Puppeteer connect options used.\n   *\n   */\n  async afterConnect(browser: Puppeteer.Browser, opts = {}) {\n    // noop\n  }\n\n  /**\n   * Called when a browser instance is available.\n   *\n   * This applies to both `puppeteer.launch()` and `puppeteer.connect()`.\n   *\n   * Convenience method created for plugins that need access to a browser instance\n   * and don't mind if it has been created through `launch` or `connect`.\n   *\n   * > Note: Don't assume that there will only be a single browser instance during the lifecycle of a plugin.\n   *\n   * @param browser - The `puppeteer` browser instance.\n   */\n  public async onBrowser(browser: Puppeteer.Browser, opts: any): Promise<void> {\n    // noop\n  }\n\n  /**\n   * Called when a target is created, for example when a new page is opened by window.open or browser.newPage.\n   *\n   * > Note: This includes target creations in incognito browser contexts.\n   *\n   * > Note: This includes browser instances created through `.launch()` as well as `.connect()`.\n   *\n   * @param  {Puppeteer.Target} target\n   */\n  async onTargetCreated(target: Puppeteer.Target) {\n    // noop\n  }\n\n  /**\n   * Same as `onTargetCreated` but prefiltered to only contain Pages, for convenience.\n   *\n   * > Note: This includes page creations in incognito browser contexts.\n   *\n   * > Note: This includes browser instances created through `.launch()` as well as `.connect()`.\n   *\n   * @param  {Puppeteer.Target} target\n   *\n   * @example\n   * async onPageCreated (page) {\n   *   let ua = await page.browser().userAgent()\n   *   if (this.opts.stripHeadless) {\n   *     ua = ua.replace('HeadlessChrome/', 'Chrome/')\n   *   }\n   *   this.debug('new ua', ua)\n   *   await page.setUserAgent(ua)\n   * }\n   */\n  async onPageCreated(page: Puppeteer.Page) {\n    // noop\n  }\n\n  /**\n   * Called when the url of a target changes.\n   *\n   * > Note: This includes target changes in incognito browser contexts.\n   *\n   * > Note: This includes browser instances created through `.launch()` as well as `.connect()`.\n   *\n   * @param  {Puppeteer.Target} target\n   */\n  async onTargetChanged(target: Puppeteer.Target) {\n    // noop\n  }\n\n  /**\n   * Called when a target is destroyed, for example when a page is closed.\n   *\n   * > Note: This includes target destructions in incognito browser contexts.\n   *\n   * > Note: This includes browser instances created through `.launch()` as well as `.connect()`.\n   *\n   * @param  {Puppeteer.Target} target\n   */\n  async onTargetDestroyed(target: Puppeteer.Target) {\n    // noop\n  }\n\n  /**\n   * Called when Puppeteer gets disconnected from the Chromium instance.\n   *\n   * This might happen because of one of the following:\n   * - Chromium is closed or crashed\n   * - The `browser.disconnect` method was called\n   */\n  async onDisconnected() {\n    // noop\n  }\n\n  /**\n   * **Deprecated:** Since puppeteer v1.6.0 `onDisconnected` has been improved\n   * and should be used instead of `onClose`.\n   *\n   * In puppeteer < v1.6.0 `onDisconnected` was not catching all exit scenarios.\n   * In order for plugins to clean up properly (e.g. deleting temporary files)\n   * the `onClose` method had been introduced.\n   *\n   * > Note: Might be called multiple times on exit.\n   *\n   * > Note: This only includes browser instances created through `.launch()`.\n   */\n  async onClose() {\n    // noop\n  }\n\n  /**\n   * After the plugin has been registered in `puppeteer-extra`.\n   *\n   * Normally right after `puppeteer.use(plugin)` is called\n   */\n  async onPluginRegistered() {\n    // noop\n  }\n\n  /**\n   * Helper method to retrieve `data` objects from other plugins.\n   *\n   * A plugin needs to state the `dataFromPlugins` requirement\n   * in order to use this method. Will be mapped to `puppeteer.getPluginData`.\n   *\n   * @param name - Filter data by `name` property\n   *\n   * @see [data]\n   * @see [requirements]\n   */\n  getDataFromPlugins(name?: string): PluginData[] {\n    return []\n  }\n\n  /**\n   * Will match plugin dependencies against all currently registered plugins.\n   * Is being called by `puppeteer-extra` and used to require missing dependencies.\n   *\n   * @param  {Array<Object>} plugins\n   * @return {Set} - list of missing plugin names\n   *\n   * @private\n   */\n  _getMissingDependencies(plugins: any) {\n    const pluginNames = new Set(plugins.map((p: any) => p.name))\n    const missing = new Set(\n      Array.from(this.dependencies.values()).filter(x => !pluginNames.has(x))\n    )\n    return missing\n  }\n\n  /**\n   * Conditionally bind browser/process events to class members.\n   * The idea is to reduce event binding boilerplate in plugins.\n   *\n   * For efficiency we make sure the plugin is using the respective event\n   * by checking the child class members before registering the listener.\n   *\n   * @param  {<Puppeteer.Browser>} browser\n   * @param  {Object} opts - Options\n   * @param  {string} opts.context - Puppeteer context (launch/connect)\n   * @param  {Object} [opts.options] - Puppeteer launch or connect options\n   * @param  {Array<string>} [opts.defaultArgs] - The default flags that Chromium will be launched with\n   *\n   * @private\n   */\n  async _bindBrowserEvents(browser: Puppeteer.Browser, opts: any = {}) {\n    if (\n      this._hasChildClassMember('onTargetCreated') ||\n      this._hasChildClassMember('onPageCreated')\n    ) {\n      browser.on('targetcreated', this._onTargetCreated.bind(this))\n    }\n    if (this._hasChildClassMember('onTargetChanged') && this.onTargetChanged) {\n      browser.on('targetchanged', this.onTargetChanged.bind(this))\n    }\n    if (\n      this._hasChildClassMember('onTargetDestroyed') &&\n      this.onTargetDestroyed\n    ) {\n      browser.on('targetdestroyed', this.onTargetDestroyed.bind(this))\n    }\n    if (this._hasChildClassMember('onDisconnected') && this.onDisconnected) {\n      browser.on('disconnected', this.onDisconnected.bind(this))\n    }\n    if (opts.context === 'launch' && this._hasChildClassMember('onClose')) {\n      // The disconnect event has been improved since puppeteer v1.6.0\n      // onClose is being kept mostly for legacy reasons\n      if (this.onClose) {\n        process.on('exit', this.onClose.bind(this))\n        browser.on('disconnected', this.onClose.bind(this))\n\n        if (opts.options.handleSIGINT !== false) {\n          process.on('SIGINT', this.onClose.bind(this))\n        }\n        if (opts.options.handleSIGTERM !== false) {\n          process.on('SIGTERM', this.onClose.bind(this))\n        }\n        if (opts.options.handleSIGHUP !== false) {\n          process.on('SIGHUP', this.onClose.bind(this))\n        }\n      }\n    }\n    if (opts.context === 'launch' && this.afterLaunch) {\n      await this.afterLaunch(browser, opts)\n    }\n    if (opts.context === 'connect' && this.afterConnect) {\n      await this.afterConnect(browser, opts)\n    }\n    if (this.onBrowser) await this.onBrowser(browser, opts)\n  }\n\n  /**\n   * @private\n   */\n  async _onTargetCreated(target: Puppeteer.Target) {\n    if (this.onTargetCreated) await this.onTargetCreated(target)\n    // Pre filter pages for plugin developers convenience\n    if (target.type() === 'page') {\n      try {\n        const page = await target.page()\n        if (!page) {\n          return\n        }\n        const validPage = 'isClosed' in page && !page.isClosed()\n        if (this.onPageCreated && validPage) {\n          await this.onPageCreated(page)\n        }\n      } catch (err) {\n        console.error(err)\n      }\n    }\n  }\n\n  /**\n   * @private\n   */\n  _register(prototype: any) {\n    this._registerChildClassMembers(prototype)\n    if (this.onPluginRegistered) this.onPluginRegistered()\n  }\n\n  /**\n   * @private\n   */\n  _registerChildClassMembers(prototype: any) {\n    this._childClassMembers = Object.getOwnPropertyNames(prototype)\n  }\n\n  /**\n   * @private\n   */\n  _hasChildClassMember(name: string) {\n    return !!this._childClassMembers.includes(name)\n  }\n\n  /**\n   * @private\n   */\n  get _isPuppeteerExtraPlugin() {\n    return true\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA,MAAM,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC,CAAA;AAmBnC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAyCsB,oBAAoB;IAQxC,YAAY,IAAoB;QAC9B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,+BAA+B,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;QACnE,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAA;QAE5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,IAAI,EAAE,CAAC,CAAA;QAE7C,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAA;KAChC;;;;;;;;;;;IAYD,IAAI,IAAI;QACN,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAA;KAC/C;;;;;;;;;;;;;;;;;;;;;;IAuBD,IAAI,QAAQ;QACV,OAAO,EAAE,CAAA;KACV;;;;;;;;;;;;;;;;;;;;;;;;;IA0BD,IAAI,YAAY;QACd,OAAO,IAAI,GAAG,CAAC,EAAE,CAAC,CAAA;KACnB;;;;;;;;;;;;IAaD,IAAI,YAAY;QACd,OAAO,IAAI,GAAG,CAAC,EAAE,CAAC,CAAA;KACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkCD,IAAI,IAAI;QACN,OAAO,EAAE,CAAA;KACV;;;;;;;;;;;;;;;;IAiBD,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAA;KAClB;;;;;;;;;;;;;;;;;IAkBD,IAAI,KAAK;QACP,OAAO,KAAK,CAAC,0BAA0B,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;KACpD;;;;;;;;;;;;;;;;;;IAmBD,MAAM,YAAY,CAAC,OAAY;;KAE9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA6BD,MAAM,WAAW,CACf,OAA0B,EAC1B,OAAO,EAAE,OAAO,EAAE,EAA6B,EAAE;;KAGlD;;;;;;;;;;;;IAaD,MAAM,aAAa,CAAC,OAAiC;;KAEpD;;;;;;;;;;;IAYD,MAAM,YAAY,CAAC,OAA0B,EAAE,IAAI,GAAG,EAAE;;KAEvD;;;;;;;;;;;;;IAcM,MAAM,SAAS,CAAC,OAA0B,EAAE,IAAS;;KAE3D;;;;;;;;;;IAWD,MAAM,eAAe,CAAC,MAAwB;;KAE7C;;;;;;;;;;;;;;;;;;;;IAqBD,MAAM,aAAa,CAAC,IAAoB;;KAEvC;;;;;;;;;;IAWD,MAAM,eAAe,CAAC,MAAwB;;KAE7C;;;;;;;;;;IAWD,MAAM,iBAAiB,CAAC,MAAwB;;KAE/C;;;;;;;;IASD,MAAM,cAAc;;KAEnB;;;;;;;;;;;;;IAcD,MAAM,OAAO;;KAEZ;;;;;;IAOD,MAAM,kBAAkB;;KAEvB;;;;;;;;;;;;IAaD,kBAAkB,CAAC,IAAa;QAC9B,OAAO,EAAE,CAAA;KACV;;;;;;;;;;IAWD,uBAAuB,CAAC,OAAY;QAClC,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAM,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;QAC5D,MAAM,OAAO,GAAG,IAAI,GAAG,CACrB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACxE,CAAA;QACD,OAAO,OAAO,CAAA;KACf;;;;;;;;;;;;;;;;IAiBD,MAAM,kBAAkB,CAAC,OAA0B,EAAE,OAAY,EAAE;QACjE,IACE,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC;YAC5C,IAAI,CAAC,oBAAoB,CAAC,eAAe,CAAC,EAC1C;YACA,OAAO,CAAC,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;SAC9D;QACD,IAAI,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,CAAC,IAAI,IAAI,CAAC,eAAe,EAAE;YACxE,OAAO,CAAC,EAAE,CAAC,eAAe,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;SAC7D;QACD,IACE,IAAI,CAAC,oBAAoB,CAAC,mBAAmB,CAAC;YAC9C,IAAI,CAAC,iBAAiB,EACtB;YACA,OAAO,CAAC,EAAE,CAAC,iBAAiB,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;SACjE;QACD,IAAI,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE;YACtE,OAAO,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;SAC3D;QACD,IAAI,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,IAAI,CAAC,oBAAoB,CAAC,SAAS,CAAC,EAAE;;;YAGrE,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBAC3C,OAAO,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;gBAEnD,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,KAAK,KAAK,EAAE;oBACvC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;iBAC9C;gBACD,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,KAAK,KAAK,EAAE;oBACxC,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;iBAC/C;gBACD,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,KAAK,KAAK,EAAE;oBACvC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;iBAC9C;aACF;SACF;QACD,IAAI,IAAI,CAAC,OAAO,KAAK,QAAQ,IAAI,IAAI,CAAC,WAAW,EAAE;YACjD,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;SACtC;QACD,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,IAAI,IAAI,CAAC,YAAY,EAAE;YACnD,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;SACvC;QACD,IAAI,IAAI,CAAC,SAAS;YAAE,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;KACxD;;;;IAKD,MAAM,gBAAgB,CAAC,MAAwB;QAC7C,IAAI,IAAI,CAAC,eAAe;YAAE,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,CAAA;;QAE5D,IAAI,MAAM,CAAC,IAAI,EAAE,KAAK,MAAM,EAAE;YAC5B,IAAI;gBACF,MAAM,IAAI,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAA;gBAChC,IAAI,CAAC,IAAI,EAAE;oBACT,OAAM;iBACP;gBACD,MAAM,SAAS,GAAG,UAAU,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAA;gBACxD,IAAI,IAAI,CAAC,aAAa,IAAI,SAAS,EAAE;oBACnC,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA;iBAC/B;aACF;YAAC,OAAO,GAAG,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;aACnB;SACF;KACF;;;;IAKD,SAAS,CAAC,SAAc;QACtB,IAAI,CAAC,0BAA0B,CAAC,SAAS,CAAC,CAAA;QAC1C,IAAI,IAAI,CAAC,kBAAkB;YAAE,IAAI,CAAC,kBAAkB,EAAE,CAAA;KACvD;;;;IAKD,0BAA0B,CAAC,SAAc;QACvC,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,CAAA;KAChE;;;;IAKD,oBAAoB,CAAC,IAAY;QAC/B,OAAO,CAAC,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;KAChD;;;;IAKD,IAAI,uBAAuB;QACzB,OAAO,IAAI,CAAA;KACZ;;;;;"}