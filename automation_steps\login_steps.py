import asyncio
import random
from .base_step import BaseStep

class EnterEmailStep(BaseStep):
    async def execute(self):
        """<PERSON>les entering the email into the input field."""
        print("Step: Entering email...")
        email_input_element = await self.browser_actions.send_keys("input[type='email'][id='usernameEntry']", self.email, timeout=30)
        if email_input_element:
            print("<PERSON><PERSON> entered successfully.")
            return True
        else:
            page_html = await self.tab.get_content()
            await self.browser_actions.log_error(self.email, "Email input field not found or typing failed during login.", page_html)
            print("Failed to find email input field or type.")
            return False

class ClickNextEmailStep(BaseStep):
    async def execute(self):
        """Handles clicking the 'Next' button after entering the email."""
        print("Step: Clicking Next (email)...")
        next_button_email = await self.browser_actions.click("button[type='submit'][data-testid='primaryButton']", timeout=10)
        if next_button_email:
            return True
        else:
            page_html = await self.tab.get_content()
            await self.browser_actions.log_error(self.email, "Next button (email) not found or clickable during login.", page_html)
            print("Failed to find or click Next (email) button.")
            return False

class HandleCodePageStep(BaseStep):
    async def execute(self):
        """Detects the 'Enter your code' form and clicks 'Use your password' link if present."""
        print("Step: Checking for 'Enter your code' form...")
        handled = await self.browser_actions.handle_code_entry_form()
        if handled:
            print("'Enter your code' form handled by clicking 'Use your password' link.")
        else:
            print("'Enter your code' form not detected or 'Use your password' link not found. Proceeding...")
        return True # Always return True as this step is for conditional handling, not a mandatory failure point

class EnterPasswordStep(BaseStep):
    async def execute(self):
        """Handles typing the password."""
        print("Step: Entering password...")
        password_input_element = await self.browser_actions.send_keys("input[type='password'][id='passwordEntry']", self.password, timeout=30)
        if password_input_element:
            print("Password entered successfully.")
            return True
        else:
            page_html = await self.tab.get_content()
            await self.browser_actions.log_error(self.email, "Password input field not found or typing failed during login.", page_html)
            print("Failed to find password input field or type.")
            return False

class ClickNextPasswordStep(BaseStep):
    async def execute(self):
        """Handles clicking the 'Next' button after entering the password."""
        print("Step: Clicking Next (password)...")
        next_button_password = await self.browser_actions.click("button[type='submit'][data-testid='primaryButton']", timeout=10)
        if next_button_password:
            # Wait for the URL to change away from the login page after clicking Next
            if not await self.browser_actions.wait_for_url_change_or_target(self.login_url_check, self.rewards_url_check, timeout=60):
                 page_html = await self.tab.get_content()
                 await self.browser_actions.log_error(self.email, "Failed to navigate away from login page after password Next click.", page_html)
                 print("Failed to navigate away from login page after password Next click.")
                 return False
            print("Navigation away from login page detected.")
            return True
        else:
            page_html = await self.tab.get_content()
            await self.browser_actions.log_error(self.email, "Next button (password) not found or clickable during login.", page_html)
            print("Failed to find or click Next (password) button.")
            return False

class HandleStaySignedInStep(BaseStep):
    async def execute(self):
        """Handles the 'Stay signed in?' prompt and clicks 'Yes'."""
        print("Step: Handling 'Stay signed in?' page...")
        # Use the new wait_delay_and_click method for the Yes button
        yes_button = await self.browser_actions.click("button[type='submit'][data-testid='primaryButton']", timeout=30)

        if yes_button:
            # Wait for navigation after clicking Yes
            if not await self.browser_actions.wait_for_url_change_or_target(self.login_url_check, self.rewards_url_check, timeout=60):
                page_html = await self.tab.get_content()
                await self.browser_actions.log_error(self.email, "Failed to navigate after 'Stay signed in?' click.", page_html)
                print("Failed to navigate after clicking 'Yes'.")
                return False
            print("Navigation after 'Yes' click successful.")
            return True
        else:
            page_html = await self.tab.get_content()
            await self.browser_actions.log_error(self.email, "Yes button not found or clickable on 'Stay signed in?' page within timeout.", page_html)
            print("Failed to find or click 'Yes' button on 'Stay signed in?' page.")
            return False