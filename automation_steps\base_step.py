import asyncio
import random
import nodriver as uc
from abc import ABC, abstractmethod
import urllib.parse
import os
import hashlib
from src.utils import log_error # Assuming utils is one level up
from src.browser_actions import BrowserActions # Import BrowserActions

class BaseStep(ABC):
    def __init__(self, bot):
        self.bot = bot
        self.tab = bot.tab # Access the browser tab from the bot instance
        self.email = bot.email
        self.password = bot.password
        self.login_url_check = bot.login_url_check
        self.rewards_url_check = bot.rewards_url_check
        self.browser_actions = bot.stealth_browser.browser_actions # Access BrowserActions from stealth_browser

    @abstractmethod
    async def execute(self):
        """
        Abstract method to execute the step's logic.
        Must be implemented by subclasses.
        """
        pass

    # Removed utility methods: _human_like_send_keys, _human_like_click, _wait_for_url_change_or_target, log_error
    # Subclasses should now use self.browser_actions.human_like_send_keys, self.browser_actions.human_like_click, etc.